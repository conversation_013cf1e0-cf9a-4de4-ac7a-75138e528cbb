<UserControl x:Class="XIAOFUTools.Tools.FeatureToTxt.FeatureToTxtDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.FeatureToTxt"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="400"
             Loaded="UserControl_Loaded">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <!-- 布尔值转可见性转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <!-- 字段名称转颜色转换器 -->
            <local:FieldNameToColorConverter x:Key="FieldNameToColorConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部固定区域：基本设置 -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="6,6,6,4">
            <!-- 要素图层选择 -->
            <GroupBox Header="基本设置" Style="{StaticResource GroupBoxStyle}">
                <Grid Margin="4">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 要素图层 -->
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="要素图层(面):"
                             Style="{StaticResource FormLabelStyle}" Margin="0,0,6,4"/>
                    <ComboBox Grid.Row="0" Grid.Column="1" Margin="0,0,3,4"
                            ItemsSource="{Binding PolygonLayers}"
                            SelectedItem="{Binding SelectedPolygonLayer}"
                            DisplayMemberPath="Name"
                            Style="{StaticResource ComboBoxStyle}"/>
                    <Button Grid.Row="0" Grid.Column="2" Content="刷新" Margin="0,0,0,4"
                          Command="{Binding RefreshLayersCommand}"
                          Style="{StaticResource DefaultButtonStyle}"/>

                    <!-- 输出文件夹 -->
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="输出文件夹:"
                             Style="{StaticResource FormLabelStyle}" Margin="0,0,6,0"/>
                    <TextBox Grid.Row="1" Grid.Column="1" Margin="0,0,3,0"
                           Text="{Binding OutputPath}"
                           Style="{StaticResource TextBoxStyle}"/>
                    <Button Grid.Row="1" Grid.Column="2" Content="浏览"
                          Command="{Binding BrowseOutputPathCommand}"
                          Style="{StaticResource DefaultButtonStyle}"/>
                </Grid>
            </GroupBox>
        </Border>

        <!-- 中间滚动区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Padding="8,6,8,6">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 输出字段配置 -->
                <GroupBox Grid.Row="0" Header="输出字段配置" Margin="0,0,0,8" Style="{StaticResource GroupBoxStyle}">
                <Grid Margin="6">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 说明文本 -->
                    <TextBlock Grid.Row="0" Text="所有字段块都可拖拽调整顺序，点击X删除字段，右键添加字段。"
                             Style="{StaticResource InfoTextBlockStyle}" Margin="0,0,0,6" TextWrapping="Wrap"/>

                    <!-- 字段配置区域 -->
                    <Border Grid.Row="1" BorderBrush="#E0E0E0" BorderThickness="1"
                          Background="White" MinHeight="80">
                        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                            <ListBox ItemsSource="{Binding OutputFields}" Margin="4"
                                   Background="Transparent" BorderThickness="0"
                                   AllowDrop="True"
                                   PreviewMouseLeftButtonDown="ListBox_PreviewMouseLeftButtonDown"
                                   Drop="ListBox_Drop"
                                   DragEnter="ListBox_DragEnter"
                                   DragLeave="ListBox_DragLeave"
                                   DragOver="ListBox_DragOver">
                                <ListBox.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <WrapPanel Orientation="Horizontal"/>
                                    </ItemsPanelTemplate>
                                </ListBox.ItemsPanel>
                                <ListBox.ItemContainerStyle>
                                    <Style TargetType="ListBoxItem">
                                        <Setter Property="Padding" Value="0"/>
                                        <Setter Property="Margin" Value="0"/>
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="ListBoxItem">
                                                    <ContentPresenter/>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ListBox.ItemContainerStyle>
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="{Binding FieldName, Converter={StaticResource FieldNameToColorConverter}}"
                                              BorderBrush="#B0C4DE" BorderThickness="1"
                                              CornerRadius="2" Margin="1" Padding="4,2"
                                              AllowDrop="True" Cursor="Hand"
                                              MouseEnter="Border_MouseEnter"
                                              MouseLeave="Border_MouseLeave">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- 字段名称 -->
                                                <TextBlock Grid.Column="0" Text="{Binding FieldName}"
                                                         FontWeight="Bold" FontSize="11" Margin="0,0,2,0"
                                                         VerticalAlignment="Center"/>

                                                <!-- 删除按钮 -->
                                                <Button Grid.Column="1" Content="×" Width="12" Height="12"
                                                      FontSize="8" Padding="0" Margin="0" FontWeight="Bold"
                                                      Background="Red" Foreground="White" BorderThickness="0"
                                                      Command="{Binding DataContext.RemoveOutputFieldCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                      CommandParameter="{Binding}"
                                                      Visibility="{Binding CanDelete, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                      ToolTip="删除此字段"
                                                      VerticalAlignment="Center"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>

                                <!-- 右键菜单 -->
                                <ListBox.ContextMenu>
                                    <ContextMenu ItemsSource="{Binding AvailableFields}">
                                        <ContextMenu.ItemContainerStyle>
                                            <Style TargetType="MenuItem">
                                                <Setter Property="Header" Value="{Binding}"/>
                                                <Setter Property="Command" Value="{Binding DataContext.AddOutputFieldCommand, RelativeSource={RelativeSource AncestorType=ContextMenu}}"/>
                                                <Setter Property="CommandParameter" Value="{Binding}"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding}" Value="---实际字段---">
                                                        <Setter Property="IsEnabled" Value="False"/>
                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                        <Setter Property="Foreground" Value="Gray"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ContextMenu.ItemContainerStyle>
                                        <ContextMenu.ItemTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding}" FontSize="12"/>
                                            </DataTemplate>
                                        </ContextMenu.ItemTemplate>
                                    </ContextMenu>
                                </ListBox.ContextMenu>
                            </ListBox>
                        </ScrollViewer>
                    </Border>
                </Grid>
            </GroupBox>

                <!-- 选项设置 -->
                <GroupBox Grid.Row="1" Header="选项设置" Margin="0,0,0,8" Style="{StaticResource GroupBoxStyle}">
                <Grid Margin="6">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左列：复选框选项 -->
                    <StackPanel Grid.Row="0" Grid.Column="0" Grid.RowSpan="3" Margin="0,0,8,0">
                        <CheckBox Content="是否输出闭合点" Margin="0,0,0,3"
                                IsChecked="{Binding OutputClosingPoint}"
                                Style="{StaticResource CheckBoxStyle}"/>
                        <CheckBox Content="内环1起编" Margin="0,0,0,3"
                                IsChecked="{Binding InnerRingStartFromOne}"
                                Style="{StaticResource CheckBoxStyle}"/>
                        <CheckBox Content="是否闭合点续编" Margin="0,0,0,3"
                                IsChecked="{Binding ClosingPointContinueNumbering}"
                                Style="{StaticResource CheckBoxStyle}"/>
                        <CheckBox Content="分块导出" Margin="0,0,0,3"
                                IsChecked="{Binding ExportSeparately}"
                                Style="{StaticResource CheckBoxStyle}"/>
                        <CheckBox Content="XY互换" Margin="0,0,0,0"
                                IsChecked="{Binding SwapXY}"
                                Style="{StaticResource CheckBoxStyle}"/>
                    </StackPanel>

                    <!-- 右列：格式设置 -->
                    <Grid Grid.Row="0" Grid.Column="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="前缀:"
                                 Style="{StaticResource FormLabelStyle}" Margin="0,0,6,3"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Margin="0,0,0,3"
                               Text="{Binding Prefix}"
                               Style="{StaticResource TextBoxStyle}"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="小数位数:"
                                 Style="{StaticResource FormLabelStyle}" Margin="0,0,6,0"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Margin="0,0,0,0"
                               Text="{Binding DecimalPlaces}"
                               Style="{StaticResource TextBoxStyle}"/>
                    </Grid>
                </Grid>
            </GroupBox>

                <!-- 进度条 -->
                <ProgressBar Grid.Row="3" Margin="0,0,0,6"
                           Value="{Binding Progress}"
                           Minimum="0" Maximum="100"
                           IsIndeterminate="{Binding IsProgressIndeterminate}"
                           Style="{StaticResource ProgressBarStyle}"/>

                <!-- 日志窗口 -->
                <Border Grid.Row="4" BorderBrush="#CDCDCD" BorderThickness="1" Margin="0,0,0,6" Height="140">
                    <TextBox Text="{Binding LogContent, Mode=OneWay}"
                           Style="{StaticResource LogTextBoxStyle}"/>
                </Border>

                <!-- 状态消息 -->
                <TextBlock Grid.Row="5" Text="{Binding StatusMessage}"
                         Style="{StaticResource StatusTextStyle}"
                         TextWrapping="Wrap" VerticalAlignment="Center" Margin="0,0,0,0"/>
            </Grid>
        </ScrollViewer>

        <!-- 底部固定区域：按钮区域 -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0"
               BorderThickness="0,1,0,0"
               Padding="8,6,8,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 帮助按钮（左下角） -->
                <Button Grid.Column="0" Content="?" VerticalAlignment="Center" Margin="0,0,8,0"
                        Command="{Binding ShowHelpCommand}"
                        Style="{StaticResource HelpButtonStyle}"
                        ToolTip="显示帮助信息"/>

                <!-- 文本格式选择 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="文本格式:" Style="{StaticResource FormLabelStyle}" Margin="0,0,4,0"/>
                    <ComboBox Width="90" ItemsSource="{Binding TextFormats}"
                            SelectedItem="{Binding SelectedTextFormat}"
                            Style="{StaticResource ComboBoxStyle}"
                            ToolTip="选择输出文件的文本编码格式"/>
                </StackPanel>

                <!-- 按钮组 -->
                <StackPanel Grid.Column="3" Orientation="Horizontal" HorizontalAlignment="Right">
                    <!-- 只在处理时显示停止按钮 -->
                    <Button Content="停止" Command="{Binding CancelCommand}" Margin="0,0,6,0"
                            Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}"
                            IsEnabled="{Binding IsProcessing}"
                            Style="{StaticResource CancelButtonStyle}"/>
                    <Button Content="生成TXT" Command="{Binding RunCommand}"
                            IsEnabled="{Binding CanProcess}"
                            Style="{StaticResource ExecuteButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
