<ResourceDictionary 
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ArcGIS Pro风格颜色资源 -->
    <Color x:Key="PrimaryColor">#005E95</Color>
    <Color x:Key="PrimaryLightColor">#0079C1</Color>
    <Color x:Key="PrimaryDarkColor">#004875</Color>
    <Color x:Key="AccentColor">#69DCFF</Color>
    <Color x:Key="TextPrimaryColor">#242424</Color>
    <Color x:Key="TextSecondaryColor">#6E6E6E</Color>
    <Color x:Key="DividerColor">#E0E0E0</Color>
    <Color x:Key="BackgroundColor">#F8F8F8</Color>
    <Color x:Key="SurfaceColor">#FFFFFF</Color>
    <Color x:Key="ErrorColor">#DE2900</Color>
    
    <!-- 画刷资源 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource DividerColor}"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    
    <!-- 全局字体设置 -->
    <Style TargetType="{x:Type Control}">
        <Setter Property="FontFamily" Value="Segoe UI, Microsoft YaHei UI"/>
        <Setter Property="FontSize" Value="12"/>
    </Style>
    
    <!-- 标题样式 -->
    <Style x:Key="HeaderTextBlockStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
    </Style>
    
    <!-- 标签样式 -->
    <Style x:Key="LabelTextBlockStyle" TargetType="TextBlock">
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,5"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
    </Style>
    
    <!-- ComboBoxItem样式 -->
    <Style x:Key="ComboBoxItemStyle" TargetType="ComboBoxItem">
        <Setter Property="Padding" Value="2,2"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBoxItem">
                    <Border Name="Bd" 
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}"
                            SnapsToDevicePixels="true">
                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsHighlighted" Value="true">
                            <Setter TargetName="Bd" Property="Background" Value="#E8F1FB"/>
                            <Setter TargetName="Bd" Property="BorderBrush" Value="#D9D9D9"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 添加一个用于ComboBox的ToggleButton模板 -->
    <ControlTemplate x:Key="ComboBoxToggleButtonTemplate" TargetType="ToggleButton">
        <Border Background="Transparent" Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=ComboBox}}" 
                Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=ComboBox}}"/>
    </ControlTemplate>
    
    <!-- 下拉菜单样式 -->
    <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Margin" Value="0,3,0,6"/>
        <Setter Property="Padding" Value="8,2"/>
        <Setter Property="BorderBrush" Value="#ADADAD"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Height" Value="22"/>
        <Setter Property="ItemContainerStyle" Value="{StaticResource ComboBoxItemStyle}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <Border Name="PART_Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <ContentPresenter Grid.Column="0"
                                                  Name="PART_ContentPresenter"
                                                  Margin="{TemplateBinding Padding}"
                                                  Content="{TemplateBinding SelectionBoxItem}"
                                                  ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                  VerticalAlignment="Center"
                                                  HorizontalAlignment="Left"/>
                                <Path Grid.Column="1"
                                      Name="DropDownGlyph"
                                      Width="8" Height="5"
                                      Data="M 0,0 L 8,0 L 4,5 Z"
                                      Fill="{StaticResource TextPrimaryBrush}"
                                      Margin="0,0,8,0"
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Center"/>
                            </Grid>
                        </Border>
                        <Popup Name="PART_Popup"
                               IsOpen="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                               Placement="Bottom"
                               Focusable="False"
                               AllowsTransparency="True"
                               PopupAnimation="Slide">
                            <Border Background="{StaticResource SurfaceBrush}"
                                    BorderBrush="{StaticResource DividerBrush}"
                                    BorderThickness="1"
                                    MaxHeight="{TemplateBinding MaxDropDownHeight}"
                                    MinWidth="{Binding ActualWidth, ElementName=PART_Border}"
                                    CornerRadius="4">
                                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled"
                                              Margin="0,0,0,0">
                                    <ItemsPresenter KeyboardNavigation.DirectionalNavigation="Contained" />
                                </ScrollViewer>
                            </Border>
                        </Popup>
                        <ToggleButton Grid.Column="0"
                                      Name="ToggleButton"
                                      Focusable="False"
                                      IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                      ClickMode="Press"
                                      Background="Transparent"
                                      BorderThickness="0"
                                      Template="{StaticResource ComboBoxToggleButtonTemplate}"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsKeyboardFocusWithin" Value="True">
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsDropDownOpen" Value="True">
                            <Setter TargetName="DropDownGlyph" Property="Data" Value="M 0,5 L 8,5 L 4,0 Z"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 文本框样式 -->
    <Style x:Key="TextBoxStyle" TargetType="TextBox">
        <Setter Property="Margin" Value="0,3,0,6"/>
        <Setter Property="Padding" Value="2,2"/>
        <Setter Property="BorderBrush" Value="#ADADAD"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Height" Value="22"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ScrollViewer x:Name="PART_ContentHost" 
                                      Margin="{TemplateBinding Padding}"
                                      VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 复选框样式 -->
    <Style x:Key="CheckBoxStyle" TargetType="CheckBox">
        <Setter Property="Margin" Value="0,5"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>
    
    <!-- 按钮样式：主按钮 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="4,2"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Width" Value="70"/>
        <Setter Property="Height" Value="22"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center" 
                            VerticalAlignment="Center" 
                            Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 按钮样式：次要按钮 -->
    <Style x:Key="TextButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="4,2"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Height" Value="22"/>
        <Setter Property="Width" Value="60"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center" 
                                          VerticalAlignment="Center" 
                                          Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#F0F0F0"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#E5E5E5"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 按钮样式：运行按钮 -->
    <Style x:Key="ExecuteButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#0079C1"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="4,2"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Width" Value="60"/>
        <Setter Property="Height" Value="22"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center" 
                                          VerticalAlignment="Center" 
                                          Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#00A0FF"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#0060A0"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 按钮样式：取消按钮 -->
    <Style x:Key="CancelButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#E6E6E6"/>
        <Setter Property="Foreground" Value="#323232"/>
        <Setter Property="Padding" Value="4,2"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#ADADAD"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Width" Value="60"/>
        <Setter Property="Height" Value="22"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center" 
                                          VerticalAlignment="Center" 
                                          Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#F0F0F0"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#D0D0D0"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 按钮样式：帮助按钮 -->
    <Style x:Key="HelpButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Width" Value="20"/>
        <Setter Property="Height" Value="20"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="10">
                        <ContentPresenter HorizontalAlignment="Center" 
                                          VerticalAlignment="Center" 
                                          Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#E9F2F7"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#CFE6F4"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 按钮样式：默认按钮 -->
    <Style x:Key="DefaultButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#F2F2F2"/>
        <Setter Property="Foreground" Value="#4C4C4C"/>
        <Setter Property="Padding" Value="4,2"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#ADADAD"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Width" Value="60"/>
        <Setter Property="Height" Value="22"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center" 
                            VerticalAlignment="Center" 
                            Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#E6E6E6"/>
                            <Setter Property="BorderBrush" Value="#949494"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#D6D6D6"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 按钮样式：链接按钮 -->
    <Style x:Key="LinkButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="4,2"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                           CornerRadius="4">
                        <TextBlock Text="{TemplateBinding Content}" 
                                   TextDecorations="Underline"
                                   Foreground="{TemplateBinding Foreground}"
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" Value="#00A0FF"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="#999999"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 分组框样式 -->
    <Style x:Key="GroupBoxStyle" TargetType="GroupBox">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Padding" Value="8,6,8,8"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
    </Style>
    
    <!-- 数据网格样式 -->
    <Style x:Key="DataGridStyle" TargetType="DataGrid">
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="CanUserReorderColumns" Value="True"/>
        <Setter Property="CanUserResizeRows" Value="False"/>
        <Setter Property="CanUserSortColumns" Value="True"/>
        <Setter Property="GridLinesVisibility" Value="All"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="AlternatingRowBackground" Value="#F5F5F5"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="VerticalGridLinesBrush" Value="{StaticResource DividerBrush}"/>
    </Style>
    
    <!-- 数据网格列标题样式 -->
    <Style x:Key="DataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
        <Setter Property="Background" Value="#F0F0F0"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="BorderThickness" Value="0,0,1,1"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>
    
    <!-- 滑块样式 -->
    <Style x:Key="SliderStyle" TargetType="Slider">
        <Setter Property="Margin" Value="0,5"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>
    
    <!-- TabControl样式 -->
    <Style x:Key="TabControlStyle" TargetType="TabControl">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="Padding" Value="2"/>
    </Style>
    
    <!-- TabItem样式 -->
    <Style x:Key="TabItemStyle" TargetType="TabItem">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Background" Value="#F0F0F0"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="1,1,1,0"/>
        <Setter Property="Padding" Value="10,4"/>
        <Setter Property="Margin" Value="0,0,1,0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TabItem">
                    <Grid>
                        <Border Name="Border"
                        Background="{TemplateBinding Background}" 
                        BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter ContentSource="Header"
                            HorizontalAlignment="Center" 
                            VerticalAlignment="Center" 
                            Margin="{TemplateBinding Padding}"/>
                    </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource SurfaceBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#E0E0E0"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 列表视图样式 -->
    <Style x:Key="ListViewStyle" TargetType="ListView">
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
    </Style>
    
    <!-- 列表视图项样式 -->
    <Style x:Key="ListViewItemStyle" TargetType="ListViewItem">
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
    </Style>
    
    <!-- 工具栏样式 -->
    <Style x:Key="ToolBarStyle" TargetType="ToolBar">
        <Setter Property="Background" Value="#F0F0F0"/>
        <Setter Property="Padding" Value="2"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
    </Style>
    
    <!-- 状态栏样式 -->
    <Style x:Key="StatusBarStyle" TargetType="StatusBar">
        <Setter Property="Background" Value="#F0F0F0"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="Height" Value="23"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="0,1,0,0"/>
    </Style>
    
    <!-- 进度条样式 -->
    <Style x:Key="ProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Background" Value="#E0E0E0"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Height" Value="6"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}" CornerRadius="3">
                        <Grid>
                            <Rectangle Name="PART_Track" Fill="{TemplateBinding Background}" RadiusX="3" RadiusY="3"/>
                            <Rectangle Name="PART_Indicator" Fill="{TemplateBinding Foreground}"
                                     HorizontalAlignment="Left" RadiusX="3" RadiusY="3"/>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 单选按钮样式 -->
    <Style x:Key="RadioButtonStyle" TargetType="RadioButton">
        <Setter Property="Margin" Value="0,5"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>
    
    <!-- 弹出框样式 -->
    <Style x:Key="PopupStyle" TargetType="Popup">
        <Setter Property="AllowsTransparency" Value="True"/>
        <Setter Property="PopupAnimation" Value="Slide"/>
    </Style>
    
    <!-- 字段错误样式 -->
    <Style x:Key="ErrorTextBlockStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Margin" Value="4,2,4,4"/>
    </Style>
    
    <!-- 信息提示样式 -->
    <Style x:Key="InfoTextBlockStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Margin" Value="4,2,4,4"/>
    </Style>
    
    <!-- 成功提示样式 -->
    <Style x:Key="SuccessTextBlockStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="#2E7D32"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Margin" Value="4,2,4,4"/>
    </Style>
    
    <!-- 警告提示样式 -->
    <Style x:Key="WarningTextBlockStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="#F9A825"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Margin" Value="4,2,4,4"/>
    </Style>
    
    <!-- 工具提示样式 -->
    <Style x:Key="ToolTipStyle" TargetType="ToolTip">
        <Setter Property="Background" Value="#F5F5F5"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="#CDCDCD"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="6,3"/>
    </Style>
    
    <!-- 日志窗口文本框样式 -->
    <Style x:Key="LogTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="White"/>
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="FontFamily" Value="Consolas, Courier New"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="AcceptsReturn" Value="True"/>
        <Setter Property="Padding" Value="5"/>
    </Style>

    <!-- 窗口边框样式 -->
    <Style x:Key="WindowBorderStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="0"/>
    </Style>

    <!-- 工具栏边框样式 -->
    <Style x:Key="ToolbarBorderStyle" TargetType="Border">
        <Setter Property="Background" Value="#F8F8F8"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Padding" Value="8,6"/>
    </Style>

    <!-- 状态栏边框样式 -->
    <Style x:Key="StatusBarBorderStyle" TargetType="Border">
        <Setter Property="Background" Value="#F0F0F0"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="0,1,0,0"/>
        <Setter Property="Padding" Value="8,4"/>
    </Style>

    <!-- 表单标签样式 -->
    <Style x:Key="FormLabelStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,2"/>
    </Style>

    <!-- 状态文本样式 -->
    <Style x:Key="StatusTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 只读文本框样式 -->
    <Style x:Key="ReadOnlyTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="#F8F8F8"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="#CDCDCD"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="4,2"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>

    <!-- 次要按钮样式 -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#F2F2F2"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="#ADADAD"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="3">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#E6E6E6"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#D6D6D6"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 垂直分隔符样式 -->
    <Style x:Key="VerticalGridSplitterStyle" TargetType="GridSplitter">
        <Setter Property="Background" Value="{StaticResource DividerBrush}"/>
        <Setter Property="Width" Value="4"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="VerticalAlignment" Value="Stretch"/>
        <Setter Property="ResizeDirection" Value="Columns"/>
        <Setter Property="ResizeBehavior" Value="PreviousAndNext"/>
    </Style>

    <!-- 紧凑型下拉框样式 -->
    <Style x:Key="CompactComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource ComboBoxStyle}">
        <Setter Property="Height" Value="18"/>
        <Setter Property="Padding" Value="3,1"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Margin" Value="0,1"/>
    </Style>

    <!-- 通用按钮样式 -->
    <Style x:Key="ButtonStyle" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
    </Style>

    <!-- Label样式 -->
    <Style x:Key="LabelStyle" TargetType="Label">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Padding" Value="0,2"/>
    </Style>

    <!-- ListBox样式 -->
    <Style x:Key="ListBoxStyle" TargetType="ListBox">
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
    </Style>

    <!-- 紧凑型文本框样式 -->
    <Style x:Key="CompactTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource TextBoxStyle}">
        <Setter Property="Height" Value="18"/>
        <Setter Property="Padding" Value="3,1"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Margin" Value="0,1"/>
    </Style>

    <!-- 紧凑型按钮样式 -->
    <Style x:Key="CompactButtonStyle" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
        <Setter Property="Height" Value="18"/>
        <Setter Property="Padding" Value="4,1"/>
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="Margin" Value="1"/>
    </Style>

    <!-- 紧凑型GroupBox样式 -->
    <Style x:Key="CompactGroupBoxStyle" TargetType="GroupBox">
        <Setter Property="Margin" Value="0,2"/>
        <Setter Property="Padding" Value="4"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Border Grid.Row="0" Background="#F8F8F8" BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="1,1,1,0" Padding="4,2">
                            <ContentPresenter ContentSource="Header" TextElement.FontWeight="SemiBold"/>
                        </Border>
                        <Border Grid.Row="1" Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="1,0,1,1"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter/>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>