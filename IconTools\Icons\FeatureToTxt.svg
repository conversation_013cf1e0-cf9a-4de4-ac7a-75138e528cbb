<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 统一蓝色渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="32" height="32" rx="8" fill="url(#bgGradient)"/>

  <!-- 地图要素 -->
  <path d="M4 8 L12 6 L14 16 L8 18 L6 12 Z" fill="#ffffff" opacity="0.9"/>

  <!-- 转换箭头 -->
  <path d="M15 16 L19 16 M17 14 L19 16 L17 18" stroke="#ffffff" stroke-width="2" stroke-linecap="round"/>

  <!-- 文本文件 -->
  <rect x="19" y="8" width="8" height="12" rx="2" fill="#ffffff" opacity="0.9"/>
  <rect x="20" y="22" width="6" height="2" rx="1" fill="#e17055"/>
  <text x="23" y="24" fill="#ffffff" font-family="SF Pro, Arial, sans-serif" font-size="5" font-weight="bold" text-anchor="middle">TXT</text>

  <!-- 文本行 -->
  <line x1="21" y1="12" x2="25" y2="12" stroke="#4facfe" stroke-width="1" opacity="0.7"/>
  <line x1="21" y1="14" x2="25" y2="14" stroke="#4facfe" stroke-width="1" opacity="0.7"/>
  <line x1="21" y1="16" x2="24" y2="16" stroke="#4facfe" stroke-width="1" opacity="0.7"/>
  <line x1="21" y1="18" x2="25" y2="18" stroke="#4facfe" stroke-width="1" opacity="0.7"/>
</svg>
