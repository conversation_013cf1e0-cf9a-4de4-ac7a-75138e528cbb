using System;
using System.Collections.Generic;
using System.Linq;
using XIAOFUTools.Tools.AIAssistant.Models;

namespace XIAOFUTools.Tools.AIAssistant.Services
{
    /// <summary>
    /// 对话管理服务
    /// </summary>
    public class ConversationManager
    {
        private readonly List<ChatMessage> _conversationHistory;
        private readonly int _maxHistoryCount;

        public ConversationManager(int maxHistoryCount = 50)
        {
            _conversationHistory = new List<ChatMessage>();
            _maxHistoryCount = maxHistoryCount;
        }

        /// <summary>
        /// 添加消息到对话历史
        /// </summary>
        /// <param name="message">聊天消息</param>
        public void AddMessage(ChatMessage message)
        {
            if (message == null)
                return;

            _conversationHistory.Add(message);

            // 保持历史记录在限制范围内
            if (_conversationHistory.Count > _maxHistoryCount)
            {
                // 保留系统消息，删除最旧的用户/助手消息
                var systemMessages = _conversationHistory.Where(m => m.Role == "system").ToList();
                var otherMessages = _conversationHistory.Where(m => m.Role != "system").ToList();
                
                // 删除最旧的非系统消息
                int toRemove = _conversationHistory.Count - _maxHistoryCount;
                otherMessages = otherMessages.Skip(toRemove).ToList();
                
                _conversationHistory.Clear();
                _conversationHistory.AddRange(systemMessages);
                _conversationHistory.AddRange(otherMessages);
            }
        }

        /// <summary>
        /// 获取对话历史
        /// </summary>
        /// <returns>对话历史数组</returns>
        public ChatMessage[] GetConversationHistory()
        {
            return _conversationHistory.ToArray();
        }

        /// <summary>
        /// 获取用于API调用的对话历史（最近N条）
        /// </summary>
        /// <param name="maxCount">最大消息数量</param>
        /// <returns>对话历史</returns>
        public ChatMessage[] GetRecentHistory(int maxCount = 20)
        {
            // 始终包含系统消息
            var systemMessages = _conversationHistory.Where(m => m.Role == "system").ToList();
            var otherMessages = _conversationHistory.Where(m => m.Role != "system").TakeLast(maxCount - systemMessages.Count).ToList();
            
            var result = new List<ChatMessage>();
            result.AddRange(systemMessages);
            result.AddRange(otherMessages);
            
            return result.OrderBy(m => m.Timestamp).ToArray();
        }

        /// <summary>
        /// 清除对话历史
        /// </summary>
        public void ClearHistory()
        {
            _conversationHistory.Clear();
        }

        /// <summary>
        /// 获取对话历史数量
        /// </summary>
        public int HistoryCount => _conversationHistory.Count;

        /// <summary>
        /// 添加系统消息（如果不存在）
        /// </summary>
        /// <param name="systemPrompt">系统提示</param>
        public void EnsureSystemMessage(string systemPrompt)
        {
            if (!_conversationHistory.Any(m => m.Role == "system"))
            {
                var systemMessage = ChatMessage.CreateSystemMessage(systemPrompt);
                _conversationHistory.Insert(0, systemMessage);
            }
        }

        /// <summary>
        /// 获取最后一条用户消息
        /// </summary>
        /// <returns>最后一条用户消息</returns>
        public ChatMessage GetLastUserMessage()
        {
            return _conversationHistory.LastOrDefault(m => m.Role == "user");
        }

        /// <summary>
        /// 获取最后一条助手消息
        /// </summary>
        /// <returns>最后一条助手消息</returns>
        public ChatMessage GetLastAssistantMessage()
        {
            return _conversationHistory.LastOrDefault(m => m.Role == "assistant");
        }
    }
}
