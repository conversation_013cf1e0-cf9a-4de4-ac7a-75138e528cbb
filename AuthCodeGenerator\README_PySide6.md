# 授权码生成器 - 极简版

## ✨ 全新极简设计

### 🎯 设计理念
- **极简至上**：去除冗余元素，专注核心功能
- **小巧精致**：窗口尺寸 420×480，占用空间更小
- **现代美观**：渐变背景，圆角设计，视觉舒适
- **紧凑高效**：所有功能一屏显示，操作便捷

### 🎨 界面优化对比

#### 原版 vs 极简版
| 特性 | 原版(tkinter) | 极简版(PySide6) |
|------|---------------|-----------------|
| 窗口尺寸 | 650×600 | 420×480 |
| 标题区域 | 3行文字+分隔线 | 1行简洁标题 |
| 授权类型 | 垂直布局 | 水平紧凑布局 |
| 时长设置 | 分散布局 | 单行集成布局 |
| 按钮样式 | 系统默认 | 现代化圆角 |
| 整体风格 | 传统界面 | 极简现代 |

### 🔧 技术特色
- **PySide6框架**：Qt6技术栈，性能卓越
- **自定义组件**：ModernButton、ModernTextEdit等
- **渐变背景**：视觉层次丰富
- **响应式交互**：悬停、聚焦效果

## 安装和运行

### 方法一：使用启动器（推荐）
```bash
python run_pyside6.py
```
启动器会自动检查并安装依赖。

### 方法二：手动安装依赖
```bash
pip install PySide6
python auth_code_generator_pyside6.py
```

### 方法三：使用requirements.txt
```bash
pip install -r requirements.txt
python auth_code_generator_pyside6.py
```

## 功能说明

### 授权类型
- **个人版**：绑定机器码，只能在指定电脑使用
- **通用版**：不绑定机器码，可在任何电脑使用

### 时长设置
- **快速选择**：7天、30天、90天、365天
- **自定义天数**：1-3650天
- **指定过期时间**：精确到秒的过期时间

### 操作功能
- **生成授权码**：根据设置生成对应授权码
- **复制到剪贴板**：一键复制授权码
- **保存到文件**：导出授权码到文本文件
- **清空重置**：清空所有输入内容

## 文件说明

- `auth_code_generator_pyside6.py` - PySide6版本主程序
- `auth_code_generator.py` - 原tkinter版本（保留）
- `run_pyside6.py` - PySide6版本启动器
- `requirements.txt` - 依赖包列表
- `settings.json` - 设置文件（自动生成）

## 系统要求

- Python 3.7+
- PySide6 6.5.0+
- Windows/macOS/Linux

## 更新日志

### v2.0 PySide6版本
- ✨ 全新极简现代化界面设计
- 🔧 升级到PySide6框架
- 🎨 优化视觉效果和用户体验
- 📱 紧凑布局，界面更加简洁
- 🚀 提升性能和稳定性

## 🎉 打包完成

已成功使用Nuitka打包为独立exe文件：

### 📦 可执行文件
- **单文件版**: `授权码生成器.exe` (~21MB)
- **多文件版**: `auth_code_generator_pyside6.dist/授权码生成器_简化版.exe`

### 🚀 使用方式
- 直接双击exe文件即可运行
- 无需安装Python或任何依赖
- 支持Windows 7/8/10/11

### 🛠️ 打包工具
- `build_exe.py` - Python打包脚本
- `打包exe.bat` - 一键打包批处理
- 详见 `打包说明.md`

---

**作者**: XIAOFU
**QQ**: 1922759464
**Q群**: 967758553
**版本**: v2.0 极简版
**打包**: Nuitka 2.7.3
