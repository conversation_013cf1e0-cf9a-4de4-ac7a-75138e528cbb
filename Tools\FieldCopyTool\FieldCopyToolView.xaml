<UserControl x:Class="XIAOFUTools.Tools.FieldCopyTool.FieldCopyToolView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.FieldCopyTool"
             mc:Ignorable="d"
             d:DataContext="{Binding Path=FieldCopyToolViewModel}"
             d:DesignHeight="600" d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 引用自定义样式 -->
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 布尔值反转转换器 -->
            <local:BooleanInverseConverter x:Key="BooleanInverseConverter"/>

            <!-- 局部样式重写 -->
            <Style x:Key="FormLabelStyle" TargetType="TextBlock" BasedOn="{StaticResource LabelTextBlockStyle}">
                <Setter Property="Margin" Value="0,3,10,0"/>
                <Setter Property="HorizontalAlignment" Value="Left"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
            </Style>

            <Style x:Key="CompactComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource ComboBoxStyle}">
                <Setter Property="Margin" Value="0,2,0,8"/>
            </Style>

            <Style x:Key="ListBoxStyle" TargetType="ListBox">
                <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
                <Setter Property="Margin" Value="0,2,0,8"/>
            </Style>

            <Style x:Key="SmallButtonStyle" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
                <Setter Property="Width" Value="50"/>
                <Setter Property="Height" Value="20"/>
                <Setter Property="FontSize" Value="10"/>
                <Setter Property="Margin" Value="2"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Border Background="{StaticResource BackgroundBrush}" BorderBrush="#CDCDCD" BorderThickness="1" CornerRadius="4">
        <Grid Margin="8">
            <Grid.RowDefinitions>
                <RowDefinition Height="3*"/>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 主要内容区域 -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左面板：源数据和字段 -->
                <GroupBox Grid.Column="0" Header="源数据和字段"
                         Style="{StaticResource GroupBoxStyle}"
                         Margin="0,0,4,0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 源数据选择 -->
                        <TextBlock Grid.Row="0" Text="源数据:"
                                  Style="{StaticResource FormLabelStyle}"
                                  Margin="0,0,0,2"/>
                        <Grid Grid.Row="1" Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <ComboBox Grid.Column="0"
                                     Style="{StaticResource CompactComboBoxStyle}"
                                     ItemsSource="{Binding SourceLayerList}"
                                     SelectedItem="{Binding SelectedSourceLayer, Mode=TwoWay}"
                                     DisplayMemberPath="Name"
                                     IsEnabled="{Binding IsProcessing, Converter={StaticResource BooleanInverseConverter}}"
                                     Margin="0,0,0,0"/>
                            <Button Grid.Column="1" Width="22" Height="22" Margin="5,0,0,0"
                                    Style="{StaticResource DefaultButtonStyle}"
                                    Command="{Binding RefreshLayersCommand}"
                                    ToolTip="刷新图层列表"
                                    VerticalAlignment="Center">
                                <TextBlock Text="⟲" FontSize="12" FontWeight="Bold"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Button>
                        </Grid>

                        <!-- 字段列表 -->
                        <DataGrid Grid.Row="2"
                                 Style="{StaticResource DataGridStyle}"
                                 ItemsSource="{Binding FieldList}"
                                 IsEnabled="{Binding IsProcessing, Converter={StaticResource BooleanInverseConverter}}"
                                 AutoGenerateColumns="False"
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 CanUserReorderColumns="False"
                                 CanUserResizeRows="False"
                                 CanUserSortColumns="True"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column"
                                 SelectionMode="Single"
                                 RowHeight="20"
                                 Margin="0,0,0,4">
                            <DataGrid.Columns>
                                <DataGridCheckBoxColumn Header="选择"
                                                       Binding="{Binding IsSelected, Mode=TwoWay}"
                                                       Width="35"/>
                                <DataGridTextColumn Header="字段名称"
                                                   Binding="{Binding Name}"
                                                   Width="90"
                                                   IsReadOnly="True"/>
                                <DataGridTextColumn Header="别名"
                                                   Binding="{Binding Alias}"
                                                   Width="80"
                                                   IsReadOnly="True"/>
                                <DataGridTextColumn Header="类型"
                                                   Binding="{Binding FieldType}"
                                                   Width="60"
                                                   IsReadOnly="True"/>
                                <DataGridTextColumn Header="长度"
                                                   Binding="{Binding Length}"
                                                   Width="40"
                                                   IsReadOnly="True"/>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- 字段操作按钮 -->
                        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,2,0,0">
                            <Button Content="全选"
                                   Style="{StaticResource SmallButtonStyle}"
                                   Command="{Binding SelectAllFieldsCommand}"/>
                            <Button Content="反选"
                                   Style="{StaticResource SmallButtonStyle}"
                                   Command="{Binding InvertFieldSelectionCommand}"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- 右面板：目标图层 -->
                <GroupBox Grid.Column="1" Header="目标图层"
                         Style="{StaticResource GroupBoxStyle}"
                         Margin="4,0,0,0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 目标图层列表 -->
                        <ListBox Grid.Row="0"
                                Style="{StaticResource ListBoxStyle}"
                                ItemsSource="{Binding TargetLayerList}"
                                IsEnabled="{Binding IsProcessing, Converter={StaticResource BooleanInverseConverter}}"
                                Margin="0,0,0,4">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <CheckBox Content="{Binding Name}"
                                             IsChecked="{Binding IsSelected, Mode=TwoWay}"
                                             Style="{StaticResource CheckBoxStyle}"
                                             Margin="2"/>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>

                        <!-- 目标图层操作按钮 -->
                        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,2,0,0">
                            <Button Content="全选"
                                   Style="{StaticResource SmallButtonStyle}"
                                   Command="{Binding SelectAllTargetLayersCommand}"/>
                            <Button Content="反选"
                                   Style="{StaticResource SmallButtonStyle}"
                                   Command="{Binding InvertTargetLayerSelectionCommand}"/>
                            <Button Content="⟲" Width="22" Height="22" Margin="5,0,0,0"
                                   Style="{StaticResource DefaultButtonStyle}"
                                   Command="{Binding RefreshLayersCommand}"
                                   ToolTip="刷新图层列表"
                                   FontSize="12" FontWeight="Bold"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>
            </Grid>

            <!-- 日志区域 -->
            <GroupBox Grid.Row="1" Header="操作日志"
                     Style="{StaticResource GroupBoxStyle}"
                     Margin="0,4,0,0">
                <TextBox Text="{Binding LogText, Mode=OneWay}"
                        Style="{StaticResource LogTextBoxStyle}"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        ScrollViewer.HorizontalScrollBarVisibility="Auto"/>
            </GroupBox>

            <!-- 底部按钮 -->
            <Grid Grid.Row="2">
                <Border BorderBrush="{StaticResource DividerBrush}"
                       BorderThickness="0,1,0,0"
                       Margin="0,4,0,4"
                       Padding="0,6,0,0">
                    <Grid>
                        <Button x:Name="HelpButton" Content="?"
                                Style="{StaticResource HelpButtonStyle}"
                                Command="{Binding ShowHelpCommand}"
                                ToolTip="查看工具使用说明"
                                HorizontalAlignment="Left"/>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button Content="停止"
                                    Style="{StaticResource CancelButtonStyle}"
                                    Command="{Binding StopCommand}"
                                    VerticalAlignment="Center"
                                    Margin="0,0,8,0"/>
                            <Button Content="开始"
                                    Style="{StaticResource ExecuteButtonStyle}"
                                    Command="{Binding StartCommand}"
                                    VerticalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
    </Border>
</UserControl>
