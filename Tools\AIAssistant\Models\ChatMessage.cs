using System;

namespace XIAOFUTools.Tools.AIAssistant.Models
{
    /// <summary>
    /// 聊天消息模型
    /// </summary>
    public class ChatMessage
    {
        /// <summary>
        /// 消息角色
        /// </summary>
        public string Role { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 消息时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 消息ID
        /// </summary>
        public string Id { get; set; }

        public ChatMessage()
        {
            Id = Guid.NewGuid().ToString();
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 创建用户消息
        /// </summary>
        /// <param name="content">消息内容</param>
        /// <returns>用户消息</returns>
        public static ChatMessage CreateUserMessage(string content)
        {
            return new ChatMessage
            {
                Role = "user",
                Content = content
            };
        }

        /// <summary>
        /// 创建助手消息
        /// </summary>
        /// <param name="content">消息内容</param>
        /// <returns>助手消息</returns>
        public static ChatMessage CreateAssistantMessage(string content)
        {
            return new ChatMessage
            {
                Role = "assistant",
                Content = content
            };
        }

        /// <summary>
        /// 创建系统消息
        /// </summary>
        /// <param name="content">消息内容</param>
        /// <returns>系统消息</returns>
        public static ChatMessage CreateSystemMessage(string content)
        {
            return new ChatMessage
            {
                Role = "system",
                Content = content
            };
        }
    }
}
