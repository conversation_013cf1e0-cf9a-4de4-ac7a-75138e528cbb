// 流式输出处理器
class StreamHandler {
    static appendContent(targetElement, newContent) {
        if (!targetElement || !newContent) return;
        
        // 获取当前内容
        let currentContent = targetElement.getAttribute('data-raw-content') || '';
        
        // 追加新内容
        currentContent += newContent;
        
        // 保存原始内容
        targetElement.setAttribute('data-raw-content', currentContent);
        
        // 渲染Markdown
        targetElement.innerHTML = MarkdownRenderer.render(currentContent);
        
        // 滚动到底部
        this.scrollToBottom();
    }

    static setContent(targetElement, content) {
        if (!targetElement) return;
        
        // 设置内容
        targetElement.setAttribute('data-raw-content', content);
        targetElement.innerHTML = MarkdownRenderer.render(content);
        
        // 滚动到底部
        this.scrollToBottom();
    }

    static scrollToBottom() {
        const messagesArea = document.getElementById('messages-area');
        if (messagesArea) {
            setTimeout(() => {
                messagesArea.scrollTop = messagesArea.scrollHeight;
            }, 10);
        }
    }

    // 打字机效果（可选）
    static typewriterEffect(targetElement, content, speed = 30) {
        return new Promise((resolve) => {
            let index = 0;
            let currentContent = '';
            
            const timer = setInterval(() => {
                if (index < content.length) {
                    currentContent += content[index];
                    this.setContent(targetElement, currentContent);
                    index++;
                } else {
                    clearInterval(timer);
                    resolve();
                }
            }, speed);
        });
    }

    // 模拟流式输出（用于测试）
    static simulateStream(targetElement, content, chunkSize = 5, delay = 50) {
        return new Promise((resolve) => {
            let index = 0;
            
            const sendChunk = () => {
                if (index < content.length) {
                    const chunk = content.slice(index, index + chunkSize);
                    this.appendContent(targetElement, chunk);
                    index += chunkSize;
                    
                    setTimeout(sendChunk, delay);
                } else {
                    resolve();
                }
            };
            
            sendChunk();
        });
    }
}
