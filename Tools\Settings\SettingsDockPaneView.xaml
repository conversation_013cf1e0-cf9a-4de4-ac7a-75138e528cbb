<UserControl x:Class="XIAOFUTools.Tools.Settings.SettingsDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.Settings"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="400"
             d:DataContext="{Binding Path=SettingsDockPaneViewModel}">
    <UserControl.DataContext>
        <local:SettingsDockPaneViewModel/>
    </UserControl.DataContext>
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 设置内容区域 -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 通用设置组 -->
                <GroupBox Header="通用设置" Style="{StaticResource GroupBoxStyle}" Margin="0,0,0,8">
                    <StackPanel Margin="8,5">
                        <CheckBox Content="自动保存用户偏好"
                                  Style="{StaticResource CheckBoxStyle}"
                                  IsChecked="{Binding AutoSavePreferences}"
                                  ToolTip="自动保存用户的设置偏好"
                                  Margin="0,2"/>

                        <CheckBox Content="显示工具提示"
                                  Style="{StaticResource CheckBoxStyle}"
                                  IsChecked="{Binding ShowTooltips}"
                                  ToolTip="在界面元素上显示工具提示"
                                  Margin="0,2"/>
                    </StackPanel>
                </GroupBox>

                <!-- 查看面积工具设置组 -->
                <GroupBox Header="查看面积工具" Style="{StaticResource GroupBoxStyle}" Margin="0,0,0,8">
                    <StackPanel Margin="8,5">
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="50"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="默认小数位数:" VerticalAlignment="Center" Margin="0,0,8,0" FontSize="11"/>
                            <TextBox Grid.Column="1" Text="{Binding DefaultDecimalPlaces}" Style="{StaticResource TextBoxStyle}"
                                     ToolTip="设置面积和长度计算结果的默认小数位数（0-10）" Height="20"/>
                            <TextBlock Grid.Column="2" Text="（0-10位）" VerticalAlignment="Center" Margin="5,0,0,0"
                                       Foreground="{StaticResource TextSecondaryBrush}" FontSize="10"/>
                        </Grid>

                        <CheckBox Content="只有选择要素时才能打开窗口"
                                  Style="{StaticResource CheckBoxStyle}"
                                  IsChecked="{Binding RequireSelectionToOpen}"
                                  ToolTip="启用后，必须先选择面要素或线要素才能打开查看面积工具"
                                  Margin="0,2"/>

                        <CheckBox Content="取消选择后自动关闭窗口"
                                  Style="{StaticResource CheckBoxStyle}"
                                  IsChecked="{Binding AutoCloseOnClearSelection}"
                                  ToolTip="启用后，当清空地图选择时会自动关闭查看面积工具窗口"
                                  Margin="0,2"/>
                    </StackPanel>
                </GroupBox>

                <!-- 预设图层管理组 -->
                <GroupBox Header="预设图层管理" Style="{StaticResource GroupBoxStyle}" Margin="0,0,0,8">
                    <StackPanel Margin="8,5">
                        <CheckBox Content="自动刷新图层列表"
                                  Style="{StaticResource CheckBoxStyle}"
                                  IsChecked="{Binding AutoRefreshLayers}"
                                  ToolTip="启用后，预设图层列表会自动刷新以显示新添加的图层文件"
                                  Margin="0,2"/>

                        <CheckBox Content="添加图层后自动移动到底部"
                                  Style="{StaticResource CheckBoxStyle}"
                                  IsChecked="{Binding MoveLayersToBottom}"
                                  ToolTip="启用后，新添加的预设图层会自动移动到图层列表底部"
                                  Margin="0,2,0,8"/>

                        <!-- 操作按钮 -->
                        <StackPanel Orientation="Horizontal">
                            <Button Content="导入图层"
                                    Style="{StaticResource ExecuteButtonStyle}"
                                    Command="{Binding ImportLayersCommand}"
                                    ToolTip="导入.lyr或.lyrx文件到预设图层文件夹"
                                    Width="70"
                                    Height="22"
                                    Margin="0,0,8,0"/>

                            <Button Content="打开文件夹"
                                    Style="{StaticResource DefaultButtonStyle}"
                                    Command="{Binding OpenLayersFolderCommand}"
                                    ToolTip="在资源管理器中打开预设图层文件夹"
                                    Width="80"
                                    Height="22"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- 插件更新设置组 -->
                <GroupBox Header="插件更新" Style="{StaticResource GroupBoxStyle}" Margin="0,0,0,8">
                    <StackPanel Margin="8,5">
                        <CheckBox Content="自动检测更新"
                                  Style="{StaticResource CheckBoxStyle}"
                                  IsChecked="{Binding AutoCheckForUpdates}"
                                  ToolTip="启用后，插件会定期检查是否有新版本可用（默认每天检查）"
                                  Margin="0,2"/>

                        <CheckBox Content="发现更新时通知"
                                  Style="{StaticResource CheckBoxStyle}"
                                  IsChecked="{Binding NotifyOnUpdate}"
                                  ToolTip="启用后，发现新版本时会弹出通知"
                                  Margin="0,2"/>

                        <CheckBox Content="启动时检查更新 (每次启动都检查)"
                                  Style="{StaticResource CheckBoxStyle}"
                                  IsChecked="{Binding CheckForUpdatesOnStartup}"
                                  ToolTip="启用后，每次启动软件时都会检查是否有新版本可用（不受检查间隔限制）"
                                  Margin="0,2"/>

                        <Grid Margin="0,4,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="50"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="检查间隔:" VerticalAlignment="Center" Margin="0,0,8,0" FontSize="11"/>
                            <TextBox Grid.Column="1" Text="{Binding CheckInterval}" Style="{StaticResource TextBoxStyle}"
                                     ToolTip="设置自动检查更新的间隔天数（0=每次启动都检查，1=每天，7=每周）" Height="20"/>
                            <TextBlock Grid.Column="2" Text="天 (0=每次启动都检查)" VerticalAlignment="Center" Margin="5,0,0,0"
                                       Foreground="{StaticResource TextSecondaryBrush}" FontSize="10"/>
                        </Grid>

                        <TextBlock Text="{Binding LastCheckTimeText}"
                                   Style="{StaticResource InfoTextBlockStyle}"
                                   FontSize="10"
                                   Margin="0,0,0,8"/>

                        <!-- 检查更新按钮 -->
                        <Button Content="立即检查更新"
                                Style="{StaticResource ExecuteButtonStyle}"
                                Command="{Binding CheckUpdateCommand}"
                                ToolTip="立即检查是否有新版本可用"
                                Width="100"
                                Height="22"
                                HorizontalAlignment="Left"/>
                    </StackPanel>
                </GroupBox>

            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮区域 -->
        <Border Grid.Row="1"
                BorderBrush="{StaticResource DividerBrush}"
                BorderThickness="0,1,0,0"
                Margin="0,8,0,0"
                Padding="0,8,0,0">
            <Grid>
                <Button Content="?" Width="22" Height="22"
                        Style="{StaticResource HelpButtonStyle}"
                        Command="{Binding ShowHelpCommand}"
                        ToolTip="查看设置说明"
                        HorizontalAlignment="Left"/>

                <TextBlock Text="设置会自动保存"
                           Style="{StaticResource InfoTextBlockStyle}"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           FontSize="10"/>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="重置"
                            Style="{StaticResource CancelButtonStyle}"
                            Margin="0,0,8,0"
                            Command="{Binding ResetCommand}"
                            ToolTip="重置所有设置为默认值"
                            Width="50"
                            Height="22"/>
                    <Button Content="应用"
                            Style="{StaticResource ExecuteButtonStyle}"
                            Command="{Binding ApplyCommand}"
                            ToolTip="手动保存当前设置"
                            Width="50"
                            Height="22"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
