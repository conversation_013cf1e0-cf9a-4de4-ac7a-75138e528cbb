# 上下文菜单扩展指南

## 📋 快速开始

在Config.daml中已预留了10个空的上下文菜单，只需在相应位置添加按钮即可。

**推荐的添加方式**：
```xml
<updateMenu refID="菜单ID">
  <insertButton refID="你的按钮ID" insert="before" placeWith="esri_core_editCopyButton" separator="true"/>
</updateMenu>
```

## 🎯 常用菜单ID

| 菜单类型 | 菜单ID | 使用场景 |
|---------|--------|----------|
| **选择要素菜单** ✅ | `esri_mapping_selection2DContextMenu` | 选中要素右键 |
| **地图框菜单** ✅ | `esri_mapping_mapContextMenu` | 地图列表中地图右键 |
| **地图视图菜单** ✅ | `esri_mapping_popupToolContextMenu` | 地图空白区域右键 |
| 图层菜单 | `esri_mapping_layerContextMenu` | 图层列表右键 |
| 要素数据菜单 | `esri_editing_data` | 要素图层数据操作 |

**完整菜单列表**: 查看Config.daml中的updateMenu节点

## 🛠️ 添加步骤

1. **在controls中定义按钮**
2. **在相应的updateMenu中添加insertButton**
3. **重新编译和测试**

### 推荐配置
```xml
<insertButton refID="你的按钮ID" insert="before" placeWith="esri_core_editCopyButton" separator="true"/>
```
- `insert="before"` - 在复制按钮之前显示
- `placeWith="esri_core_editCopyButton"` - 参考复制按钮位置
- `separator="true"` - 添加分隔线

## 🎯 实际示例

### 在图层右键菜单添加工具
```xml
<updateMenu refID="esri_mapping_layerContextMenu">
  <insertButton refID="XIAOFUTools_你的工具" insert="before" placeWith="esri_core_editCopyButton" separator="true"/>
</updateMenu>
```

### 在选择菜单添加多个工具
```xml
<updateMenu refID="esri_mapping_selection2DContextMenu">
  <insertButton refID="XIAOFUTools_ViewAreaButton" insert="before" placeWith="esri_core_editCopyButton" separator="true"/>
  <insertButton refID="XIAOFUTools_工具2" insert="after" placeWith="XIAOFUTools_ViewAreaButton" separator="false"/>
</updateMenu>
```

## ⚠️ 重要提醒

- 按钮必须先在`<controls>`中定义
- 修改后需要重启ArcGIS Pro测试
- 推荐使用`insert="before" placeWith="esri_core_editCopyButton"`确保正确位置

---

*最后更新: 2024年*
