<UserControl x:Class="XIAOFUTools.Tools.AIAssistant.AIAssistantDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:wv2="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="300"
             Loaded="UserControl_Loaded">
    <Grid>
        <!-- WebView2 主界面区域 -->
        <wv2:WebView2 x:Name="webView"
                      NavigationCompleted="WebView_NavigationCompleted"
                      WebMessageReceived="WebView_WebMessageReceived"/>
    </Grid>
</UserControl>
