using System;
using System.Linq;
using System.Threading.Tasks;
using ArcGIS.Core.Data;
using ArcGIS.Desktop.Framework.Threading.Tasks;
using ArcGIS.Desktop.Mapping;
using Newtonsoft.Json;
using XIAOFUTools.Tools.AIAssistant.Models;
using XIAOFUTools.Tools.AIAssistant.Tools.Base;
using System.Collections.Generic;

namespace XIAOFUTools.Tools.AIAssistant.Tools
{
    /// <summary>
    /// 要素选择函数
    /// 提供要素选择、查询和统计功能
    /// </summary>
    public class FeatureSelectionFunction : BaseOpenAIFunction
    {
        public override string Name => "select_features";

        public override string Description => "执行ArcGIS Pro要素选择和查询操作，包括按属性选择、清除选择、获取选择统计、全选、反选等功能。";

        protected override FunctionParameters GetParametersDefinition()
        {
            var parameters = new FunctionParameters();
            
            parameters.AddProperty("action", 
                ParameterProperty.CreateString("要执行的选择操作", 
                    new List<string> { "select_by_attribute", "clear_selection", "get_selection_info", "select_all", "invert_selection" }), 
                true);
            
            parameters.AddProperty("layer_name", 
                ParameterProperty.CreateString("目标图层名称"));
            
            parameters.AddProperty("where_clause", 
                ParameterProperty.CreateString("SQL查询条件（用于按属性选择）"));
            
            parameters.AddProperty("selection_type", 
                ParameterProperty.CreateString("选择类型", 
                    new List<string> { "new", "add", "remove", "intersect" }, "new"));

            return parameters;
        }

        protected override async Task<FunctionResult> ExecuteInternalAsync(FunctionCall functionCall, GISContext context)
        {
            Log($"开始执行要素选择操作，参数: {functionCall.Arguments}");

            if (context == null)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    "无法获取ArcGIS Pro上下文信息"
                );
            }

            // 解析参数
            var parameters = ParseArguments<FeatureSelectionParameters>(functionCall);

            try
            {
                return await QueuedTask.Run(() =>
                {
                    var mapView = MapView.Active;
                    if (mapView?.Map == null)
                    {
                        return FunctionResult.CreateFailure(
                            functionCall.Id,
                            functionCall.Name,
                            "没有活动的地图视图"
                        );
                    }

                    var map = mapView.Map;

                    switch (parameters.Action.ToLower())
                    {
                        case "select_by_attribute":
                            return SelectByAttribute(functionCall, map, parameters);

                        case "clear_selection":
                            return ClearSelection(functionCall, map, parameters.LayerName);

                        case "get_selection_info":
                            return GetSelectionInfo(functionCall, map, parameters.LayerName);

                        case "select_all":
                            return SelectAll(functionCall, map, parameters.LayerName);

                        case "invert_selection":
                            return InvertSelection(functionCall, map, parameters.LayerName);

                        default:
                            return FunctionResult.CreateFailure(
                                functionCall.Id,
                                functionCall.Name,
                                $"不支持的操作: {parameters.Action}"
                            );
                    }
                });
            }
            catch (Exception ex)
            {
                Log($"执行要素选择操作时发生错误: {ex.Message}", LogLevel.Error);
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"操作失败: {ex.Message}"
                );
            }
        }

        private FunctionResult SelectByAttribute(FunctionCall functionCall, Map map, FeatureSelectionParameters parameters)
        {
            if (string.IsNullOrWhiteSpace(parameters.LayerName))
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    "图层名称不能为空"
                );
            }

            if (string.IsNullOrWhiteSpace(parameters.WhereClause))
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    "查询条件不能为空"
                );
            }

            var featureLayer = map.FindLayers(parameters.LayerName).OfType<FeatureLayer>().FirstOrDefault();
            if (featureLayer == null)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"未找到要素图层: {parameters.LayerName}"
                );
            }

            try
            {
                var queryFilter = new QueryFilter { WhereClause = parameters.WhereClause };
                
                SelectionCombinationMethod selectionMethod = SelectionCombinationMethod.New;
                switch (parameters.SelectionType?.ToLower())
                {
                    case "add":
                        selectionMethod = SelectionCombinationMethod.Add;
                        break;
                    case "remove":
                        selectionMethod = SelectionCombinationMethod.Subtract;
                        break;
                    case "intersect":
                        selectionMethod = SelectionCombinationMethod.And;
                        break;
                }

                var selection = featureLayer.Select(queryFilter, selectionMethod);
                var selectedCount = selection.GetCount();

                var result = new
                {
                    layer_name = featureLayer.Name,
                    where_clause = parameters.WhereClause,
                    selection_type = parameters.SelectionType,
                    selected_count = selectedCount,
                    total_features = featureLayer.GetFeatureClass().GetCount()
                };

                return FunctionResult.CreateSuccessWithData(
                    functionCall.Id,
                    functionCall.Name,
                    result,
                    $"成功选择 {selectedCount} 个要素"
                );
            }
            catch (Exception ex)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"按属性选择失败: {ex.Message}"
                );
            }
        }

        private FunctionResult ClearSelection(FunctionCall functionCall, Map map, string layerName)
        {
            if (string.IsNullOrWhiteSpace(layerName))
            {
                // 清除所有图层的选择
                map.ClearSelection();
                return FunctionResult.CreateSuccessWithData(
                    functionCall.Id,
                    functionCall.Name,
                    new { cleared_layers = "all" },
                    "成功清除所有图层的选择"
                );
            }
            else
            {
                var featureLayer = map.FindLayers(layerName).OfType<FeatureLayer>().FirstOrDefault();
                if (featureLayer == null)
                {
                    return FunctionResult.CreateFailure(
                        functionCall.Id,
                        functionCall.Name,
                        $"未找到要素图层: {layerName}"
                    );
                }

                featureLayer.ClearSelection();
                return FunctionResult.CreateSuccessWithData(
                    functionCall.Id,
                    functionCall.Name,
                    new { cleared_layer = layerName },
                    $"成功清除图层 {layerName} 的选择"
                );
            }
        }

        private FunctionResult GetSelectionInfo(FunctionCall functionCall, Map map, string layerName)
        {
            if (string.IsNullOrWhiteSpace(layerName))
            {
                // 获取所有图层的选择信息
                var allSelections = map.GetLayersAsFlattenedList().OfType<FeatureLayer>()
                    .Select(layer => new
                    {
                        layer_name = layer.Name,
                        selected_count = layer.SelectionCount,
                        total_features = layer.GetFeatureClass()?.GetCount() ?? 0
                    })
                    .Where(info => info.selected_count > 0)
                    .ToArray();

                var totalSelected = allSelections.Sum(s => s.selected_count);

                var result = new
                {
                    total_selected = totalSelected,
                    layers_with_selection = allSelections.Length,
                    layer_details = allSelections
                };

                return FunctionResult.CreateSuccessWithData(
                    functionCall.Id,
                    functionCall.Name,
                    result,
                    $"当前共选择 {totalSelected} 个要素，涉及 {allSelections.Length} 个图层"
                );
            }
            else
            {
                var featureLayer = map.FindLayers(layerName).OfType<FeatureLayer>().FirstOrDefault();
                if (featureLayer == null)
                {
                    return FunctionResult.CreateFailure(
                        functionCall.Id,
                        functionCall.Name,
                        $"未找到要素图层: {layerName}"
                    );
                }

                var result = new
                {
                    layer_name = featureLayer.Name,
                    selected_count = featureLayer.SelectionCount,
                    total_features = featureLayer.GetFeatureClass().GetCount()
                };

                return FunctionResult.CreateSuccessWithData(
                    functionCall.Id,
                    functionCall.Name,
                    result,
                    $"图层 {layerName} 当前选择 {featureLayer.SelectionCount} 个要素"
                );
            }
        }

        private FunctionResult SelectAll(FunctionCall functionCall, Map map, string layerName)
        {
            if (string.IsNullOrWhiteSpace(layerName))
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    "图层名称不能为空"
                );
            }

            var featureLayer = map.FindLayers(layerName).OfType<FeatureLayer>().FirstOrDefault();
            if (featureLayer == null)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"未找到要素图层: {layerName}"
                );
            }

            // 使用空查询过滤器选择所有要素
            var queryFilter = new QueryFilter();
            featureLayer.Select(queryFilter, SelectionCombinationMethod.New);
            var selectedCount = featureLayer.SelectionCount;

            var result = new
            {
                layer_name = featureLayer.Name,
                selected_count = selectedCount
            };

            return FunctionResult.CreateSuccessWithData(
                functionCall.Id,
                functionCall.Name,
                result,
                $"成功选择图层 {layerName} 的所有 {selectedCount} 个要素"
            );
        }

        private FunctionResult InvertSelection(FunctionCall functionCall, Map map, string layerName)
        {
            if (string.IsNullOrWhiteSpace(layerName))
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    "图层名称不能为空"
                );
            }

            var featureLayer = map.FindLayers(layerName).OfType<FeatureLayer>().FirstOrDefault();
            if (featureLayer == null)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"未找到要素图层: {layerName}"
                );
            }

            var previousCount = featureLayer.SelectionCount;

            // ArcGIS Pro SDK没有直接的InvertSelection方法，需要手动实现
            // 先获取所有要素，然后排除已选择的要素
            try
            {
                var allFilter = new QueryFilter();
                var currentSelection = featureLayer.GetSelection();

                // 如果有选择，则选择未选中的要素
                if (currentSelection.GetCount() > 0)
                {
                    // 获取选中要素的OID列表
                    var selectedOIDs = new List<long>();
                    using (var cursor = currentSelection.Search())
                    {
                        while (cursor.MoveNext())
                        {
                            using (var feature = cursor.Current)
                            {
                                selectedOIDs.Add(feature.GetObjectID());
                            }
                        }
                    }

                    // 构建排除已选择要素的查询
                    if (selectedOIDs.Count > 0)
                    {
                        var oidFieldName = featureLayer.GetFeatureClass().GetDefinition().GetObjectIDField();
                        var whereClause = $"{oidFieldName} NOT IN ({string.Join(",", selectedOIDs)})";
                        allFilter.WhereClause = whereClause;
                    }
                }

                featureLayer.Select(allFilter, SelectionCombinationMethod.New);
                var newCount = featureLayer.SelectionCount;

                var result = new
                {
                    layer_name = featureLayer.Name,
                    previous_selected = previousCount,
                    new_selected = newCount,
                    total_features = featureLayer.GetFeatureClass().GetCount()
                };

                return FunctionResult.CreateSuccessWithData(
                    functionCall.Id,
                    functionCall.Name,
                    result,
                    $"成功反选图层 {layerName}，选择数量从 {previousCount} 变为 {newCount}"
                );
            }
            catch (Exception ex)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"反选操作失败: {ex.Message}"
                );
            }
        }

        /// <summary>
        /// 要素选择参数模型
        /// </summary>
        private class FeatureSelectionParameters
        {
            [JsonProperty("action")]
            public string Action { get; set; }

            [JsonProperty("layer_name")]
            public string LayerName { get; set; }

            [JsonProperty("where_clause")]
            public string WhereClause { get; set; }

            [JsonProperty("selection_type")]
            public string SelectionType { get; set; } = "new";
        }
    }
}
