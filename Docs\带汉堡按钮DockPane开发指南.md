# 带汉堡按钮DockPane开发指南

## 概述

本指南介绍如何在ArcGIS Pro加载项中创建带有标准汉堡按钮的DockPane界面。汉堡按钮提供了一个标准的菜单入口，符合ArcGIS Pro的界面设计规范。

## 核心组件

### 1. XAML界面设计

使用标准的ArcGIS Pro控件来实现汉堡按钮：

```xml
<UserControl x:Class="XIAOFUTools.Tools.YourDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             xmlns:controls="clr-namespace:ArcGIS.Desktop.Framework.Controls;assembly=ArcGIS.Desktop.Framework">

    <!-- 标题栏 - 使用标准DockPane头部 -->
    <DockPanel Grid.Row="0" LastChildFill="true" KeyboardNavigation.TabNavigation="Local" Height="30">
        <controls:BurgerButton DockPanel.Dock="Right"
                  ToolTip="{Binding BurgerButtonTooltip}"
                  PopupMenu="{Binding BurgerButtonMenu}"/>
        <TextBlock Text="{Binding Heading}" Style="{DynamicResource Esri_TextBlockDockPaneHeader}">
            <TextBlock.ToolTip>
                <WrapPanel Orientation="Vertical" MaxWidth="300">
                    <TextBlock Text="{Binding Heading}" TextWrapping="Wrap"/>
                </WrapPanel>
            </TextBlock.ToolTip>
        </TextBlock>
    </DockPanel>
</UserControl>
```

### 2. ViewModel实现

在DockPane的ViewModel中实现汉堡按钮相关属性：

```csharp
internal class YourDockPaneViewModel : DockPane
{
    private const string _dockPaneID = "YourAddIn_YourDockPane";
    private const string _menuID = "YourAddIn_YourDockPane_Menu";

    /// <summary>
    /// DockPane标题
    /// </summary>
    private string _heading = "您的DockPane标题";
    public string Heading
    {
        get => _heading;
        set => SetProperty(ref _heading, value);
    }

    #region Burger Button

    /// <summary>
    /// 汉堡按钮提示文本
    /// </summary>
    public string BurgerButtonTooltip
    {
        get { return "菜单选项"; }
    }

    /// <summary>
    /// 汉堡按钮菜单
    /// </summary>
    public System.Windows.Controls.ContextMenu BurgerButtonMenu
    {
        get { return FrameworkApplication.CreateContextMenu(_menuID); }
    }
    #endregion
}
```

### 3. 菜单按钮实现

为汉堡菜单中的每个选项创建对应的按钮类：

```csharp
/// <summary>
/// 菜单选项1按钮实现
/// </summary>
internal class YourDockPane_MenuOption1Button : Button
{
    protected override void OnClick()
    {
        var dockPane = FrameworkApplication.DockPaneManager.Find("YourAddIn_YourDockPane");
        if (dockPane is YourDockPaneViewModel viewModel)
        {
            // 执行菜单选项1的逻辑
            viewModel.MenuOption1Command.Execute(null);
        }
    }
}

/// <summary>
/// 菜单选项2按钮实现
/// </summary>
internal class YourDockPane_MenuOption2Button : Button
{
    protected override void OnClick()
    {
        var dockPane = FrameworkApplication.DockPaneManager.Find("YourAddIn_YourDockPane");
        if (dockPane is YourDockPaneViewModel viewModel)
        {
            // 执行菜单选项2的逻辑
            viewModel.MenuOption2Command.Execute(null);
        }
    }
}
```

### 4. Config.daml配置

在Config.daml文件中配置汉堡菜单：

```xml
<!-- 在controls部分添加菜单按钮 -->
<controls>
    <button id="YourAddIn_YourDockPane_MenuOption1Button" 
            caption="菜单选项1" 
            className="YourNamespace.YourDockPane_MenuOption1Button" 
            loadOnClick="true" 
            smallImage="Images\YourIcon_16.png" 
            largeImage="Images\YourIcon_32.png">
        <tooltip heading="菜单选项1">选项1的描述<disabledText /></tooltip>
    </button>
    
    <button id="YourAddIn_YourDockPane_MenuOption2Button" 
            caption="菜单选项2" 
            className="YourNamespace.YourDockPane_MenuOption2Button" 
            loadOnClick="true" 
            smallImage="Images\YourIcon_16.png" 
            largeImage="Images\YourIcon_32.png">
        <tooltip heading="菜单选项2">选项2的描述<disabledText /></tooltip>
    </button>
</controls>

<!-- 在menus部分定义汉堡菜单 -->
<menus>
    <menu id="YourAddIn_YourDockPane_Menu" caption="菜单选项" contextMenu="true">
        <button refID="YourAddIn_YourDockPane_MenuOption1Button" />
        <button refID="YourAddIn_YourDockPane_MenuOption2Button" />
    </menu>
</menus>
```

## 实际应用示例：AI助手

以下是AI助手DockPane的实际实现示例：

### 汉堡菜单功能
- **新建对话**：创建新的对话会话，清除记忆
- **历史会话**：查看历史对话记录（功能即将推出）

### 关键特性
1. **标准化界面**：使用ArcGIS Pro标准控件，保持界面一致性
2. **功能集成**：汉堡菜单集成了常用的对话管理功能
3. **用户体验**：符合用户对ArcGIS Pro界面的使用习惯

## 开发要点

### 1. 命名规范
- DockPane ID：`YourAddIn_YourDockPane`
- 菜单 ID：`YourAddIn_YourDockPane_Menu`
- 按钮 ID：`YourAddIn_YourDockPane_MenuOptionButton`

### 2. 样式一致性
- 使用 `Esri_TextBlockDockPaneHeader` 样式
- 保持30像素的标题栏高度
- 使用标准的ArcGIS Pro图标

### 3. 功能实现
- 通过DockPaneManager获取DockPane实例
- 使用Command模式执行菜单操作
- 保持ViewModel的职责单一

## 总结

带汉堡按钮的DockPane设计提供了：
- 标准化的用户界面
- 灵活的菜单扩展能力
- 良好的用户体验
- 符合ArcGIS Pro设计规范

通过遵循本指南，可以创建出专业、一致的DockPane界面，提升用户的使用体验。
