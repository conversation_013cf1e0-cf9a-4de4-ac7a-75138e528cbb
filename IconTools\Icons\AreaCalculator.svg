<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 统一蓝色渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6c5ce7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a29bfe;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="32" height="32" rx="8" fill="url(#bgGradient)"/>

  <!-- 多边形区域 -->
  <path d="M6 8 L26 6 L28 16 L24 26 L8 28 L4 18 Z" fill="#ffffff" opacity="0.9"/>

  <!-- 计算符号 -->
  <circle cx="16" cy="16" r="6" fill="#6c5ce7" opacity="0.9"/>
  <text x="16" y="20" fill="#ffffff" font-family="SF Pro, Arial, sans-serif" font-size="8" font-weight="bold" text-anchor="middle">A</text>

  <!-- 装饰线条 -->
  <line x1="8" y1="12" x2="12" y2="12" stroke="#6c5ce7" stroke-width="2" opacity="0.6"/>
  <line x1="20" y1="10" x2="24" y2="10" stroke="#6c5ce7" stroke-width="2" opacity="0.6"/>
  <line x1="10" y1="22" x2="14" y2="22" stroke="#6c5ce7" stroke-width="2" opacity="0.6"/>
</svg>