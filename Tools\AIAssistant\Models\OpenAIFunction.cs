using System.Collections.Generic;
using Newtonsoft.Json;

namespace XIAOFUTools.Tools.AIAssistant.Models
{
    /// <summary>
    /// OpenAI Function定义模型
    /// 符合OpenAI Function Calling规范
    /// </summary>
    public class OpenAIFunction
    {
        /// <summary>
        /// 函数名称
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// 函数描述
        /// </summary>
        [JsonProperty("description")]
        public string Description { get; set; }

        /// <summary>
        /// 函数参数架构（JSON Schema格式）
        /// </summary>
        [JsonProperty("parameters")]
        public FunctionParameters Parameters { get; set; }

        public OpenAIFunction()
        {
            Parameters = new FunctionParameters();
        }

        /// <summary>
        /// 创建OpenAI函数定义
        /// </summary>
        /// <param name="name">函数名称</param>
        /// <param name="description">函数描述</param>
        /// <param name="parameters">参数定义</param>
        /// <returns>OpenAI函数定义</returns>
        public static OpenAIFunction Create(string name, string description, FunctionParameters parameters = null)
        {
            return new OpenAIFunction
            {
                Name = name,
                Description = description,
                Parameters = parameters ?? new FunctionParameters()
            };
        }
    }

    /// <summary>
    /// 函数参数定义
    /// </summary>
    public class FunctionParameters
    {
        /// <summary>
        /// 参数类型（通常为"object"）
        /// </summary>
        [JsonProperty("type")]
        public string Type { get; set; } = "object";

        /// <summary>
        /// 参数属性定义
        /// </summary>
        [JsonProperty("properties")]
        public Dictionary<string, ParameterProperty> Properties { get; set; }

        /// <summary>
        /// 必需的参数列表
        /// </summary>
        [JsonProperty("required")]
        public List<string> Required { get; set; }

        public FunctionParameters()
        {
            Properties = new Dictionary<string, ParameterProperty>();
            Required = new List<string>();
        }

        /// <summary>
        /// 添加参数属性
        /// </summary>
        /// <param name="name">参数名称</param>
        /// <param name="property">参数属性</param>
        /// <param name="isRequired">是否必需</param>
        public void AddProperty(string name, ParameterProperty property, bool isRequired = false)
        {
            Properties[name] = property;
            if (isRequired && !Required.Contains(name))
            {
                Required.Add(name);
            }
        }
    }

    /// <summary>
    /// 参数属性定义
    /// </summary>
    public class ParameterProperty
    {
        /// <summary>
        /// 参数类型
        /// </summary>
        [JsonProperty("type")]
        public string Type { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        [JsonProperty("description")]
        public string Description { get; set; }

        /// <summary>
        /// 枚举值（如果适用）
        /// </summary>
        [JsonProperty("enum", NullValueHandling = NullValueHandling.Ignore)]
        public List<string> Enum { get; set; }

        /// <summary>
        /// 默认值（如果适用）
        /// </summary>
        [JsonProperty("default", NullValueHandling = NullValueHandling.Ignore)]
        public object Default { get; set; }

        /// <summary>
        /// 最小值（数值类型）
        /// </summary>
        [JsonProperty("minimum", NullValueHandling = NullValueHandling.Ignore)]
        public double? Minimum { get; set; }

        /// <summary>
        /// 最大值（数值类型）
        /// </summary>
        [JsonProperty("maximum", NullValueHandling = NullValueHandling.Ignore)]
        public double? Maximum { get; set; }

        /// <summary>
        /// 创建字符串类型参数
        /// </summary>
        /// <param name="description">参数描述</param>
        /// <param name="enumValues">枚举值</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>参数属性</returns>
        public static ParameterProperty CreateString(string description, List<string> enumValues = null, string defaultValue = null)
        {
            return new ParameterProperty
            {
                Type = "string",
                Description = description,
                Enum = enumValues,
                Default = defaultValue
            };
        }

        /// <summary>
        /// 创建布尔类型参数
        /// </summary>
        /// <param name="description">参数描述</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>参数属性</returns>
        public static ParameterProperty CreateBoolean(string description, bool? defaultValue = null)
        {
            return new ParameterProperty
            {
                Type = "boolean",
                Description = description,
                Default = defaultValue
            };
        }

        /// <summary>
        /// 创建数值类型参数
        /// </summary>
        /// <param name="description">参数描述</param>
        /// <param name="minimum">最小值</param>
        /// <param name="maximum">最大值</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>参数属性</returns>
        public static ParameterProperty CreateNumber(string description, double? minimum = null, double? maximum = null, double? defaultValue = null)
        {
            return new ParameterProperty
            {
                Type = "number",
                Description = description,
                Minimum = minimum,
                Maximum = maximum,
                Default = defaultValue
            };
        }

        /// <summary>
        /// 创建整数类型参数
        /// </summary>
        /// <param name="description">参数描述</param>
        /// <param name="minimum">最小值</param>
        /// <param name="maximum">最大值</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>参数属性</returns>
        public static ParameterProperty CreateInteger(string description, int? minimum = null, int? maximum = null, int? defaultValue = null)
        {
            return new ParameterProperty
            {
                Type = "integer",
                Description = description,
                Minimum = minimum,
                Maximum = maximum,
                Default = defaultValue
            };
        }
    }
}
