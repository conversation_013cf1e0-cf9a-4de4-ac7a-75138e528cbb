using System;
using System.IO;
using Microsoft.Win32;

namespace XIAOFUTools.Common
{
    /// <summary>
    /// 纯WPF文件夹选择器，不依赖Windows Forms
    /// 使用Microsoft.Win32.OpenFileDialog实现文件夹选择功能
    /// </summary>
    public class WpfFolderBrowserDialog
    {
        public string Description { get; set; } = "选择文件夹";
        public string SelectedPath { get; set; } = "";
        public bool ShowNewFolderButton { get; set; } = true;

        /// <summary>
        /// 显示文件夹选择对话框
        /// </summary>
        /// <returns>如果用户选择了文件夹返回true，否则返回false</returns>
        public bool ShowDialog()
        {
            try
            {
                // 使用OpenFileDialog的文件夹选择模式
                var dialog = new OpenFileDialog
                {
                    Title = Description,
                    CheckFileExists = false,
                    CheckPathExists = true,
                    FileName = "选择此文件夹",
                    Filter = "文件夹选择|*.folder",
                    ValidateNames = false,
                    Multiselect = false
                };

                // 设置初始目录
                if (!string.IsNullOrEmpty(SelectedPath) && Directory.Exists(SelectedPath))
                {
                    dialog.InitialDirectory = SelectedPath;
                }
                else if (!string.IsNullOrEmpty(SelectedPath))
                {
                    var parentDir = Path.GetDirectoryName(SelectedPath);
                    if (!string.IsNullOrEmpty(parentDir) && Directory.Exists(parentDir))
                    {
                        dialog.InitialDirectory = parentDir;
                    }
                }

                // 显示对话框
                if (dialog.ShowDialog() == true)
                {
                    // 获取选择的文件夹路径
                    SelectedPath = Path.GetDirectoryName(dialog.FileName);
                    return !string.IsNullOrEmpty(SelectedPath);
                }
            }
            catch (Exception ex)
            {
                // 记录异常但不抛出，返回false表示用户取消或出错
                System.Diagnostics.Debug.WriteLine($"文件夹选择对话框异常: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 静态方法：快速显示文件夹选择对话框
        /// </summary>
        /// <param name="description">对话框描述</param>
        /// <param name="initialPath">初始路径</param>
        /// <returns>选择的文件夹路径，如果取消则返回null</returns>
        public static string ShowFolderDialog(string description = "选择文件夹", string initialPath = "")
        {
            var dialog = new WpfFolderBrowserDialog
            {
                Description = description,
                SelectedPath = initialPath
            };

            return dialog.ShowDialog() ? dialog.SelectedPath : null;
        }
    }
}
