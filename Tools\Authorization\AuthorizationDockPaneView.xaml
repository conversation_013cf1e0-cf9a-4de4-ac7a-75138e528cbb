<UserControl x:Class="XIAOFUTools.Tools.Authorization.AuthorizationDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.Authorization"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="350">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 布尔值转可见性转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- 布尔值反转转换器 -->
            <local:BooleanInverseConverter x:Key="BooleanInverseConverter"/>

        <!-- 样式定义 -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,0,0,6"/>
            <Setter Property="Foreground" Value="{DynamicResource Esri_TextControlBrush}"/>
        </Style>

        <Style x:Key="LabelTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Margin" Value="0,2,0,2"/>
            <Setter Property="Foreground" Value="{DynamicResource Esri_TextControlBrush}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <Style x:Key="ValueTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Margin" Value="0,1,0,3"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Foreground" Value="{DynamicResource Esri_TextControlBrush}"/>
        </Style>

        <Style x:Key="StatusTextStyle" TargetType="TextBlock" BasedOn="{StaticResource ValueTextStyle}">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>

        <Style x:Key="SectionBorderStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="{DynamicResource Esri_BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="2"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="Background" Value="{DynamicResource Esri_ControlBackgroundBrush}"/>
        </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="8">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

        <!-- 授权状态区域 -->
        <Border Grid.Row="0" Style="{StaticResource SectionBorderStyle}" Margin="0,0,0,6">
            <StackPanel>
                <TextBlock Text="授权状态" Style="{StaticResource LabelTextStyle}" Margin="0,0,0,4"/>
                <TextBlock Text="{Binding AuthStatus.Message}"
                          Style="{StaticResource StatusTextStyle}" Margin="0,0,0,6">
                    <TextBlock.Foreground>
                        <SolidColorBrush Color="{Binding AuthStatus.StatusColor}"/>
                    </TextBlock.Foreground>
                </TextBlock>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Margin="0,0,4,0">
                        <TextBlock Text="过期时间" Style="{StaticResource LabelTextStyle}" Margin="0,0,0,2"/>
                        <TextBlock Text="{Binding AuthStatus.FormattedExpireTime}" Style="{StaticResource ValueTextStyle}" FontSize="10"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Margin="4,0,0,0">
                        <TextBlock Text="剩余时间" Style="{StaticResource LabelTextStyle}" Margin="0,0,0,2"/>
                        <TextBlock Text="{Binding AuthStatus.RemainingTimeDescription}" Style="{StaticResource ValueTextStyle}" FontSize="10"/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </Border>

        <!-- 机器码区域 -->
        <Border Grid.Row="1" Style="{StaticResource SectionBorderStyle}" Margin="0,0,0,6">
            <StackPanel>
                <Grid>
                    <TextBlock Text="机器码" Style="{StaticResource LabelTextStyle}" HorizontalAlignment="Left"/>
                    <TextBlock Text="发送给管理员获取授权码" Style="{StaticResource LabelTextStyle}"
                              FontSize="10" Foreground="Gray" HorizontalAlignment="Right"/>
                </Grid>
                <TextBox Text="{Binding AuthStatus.MachineCode, Mode=OneWay}"
                        IsReadOnly="True"
                        TextWrapping="Wrap"
                        Height="50"
                        FontSize="10"
                        VerticalScrollBarVisibility="Auto"
                        Margin="0,2,0,4"/>
                <Button Content="复制机器码"
                       Command="{Binding CopyMachineCodeCommand}"
                       Style="{DynamicResource Esri_Button}"
                       HorizontalAlignment="Right"
                       Padding="10,4"
                       FontSize="11"/>
            </StackPanel>
        </Border>

        <!-- 授权码输入区域 -->
        <Border Grid.Row="2" Style="{StaticResource SectionBorderStyle}" Margin="0,0,0,6">
            <StackPanel>
                <Grid>
                    <TextBlock Text="授权码输入" Style="{StaticResource LabelTextStyle}" HorizontalAlignment="Left"/>
                    <TextBlock Text="从管理员处获得" Style="{StaticResource LabelTextStyle}"
                              FontSize="10" Foreground="Gray" HorizontalAlignment="Right"/>
                </Grid>
                <TextBox Text="{Binding InputAuthCode, UpdateSourceTrigger=PropertyChanged}"
                        Height="60"
                        TextWrapping="Wrap"
                        FontSize="10"
                        VerticalScrollBarVisibility="Auto"
                        AcceptsReturn="True"
                        Margin="0,2,0,6"/>

                <!-- 按钮区域 -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Button Grid.Column="1" Content="授权"
                           Command="{Binding AuthorizeCommand}"
                           IsEnabled="{Binding IsProcessing, Converter={StaticResource BooleanInverseConverter}}"
                           Style="{DynamicResource Esri_Button}"
                           Padding="10,4"
                           FontSize="11"
                           Width="Auto"
                           Margin="0,0,6,0"/>

                    <Button Grid.Column="2" Content="清空"
                           Command="{Binding ClearInputCommand}"
                           Style="{DynamicResource Esri_Button}"
                           Padding="10,4"
                           FontSize="11"
                           Width="Auto"
                           Visibility="{Binding IsNotAuthorized, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- 授权管理区域 -->
        <Border Grid.Row="3" Style="{StaticResource SectionBorderStyle}">
            <StackPanel>
                <!-- 操作按钮区域 -->
                <Grid Margin="0,0,0,8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 刷新按钮 -->
                    <Button Grid.Column="0" Content="刷新状态"
                           Command="{Binding RefreshCommand}"
                           IsEnabled="{Binding IsProcessing, Converter={StaticResource BooleanInverseConverter}}"
                           Style="{DynamicResource Esri_Button}"
                           Padding="10,4"
                           FontSize="11"
                           Margin="0,0,3,0"/>

                    <!-- 取消授权按钮 -->
                    <Button Grid.Column="1" Content="取消授权"
                           Command="{Binding RevokeAuthorizationCommand}"
                           IsEnabled="{Binding IsProcessing, Converter={StaticResource BooleanInverseConverter}}"
                           Style="{DynamicResource Esri_Button}"
                           Padding="10,4"
                           FontSize="11"
                           Background="#FF6B6B"
                           Foreground="White"
                           FontWeight="Bold"
                           Margin="3,0,0,0"
                           Visibility="{Binding IsAuthorized, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </Grid>

                <!-- 未授权时的提示 -->
                <StackPanel Visibility="{Binding IsNotAuthorized, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="请输入授权码完成授权"
                              Style="{StaticResource ValueTextStyle}"
                              FontSize="11"
                              Foreground="Gray"
                              HorizontalAlignment="Center"
                              Margin="0,0,0,8"/>
                </StackPanel>



                <!-- 底部说明信息 -->
                <Border BorderBrush="{DynamicResource Esri_BorderBrush}"
                       BorderThickness="1"
                       CornerRadius="2"
                       Padding="8"
                       Background="{DynamicResource Esri_ControlBackgroundBrush}">
                    <StackPanel>
                        <TextBox Text="作者: XIAOFU"
                                FontSize="10"
                                FontWeight="Bold"
                                HorizontalAlignment="Center"
                                Margin="0,0,0,4"
                                IsReadOnly="True"
                                BorderThickness="0"
                                Background="Transparent"
                                TextAlignment="Center"
                                Cursor="IBeam"/>

                        <TextBox Text="QQ: 1922759464"
                                FontSize="9"
                                HorizontalAlignment="Center"
                                Margin="0,0,0,2"
                                IsReadOnly="True"
                                BorderThickness="0"
                                Background="Transparent"
                                TextAlignment="Center"
                                Cursor="IBeam"/>

                        <TextBox Text="Q群: 967758553"
                                FontSize="9"
                                HorizontalAlignment="Center"
                                Margin="0,0,0,6"
                                IsReadOnly="True"
                                BorderThickness="0"
                                Background="Transparent"
                                TextAlignment="Center"
                                Cursor="IBeam"/>

                        <TextBlock Text="授权信息:"
                                  Style="{StaticResource LabelTextStyle}"
                                  FontSize="10"
                                  FontWeight="Bold"
                                  Margin="0,0,0,4"/>

                        <TextBox FontSize="9"
                                TextWrapping="Wrap"
                                Margin="0,0,0,0"
                                IsReadOnly="True"
                                BorderThickness="0"
                                Background="Transparent"
                                Cursor="IBeam"
                                Text="1. 机器码用于唯一标识设备，请勿泄露。&#x0A;2. 授权码为工具箱的使用凭证(自带有试用码)。&#x0A;3. 授权状态显示当前工具的激活情况。&#x0A;4. 剩余天数显示授权的有效期限。&#x0A;5. 如果授权状态为未激活请联系作者获取授权。"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
