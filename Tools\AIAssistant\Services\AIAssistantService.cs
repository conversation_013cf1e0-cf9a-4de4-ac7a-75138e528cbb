using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using XIAOFUTools.Tools.AIAssistant.Models;
using XIAOFUTools.Tools.AIAssistant.Prompts;

namespace XIAOFUTools.Tools.AIAssistant.Services
{
    /// <summary>
    /// AI助手服务核心类
    /// </summary>
    public class AIAssistantService
    {
        private readonly HttpClient _httpClient;
        private readonly AIServiceConfig _config;

        public AIAssistantService()
        {
            // 创建支持流式响应的HttpClient
            var handler = new HttpClientHandler();
            _httpClient = new HttpClient(handler);
            _config = ConfigurationManager.GetAIServiceConfig();

            // 设置HTTP客户端
            _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_config.ApiKey}");
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "XIAOFU-Tools-AI-Assistant/1.0");

            System.Diagnostics.Debug.WriteLine($"初始化Moonshot API客户端，模型: {_config.ModelName} (Kimi K2)，流式输出: {_config.EnableStreaming}");
        }

        /// <summary>
        /// 获取AI回复
        /// </summary>
        /// <param name="userMessage">用户消息</param>
        /// <param name="conversationHistory">对话历史</param>
        /// <param name="onStreamUpdate">流式更新回调</param>
        /// <param name="mode">对话模式</param>
        /// <param name="enableFunctions">是否启用函数调用</param>
        /// <returns>完整的AI回复</returns>
        public async Task<string> GetResponseAsync(string userMessage, ChatMessage[] conversationHistory, Action<string> onStreamUpdate = null, string mode = "chat", bool enableFunctions = false)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始处理请求 - 模式: {mode}, 启用函数: {enableFunctions}");

                // 构建请求消息列表
                var messages = await BuildMessageListAsync(userMessage, conversationHistory, mode);
                System.Diagnostics.Debug.WriteLine($"消息列表构建完成，共 {messages.Count} 条消息");

                // 如果启用函数调用，进入函数调用循环
                if (mode == "agent" && enableFunctions)
                {
                    return await ProcessWithFunctionCalling(messages, onStreamUpdate);
                }
                else
                {
                    // 普通对话模式
                    return await ProcessNormalConversation(messages, onStreamUpdate, mode, enableFunctions);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AI服务调用异常: {ex.GetType().Name} - {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");

                if (ex is System.IO.FileNotFoundException fileEx)
                {
                    System.Diagnostics.Debug.WriteLine($"文件未找到: {fileEx.FileName}");
                }

                throw new Exception($"AI服务调用失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理智能Agent对话（类似Cursor的智能执行）
        /// </summary>
        private async Task<string> ProcessWithFunctionCalling(List<ChatMessage> messages, Action<string> onStreamUpdate)
        {
            System.Diagnostics.Debug.WriteLine("🤖 启动智能GIS Agent - 类似Cursor的智能执行模式");

            // 显示Agent思考过程
            var thinkingMessage = "🧠 **Analyzing your request and current GIS context...**\n";
            onStreamUpdate?.Invoke(thinkingMessage);

            var maxIterations = 10; // 防止无限循环
            var iteration = 0;
            var finalResponse = new StringBuilder();
            var conversationMessages = new List<ChatMessage>(messages);
            var executionPlan = new StringBuilder();

            while (iteration < maxIterations)
            {
                iteration++;
                System.Diagnostics.Debug.WriteLine($"🔄 智能执行轮次 {iteration}");

                // 构建当前轮次的请求体
                var requestBody = BuildRequestBody(conversationMessages, "agent", true);

                // 发送请求并处理响应
                var (content, toolCalls, shouldContinue) = await ProcessAgentIteration(requestBody as Dictionary<string, object>, onStreamUpdate);

                // 显示AI的思考过程
                if (!string.IsNullOrEmpty(content))
                {
                    // 如果内容包含思考过程，特殊格式化
                    var formattedContent = FormatAgentThinking(content, iteration);
                    finalResponse.AppendLine(formattedContent);

                    conversationMessages.Add(new ChatMessage
                    {
                        Role = "assistant",
                        Content = content
                    });
                }

                // 如果没有工具调用，AI认为任务完成
                if (toolCalls == null || toolCalls.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("✅ Agent判断任务完成，目标已达成");

                    // 显示完成总结
                    var completionSummary = "\n🎯 **Task completed successfully!**\n";
                    finalResponse.AppendLine(completionSummary);
                    onStreamUpdate?.Invoke(completionSummary);
                    break;
                }

                // 显示执行计划
                var planMessage = $"📋 **Execution Plan (Step {iteration}):**\n";
                planMessage += $"→ Calling {toolCalls.Count} tool{(toolCalls.Count > 1 ? "s" : "")} to gather information\n";
                onStreamUpdate?.Invoke(planMessage);

                // 执行工具调用
                var functionResults = await ExecuteToolCallsWithIntelligentFeedback(toolCalls, onStreamUpdate, iteration);

                // 将工具执行结果添加到对话历史
                foreach (var result in functionResults)
                {
                    conversationMessages.Add(new ChatMessage
                    {
                        Role = "tool",
                        Content = JsonConvert.SerializeObject(new
                        {
                            tool_call_id = result.Id,
                            name = result.Name,
                            content = result.Content
                        })
                    });
                }

                // 让AI判断是否需要继续
                if (!shouldContinue)
                {
                    System.Diagnostics.Debug.WriteLine("AI判断无需继续执行");
                    break;
                }
            }

            if (iteration >= maxIterations)
            {
                var warning = "\n⚠️ 达到最大迭代次数，停止执行\n";
                finalResponse.AppendLine(warning);
                onStreamUpdate?.Invoke(warning);
            }

            System.Diagnostics.Debug.WriteLine($"Agent多轮处理完成，共执行 {iteration} 轮");
            return finalResponse.ToString();
        }

        /// <summary>
        /// 格式化Agent思考过程（类似Cursor的思考显示）
        /// </summary>
        private string FormatAgentThinking(string content, int iteration)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            // 检测是否包含思考关键词
            var thinkingKeywords = new[] { "analyzing", "thinking", "considering", "planning", "strategy", "approach" };
            var isThinking = thinkingKeywords.Any(keyword => content.ToLower().Contains(keyword));

            if (isThinking && iteration == 1)
            {
                return $"💭 **Agent Analysis:**\n{content}\n";
            }
            else if (content.Contains("executing") || content.Contains("calling"))
            {
                return $"⚡ **Executing:**\n{content}\n";
            }
            else
            {
                return content;
            }
        }

        /// <summary>
        /// 处理单轮Agent迭代
        /// </summary>
        private async Task<(string content, List<JObject> toolCalls, bool shouldContinue)> ProcessAgentIteration(
            Dictionary<string, object> requestBody,
            Action<string> onStreamUpdate)
        {
            // 对于Agent模式，暂时使用非流式处理以简化工具调用解析
            var modifiedRequestBody = new Dictionary<string, object>(requestBody);
            modifiedRequestBody["stream"] = false;

            string jsonRequest = JsonConvert.SerializeObject(modifiedRequestBody);
            System.Diagnostics.Debug.WriteLine($"Agent迭代请求: {jsonRequest}");

            var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            using (var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiEndpoint}/chat/completions"))
            {
                request.Content = content;

                using (var response = await _httpClient.SendAsync(request))
                {
                    if (!response.IsSuccessStatusCode)
                    {
                        string errorContent = await response.Content.ReadAsStringAsync();
                        throw new Exception($"API请求失败: {response.StatusCode} - {errorContent}");
                    }

                    var responseContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"Agent迭代响应: {responseContent}");

                    // 解析响应
                    var responseData = JObject.Parse(responseContent);
                    var choice = responseData["choices"]?[0];
                    var message = choice?["message"];

                    if (message == null)
                    {
                        return (null, null, false);
                    }

                    var aiContent = message["content"]?.ToString();
                    var toolCallsArray = message["tool_calls"] as JArray;
                    var toolCalls = new List<JObject>();

                    // 解析工具调用
                    if (toolCallsArray != null)
                    {
                        foreach (var toolCall in toolCallsArray)
                        {
                            if (toolCall is JObject toolCallObj)
                            {
                                toolCalls.Add(toolCallObj);
                            }
                        }
                    }

                    // 如果有内容，流式显示
                    if (!string.IsNullOrEmpty(aiContent))
                    {
                        onStreamUpdate?.Invoke(aiContent);
                    }

                    // 判断是否应该继续（有工具调用就继续）
                    bool shouldContinue = toolCalls.Count > 0;

                    return (aiContent, toolCalls, shouldContinue);
                }
            }
        }

        /// <summary>
        /// 智能工具执行（类似Augment的智能反馈）
        /// </summary>
        private async Task<List<FunctionResult>> ExecuteToolCallsWithIntelligentFeedback(List<JObject> toolCalls, Action<string> onStreamUpdate, int iteration)
        {
            var results = new List<FunctionResult>();

            foreach (var toolCall in toolCalls)
            {
                try
                {
                    var functionName = toolCall["function"]?["name"]?.ToString();
                    var functionArgs = toolCall["function"]?["arguments"]?.ToString();
                    var toolCallId = toolCall["id"]?.ToString();

                    if (string.IsNullOrEmpty(functionName))
                        continue;

                    System.Diagnostics.Debug.WriteLine($"🔧 智能执行工具: {functionName}");

                    // 显示智能执行状态
                    var intelligentStatus = GetIntelligentToolDescription(functionName, functionArgs);
                    onStreamUpdate?.Invoke($"⚡ {intelligentStatus}\n");

                    // 创建函数调用对象
                    var functionCall = new FunctionCall
                    {
                        Id = toolCallId ?? Guid.NewGuid().ToString(),
                        Name = functionName,
                        Arguments = functionArgs
                    };

                    // 执行工具
                    var functionManager = OpenAIFunctionManager.Instance;
                    var result = await functionManager.ExecuteFunctionAsync(functionCall);

                    // 智能结果反馈
                    var intelligentFeedback = FormatIntelligentResult(result, functionName);
                    onStreamUpdate?.Invoke(intelligentFeedback);

                    results.Add(result);

                    System.Diagnostics.Debug.WriteLine($"✅ 工具执行完成: {functionName} - {result.Success}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ 工具执行异常: {ex.Message}");

                    var errorFeedback = $"⚠️ **Tool execution failed:** {ex.Message}\n";
                    onStreamUpdate?.Invoke(errorFeedback);

                    results.Add(new FunctionResult
                    {
                        Id = toolCall["id"]?.ToString() ?? Guid.NewGuid().ToString(),
                        Name = toolCall["function"]?["name"]?.ToString() ?? "unknown",
                        Success = false,
                        ErrorMessage = ex.Message,
                        Content = $"执行失败: {ex.Message}"
                    });
                }
            }

            return results;
        }

        /// <summary>
        /// 获取智能工具描述
        /// </summary>
        private string GetIntelligentToolDescription(string functionName, string args)
        {
            return functionName switch
            {
                "get_map_info" => "**Analyzing current map and layer configuration...**",
                "manage_layers" => "**Managing layer visibility and properties...**",
                "select_features" => "**Performing intelligent feature selection...**",
                "system_diagnostic" => "**Running system health diagnostics...**",
                _ => $"**Executing {functionName}...**"
            };
        }

        /// <summary>
        /// 格式化智能结果反馈
        /// </summary>
        private string FormatIntelligentResult(FunctionResult result, string functionName)
        {
            if (!result.Success)
            {
                return $"❌ **{functionName} failed:** {result.ErrorMessage}\n";
            }

            // 尝试解析结构化结果
            try
            {
                var resultData = JsonConvert.DeserializeObject<dynamic>(result.Content);
                if (resultData?.success == true && resultData?.data != null)
                {
                    return $"✅ **{functionName} completed** - {resultData.message}\n";
                }
            }
            catch
            {
                // 如果不是结构化数据，直接显示
            }

            return $"✅ **{functionName} completed successfully**\n";
        }

        /// <summary>
        /// 处理普通对话
        /// </summary>
        private async Task<string> ProcessNormalConversation(List<ChatMessage> messages, Action<string> onStreamUpdate, string mode, bool enableFunctions)
        {
            // 构建请求体
            var requestBody = BuildRequestBody(messages, mode, enableFunctions);

            string jsonRequest = JsonConvert.SerializeObject(requestBody);
            System.Diagnostics.Debug.WriteLine($"API请求: {jsonRequest}");

            var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            // 发送请求 - 使用HttpCompletionOption.ResponseHeadersRead确保流式响应
            using (var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiEndpoint}/chat/completions"))
            {
                request.Content = content;

                // 为流式请求设置必要的头部
                if (_config.EnableStreaming)
                {
                    request.Headers.Add("Accept", "text/event-stream");
                    request.Headers.Add("Cache-Control", "no-cache");
                }

                using (var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead))
                {
                    if (!response.IsSuccessStatusCode)
                    {
                        string errorContent = await response.Content.ReadAsStringAsync();
                        throw new Exception($"API请求失败: {response.StatusCode} - {errorContent}");
                    }

                    // 处理响应
                    if (_config.EnableStreaming)
                    {
                        return await ProcessStreamResponse(response, onStreamUpdate);
                    }
                    else
                    {
                        return await ProcessNormalResponse(response);
                    }
                }
            }
        }

        /// <summary>
        /// 发送流式API请求并处理函数调用
        /// </summary>
        private async Task<string> SendStreamAPIRequestWithFunctions(Dictionary<string, object> requestBody, Action<string> onStreamUpdate)
        {
            string jsonRequest = JsonConvert.SerializeObject(requestBody);
            System.Diagnostics.Debug.WriteLine($"API请求: {jsonRequest}");

            var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            using (var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiEndpoint}/chat/completions"))
            {
                request.Content = content;
                request.Headers.Add("Accept", "text/event-stream");
                request.Headers.Add("Cache-Control", "no-cache");

                using (var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead))
                {
                    if (!response.IsSuccessStatusCode)
                    {
                        string errorContent = await response.Content.ReadAsStringAsync();
                        throw new Exception($"API请求失败: {response.StatusCode} - {errorContent}");
                    }

                    return await ProcessStreamResponseWithFunctions(response, onStreamUpdate);
                }
            }
        }

        /// <summary>
        /// 处理包含函数调用的流式响应
        /// </summary>
        private async Task<string> ProcessStreamResponseWithFunctions(HttpResponseMessage response, Action<string> onStreamUpdate)
        {
            var finalResponse = new StringBuilder();
            var currentToolCalls = new List<JObject>();
            var currentContent = new StringBuilder();

            using (var stream = await response.Content.ReadAsStreamAsync())
            using (var reader = new StreamReader(stream))
            {
                string line;
                while ((line = await reader.ReadLineAsync()) != null)
                {
                    if (string.IsNullOrWhiteSpace(line) || !line.StartsWith("data: "))
                        continue;

                    string data = line.Substring(6); // 移除 "data: " 前缀

                    if (data == "[DONE]")
                        break;

                    try
                    {
                        var jsonData = JObject.Parse(data);
                        var choices = jsonData["choices"] as JArray;

                        if (choices != null && choices.Count > 0)
                        {
                            var choice = choices[0] as JObject;
                            var delta = choice?["delta"] as JObject;

                            if (delta != null)
                            {
                                // 处理内容流
                                var content = delta["content"]?.ToString();
                                if (!string.IsNullOrEmpty(content))
                                {
                                    currentContent.Append(content);
                                    onStreamUpdate?.Invoke(content);
                                }

                                // 处理工具调用
                                var toolCalls = delta["tool_calls"] as JArray;
                                if (toolCalls != null)
                                {
                                    foreach (var toolCall in toolCalls)
                                    {
                                        var toolCallObj = toolCall as JObject;
                                        if (toolCallObj != null)
                                        {
                                            // 累积工具调用信息
                                            AccumulateToolCall(currentToolCalls, toolCallObj);
                                        }
                                    }
                                }
                            }

                            // 检查是否完成
                            var finishReason = choice?["finish_reason"]?.ToString();
                            if (finishReason == "tool_calls")
                            {
                                // 执行函数调用
                                await ExecuteToolCallsAndContinue(currentToolCalls, currentContent, onStreamUpdate, finalResponse);
                                return finalResponse.ToString();
                            }
                            else if (finishReason == "stop")
                            {
                                finalResponse.Append(currentContent.ToString());
                                return finalResponse.ToString();
                            }
                        }
                    }
                    catch (JsonException ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"解析流式数据失败: {ex.Message}, 数据: {data}");
                    }
                }
            }

            finalResponse.Append(currentContent.ToString());
            return finalResponse.ToString();
        }

        /// <summary>
        /// 累积工具调用信息
        /// </summary>
        private void AccumulateToolCall(List<JObject> currentToolCalls, JObject toolCallDelta)
        {
            var index = toolCallDelta["index"]?.Value<int>() ?? 0;

            // 确保列表足够大
            while (currentToolCalls.Count <= index)
            {
                currentToolCalls.Add(new JObject());
            }

            var existingCall = currentToolCalls[index];

            // 合并工具调用信息
            if (toolCallDelta["id"] != null)
                existingCall["id"] = toolCallDelta["id"];
            if (toolCallDelta["type"] != null)
                existingCall["type"] = toolCallDelta["type"];

            var function = toolCallDelta["function"] as JObject;
            if (function != null)
            {
                if (existingCall["function"] == null)
                    existingCall["function"] = new JObject();

                var existingFunction = existingCall["function"] as JObject;

                if (function["name"] != null)
                    existingFunction["name"] = function["name"];

                if (function["arguments"] != null)
                {
                    var args = function["arguments"].ToString();
                    if (existingFunction["arguments"] == null)
                        existingFunction["arguments"] = args;
                    else
                        existingFunction["arguments"] = existingFunction["arguments"].ToString() + args;
                }
            }
        }

        /// <summary>
        /// 执行工具调用并继续对话
        /// </summary>
        private async Task ExecuteToolCallsAndContinue(List<JObject> toolCalls, StringBuilder currentContent, Action<string> onStreamUpdate, StringBuilder finalResponse)
        {
            // 添加当前内容到最终响应
            finalResponse.Append(currentContent.ToString());

            // 显示函数调用信息
            var functionInfo = $"\n🔧 正在执行 {toolCalls.Count} 个函数调用...\n";
            finalResponse.Append(functionInfo);
            onStreamUpdate?.Invoke(functionInfo);

            // 执行所有函数调用
            var functionResults = await ProcessFunctionCalls(new JArray(toolCalls.ToArray()));

            // 显示函数执行结果
            foreach (var result in functionResults)
            {
                var resultInfo = $"\n✅ 函数 {result.Name} 执行完成\n";
                if (result.Success)
                {
                    // 尝试解析结构化结果
                    try
                    {
                        var resultData = JObject.Parse(result.Content);
                        if (resultData["data"] != null)
                        {
                            resultInfo += $"结果: {resultData["message"]}\n";
                            // 可以添加更详细的结果显示
                        }
                        else
                        {
                            resultInfo += $"结果: {result.Content}\n";
                        }
                    }
                    catch
                    {
                        resultInfo += $"结果: {result.Content}\n";
                    }
                }
                else
                {
                    resultInfo += $"❌ 执行失败: {result.ErrorMessage}\n";
                }

                finalResponse.Append(resultInfo);
                onStreamUpdate?.Invoke(resultInfo);
            }

            // 添加总结信息
            var summary = $"\n📋 函数调用完成，共执行 {functionResults.Count} 个函数\n";
            finalResponse.Append(summary);
            onStreamUpdate?.Invoke(summary);
        }

        /// <summary>
        /// 处理函数调用
        /// </summary>
        private async Task<List<FunctionResult>> ProcessFunctionCalls(JToken toolCalls)
        {
            var results = new List<FunctionResult>();
            var functionManager = OpenAIFunctionManager.Instance;

            foreach (var toolCall in toolCalls)
            {
                try
                {
                    var callId = toolCall["id"]?.ToString();
                    var functionData = toolCall["function"];
                    var functionName = functionData?["name"]?.ToString();
                    var arguments = functionData?["arguments"]?.ToString();

                    System.Diagnostics.Debug.WriteLine($"执行函数调用: {functionName}({arguments})");

                    var functionCall = new FunctionCall
                    {
                        Id = callId,
                        Name = functionName,
                        Arguments = arguments
                    };

                    var result = await functionManager.ExecuteFunctionAsync(functionCall);
                    results.Add(result);

                    System.Diagnostics.Debug.WriteLine($"函数执行结果: {result.Success}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"函数调用失败: {ex.Message}");
                    results.Add(FunctionResult.CreateFailure(
                        toolCall["id"]?.ToString() ?? "unknown",
                        toolCall["function"]?["name"]?.ToString() ?? "unknown",
                        ex.Message
                    ));
                }
            }

            return results;
        }

        /// <summary>
        /// 构建消息列表
        /// </summary>
        private async Task<List<ChatMessage>> BuildMessageListAsync(string userMessage, ChatMessage[] conversationHistory, string mode = "chat")
        {
            var messages = new List<ChatMessage>();

            // 获取GIS上下文
            GISContext gisContext = null;
            try
            {
                System.Diagnostics.Debug.WriteLine("开始获取GIS上下文...");
                var gisService = new GISAgentService();
                gisContext = await gisService.GetCurrentGISContextAsync();
                System.Diagnostics.Debug.WriteLine($"GIS上下文获取成功: {gisContext?.MapName ?? "无地图"}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取GIS上下文失败: {ex.GetType().Name} - {ex.Message}");
                if (ex is System.IO.FileNotFoundException fileEx)
                {
                    System.Diagnostics.Debug.WriteLine($"GIS上下文获取时文件未找到: {fileEx.FileName}");
                }
            }

            // 生成智能系统提示词
            try
            {
                System.Diagnostics.Debug.WriteLine("开始生成系统提示词...");
                var systemPrompt = PromptManager.Instance.GenerateSystemPrompt(
                    mode: mode,
                    gisContext: gisContext,
                    userMessage: userMessage
                );
                System.Diagnostics.Debug.WriteLine($"系统提示词生成成功，长度: {systemPrompt?.Length ?? 0}");

                // 添加系统提示
                messages.Add(ChatMessage.CreateSystemMessage(systemPrompt));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"生成系统提示词失败: {ex.GetType().Name} - {ex.Message}");
                if (ex is System.IO.FileNotFoundException fileEx)
                {
                    System.Diagnostics.Debug.WriteLine($"提示词生成时文件未找到: {fileEx.FileName}");
                }

                // 使用默认提示词
                var defaultPrompt = mode == "agent" ?
                    "你是ArcGIS Pro的智能助手，可以帮助用户处理GIS相关任务。" :
                    "你是ArcGIS Pro的专业GIS顾问，可以回答GIS相关问题。";
                messages.Add(ChatMessage.CreateSystemMessage(defaultPrompt));
            }

            // 添加对话历史（最近的几条）
            if (conversationHistory != null && conversationHistory.Length > 0)
            {
                // 只取最近的对话，避免token超限
                var recentHistory = conversationHistory.TakeLast(10).ToArray();
                messages.AddRange(recentHistory);
            }

            // 添加当前用户消息
            messages.Add(ChatMessage.CreateUserMessage(userMessage));

            return messages;
        }

        /// <summary>
        /// 处理流式响应
        /// </summary>
        private async Task<string> ProcessStreamResponse(HttpResponseMessage response, Action<string> onStreamUpdate)
        {
            var fullResponse = new StringBuilder();

            System.Diagnostics.Debug.WriteLine($"开始处理Kimi K2流式响应，Content-Type: {response.Content.Headers.ContentType}");

            using (var stream = await response.Content.ReadAsStreamAsync())
            using (var reader = new StreamReader(stream, Encoding.UTF8))
            {
                string line;
                int lineCount = 0;
                int contentCount = 0;

                while ((line = await reader.ReadLineAsync()) != null)
                {
                    lineCount++;

                    // 处理Server-Sent Events格式 - Moonshot API标准格式
                    if (line.StartsWith("data: "))
                    {
                        string data = line.Substring(6).Trim();
                        System.Diagnostics.Debug.WriteLine($"SSE数据[{lineCount}]: {data}");

                        // 检查流式响应结束标志
                        if (data == "[DONE]")
                        {
                            System.Diagnostics.Debug.WriteLine("Kimi K2流式响应完成");
                            break;
                        }

                        // 跳过空数据
                        if (string.IsNullOrWhiteSpace(data))
                        {
                            continue;
                        }

                        try
                        {
                            // 解析Moonshot API的响应格式
                            var jsonData = JsonConvert.DeserializeObject<dynamic>(data);

                            // 检查choices数组
                            if (jsonData?.choices != null && jsonData.choices.Count > 0)
                            {
                                var choice = jsonData.choices[0];
                                var delta = choice?.delta;

                                // 获取内容
                                string content = delta?.content?.ToString();

                                if (!string.IsNullOrEmpty(content))
                                {
                                    contentCount++;
                                    fullResponse.Append(content);
                                    System.Diagnostics.Debug.WriteLine($"流式内容[{contentCount}]: '{content}'");

                                    // 立即调用回调，实现真正的实时流式输出
                                    onStreamUpdate?.Invoke(content);
                                }

                                // 检查finish_reason
                                var finishReason = choice?.finish_reason?.ToString();
                                if (!string.IsNullOrEmpty(finishReason))
                                {
                                    System.Diagnostics.Debug.WriteLine($"完成原因: {finishReason}");
                                    if (finishReason == "stop")
                                    {
                                        break;
                                    }
                                }
                            }
                        }
                        catch (JsonException ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"JSON解析错误: {ex.Message}");
                            System.Diagnostics.Debug.WriteLine($"原始数据: {data}");
                            continue;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"处理流式数据时出错: {ex.Message}");
                            continue;
                        }
                    }
                    else if (string.IsNullOrWhiteSpace(line))
                    {
                        // SSE格式中的空行，继续处理
                        continue;
                    }
                    else if (line.StartsWith(":"))
                    {
                        // SSE注释行，忽略
                        continue;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"未识别的SSE行[{lineCount}]: {line}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Kimi K2流式处理完成:");
                System.Diagnostics.Debug.WriteLine($"- 总行数: {lineCount}");
                System.Diagnostics.Debug.WriteLine($"- 内容块数: {contentCount}");
                System.Diagnostics.Debug.WriteLine($"- 响应长度: {fullResponse.Length}字符");
            }

            return fullResponse.ToString();
        }

        /// <summary>
        /// 处理普通响应
        /// </summary>
        private async Task<string> ProcessNormalResponse(HttpResponseMessage response)
        {
            string responseContent = await response.Content.ReadAsStringAsync();
            var jsonResponse = JsonConvert.DeserializeObject<dynamic>(responseContent);
            
            return jsonResponse?.choices?[0]?.message?.content?.ToString() ?? "未收到有效回复";
        }

        /// <summary>
        /// 获取系统提示词
        /// </summary>
        private string GetSystemPrompt()
        {
            return @"你是XIAOFU工具箱的专业GIS助手，具备以下能力和特点：

## 身份定位
- 专业的地理信息系统(GIS)技术专家
- XIAOFU工具箱的官方AI助手
- 熟悉ArcGIS Pro和各种GIS操作

## 核心能力
1. **GIS知识专家**：精通GIS理论、空间分析、坐标系统、地图制图等
2. **技术咨询专家**：解答GIS相关的技术问题和概念
3. **操作指导专家**：提供详细的操作步骤和最佳实践
4. **问题解决专家**：诊断和解决GIS相关问题

## 回复风格
- 专业而友好，使用中文回复
- 提供具体可操作的建议
- 结构化回答，使用列表和步骤
- 适当使用emoji增强可读性
- 基于当前GIS环境上下文提供针对性建议

请根据用户的GIS相关问题提供专业、准确、实用的解答。";
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();
        }

        /// <summary>
        /// 构建API请求体
        /// </summary>
        /// <param name="messages">消息列表</param>
        /// <param name="mode">对话模式</param>
        /// <param name="enableFunctions">是否启用函数调用</param>
        /// <returns>请求体对象</returns>
        private object BuildRequestBody(List<ChatMessage> messages, string mode, bool enableFunctions)
        {
            var requestBody = new Dictionary<string, object>
            {
                ["model"] = _config.ModelName,
                ["messages"] = messages.Select(m => new { role = m.Role, content = m.Content }).ToArray(),
                ["max_tokens"] = _config.MaxTokens,
                ["temperature"] = _config.Temperature,
                ["stream"] = _config.EnableStreaming
            };

            // 如果是agent模式且启用了函数调用，添加functions和tool_choice
            if (mode == "agent" && enableFunctions)
            {
                var functionManager = OpenAIFunctionManager.Instance;
                var availableFunctions = functionManager.GetAvailableFunctions();

                if (availableFunctions.Any())
                {
                    // 添加tools数组（OpenAI新格式）
                    requestBody["tools"] = availableFunctions.Select(f => new
                    {
                        type = "function",
                        function = f
                    }).ToArray();

                    // 设置tool_choice为auto，让AI自动决定是否调用函数
                    requestBody["tool_choice"] = "auto";

                    System.Diagnostics.Debug.WriteLine($"启用函数调用，可用函数数量: {availableFunctions.Count}");
                    foreach (var func in availableFunctions)
                    {
                        System.Diagnostics.Debug.WriteLine($"- {func.Name}: {func.Description}");
                    }
                }
            }

            return requestBody;
        }
    }
}
