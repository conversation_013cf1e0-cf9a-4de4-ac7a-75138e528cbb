<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手测试页面</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="lib/highlight.min.css">
</head>
<body>
    <div id="app">
        <!-- 顶部控制栏 -->
        <div class="top-controls">
            <button class="control-btn" id="historyToggle" title="历史记录" onclick="handleHistoryClick()">
                <span>☰</span>
            </button>

            <!-- 中间状态信息 -->
            <div class="status-info">
                <div class="status-left">
                    <span class="status-indicator" id="statusIndicator">🟢</span>
                    <span class="status-text" id="statusText">测试模式</span>
                </div>
                <div class="status-right">
                    <span class="model-info">测试环境</span>
                </div>
            </div>

            <div class="menu-dropdown">
                <button class="control-btn menu-btn" id="menuButton" title="菜单" onclick="handleMenuClick()">
                    <span>☰</span>
                </button>
                <div class="dropdown-content" id="dropdownMenu">
                    <div class="dropdown-item" id="newChat" onclick="handleNewChatClick()">新建对话</div>
                    <div class="dropdown-item" id="clearChat" onclick="handleClearChatClick()">清空对话</div>
                </div>
            </div>

            <!-- 历史记录弹窗 -->
            <div class="history-bar" id="historyBar">
                <div class="history-content">
                    <div class="history-item active">📝 测试对话</div>
                </div>
            </div>
        </div>

        <!-- 聊天消息区域 -->
        <div id="chat-container">
            <div id="messages-area">
                <div id="welcome-message" class="message assistant-message">
                    <div class="message-content">
                        <div class="message-text">
                            <h4>🧪 AI助手测试模式</h4>
                            <p>这是测试页面，用于验证流式输出功能。</p>
                            <p>请输入消息测试流式输出效果。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area" id="inputArea">
            <!-- 工具调用指示器 -->
            <div class="tool-indicator" id="toolIndicator">
                <div class="tool-indicator-content">
                    <div class="loading-spinner"></div>
                    <span id="toolIndicatorText">正在调用工具...</span>
                </div>
            </div>

            <!-- 可拉伸分隔线 -->
            <div class="resize-handle" id="resizeHandle">
                <div class="resize-line"></div>
            </div>

            <!-- 输入容器 -->
            <div class="input-container">
                <!-- 文字输入区域（包含内嵌按钮） -->
                <div class="text-input-wrapper">
                    <textarea
                        id="messageInput"
                        placeholder="请输入测试消息..."
                        rows="1"
                        maxlength="2000"></textarea>

                    <!-- 输入框内的工具栏 -->
                    <div class="input-inline-toolbar">
                        <!-- 左侧：图片附件和模式切换 -->
                        <div class="inline-left">
                            <button class="inline-btn" id="attachBtn" title="添加图片">
                                <span>📎</span>
                            </button>
                            <div class="mode-switch-compact">
                                <span class="mode-label active" id="chatLabel">Chat</span>
                                <div class="switch-container">
                                    <input type="checkbox" id="modeSwitch" class="switch-input">
                                    <label for="modeSwitch" class="switch-label">
                                        <span class="switch-slider"></span>
                                    </label>
                                </div>
                                <span class="mode-label" id="agentLabel">Agent</span>
                            </div>
                        </div>

                        <!-- 右侧：发送/停止按钮 -->
                        <div class="inline-right">
                            <button class="inline-action-btn send-btn" id="sendButton" title="发送消息">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                </svg>
                            </button>
                            <button class="inline-action-btn stop-btn" id="stopButton" title="停止生成" style="display: none;">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M6 6h12v12H6z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loading-indicator" class="hidden">
            <div class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <span class="loading-text">AI正在思考...</span>
        </div>
    </div>

    <!-- JavaScript库 -->
    <script src="lib/marked.min.js"></script>
    <script src="lib/highlight.min.js"></script>
    
    <!-- 应用脚本 -->
    <script src="js/markdown-renderer.js"></script>
    <script src="js/stream-handler.js"></script>
    <script src="js/app.js"></script>
    
    <script>
        // 测试模式提示
        console.log('AI助手测试页面已加载');
        console.log('这是一个独立的测试页面，用于验证流式输出功能');
    </script>
</body>
</html>
