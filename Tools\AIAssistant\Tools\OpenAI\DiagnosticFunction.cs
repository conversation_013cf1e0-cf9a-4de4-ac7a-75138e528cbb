using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using XIAOFUTools.Tools.AIAssistant.Models;
using XIAOFUTools.Tools.AIAssistant.Tools.Base;
using XIAOFUTools.Tools.AIAssistant.Services;

namespace XIAOFUTools.Tools.AIAssistant.Tools.OpenAI
{
    /// <summary>
    /// 诊断OpenAI函数
    /// 用于检查系统状态和函数注册情况
    /// </summary>
    public class DiagnosticFunction : BaseOpenAIFunction
    {
        public override string Name => "system_diagnostic";

        public override string Description => "执行系统诊断，检查OpenAI函数注册状态、AI助手配置和系统健康状况。用于故障排除和系统监控。";

        protected override bool RequiresContext => false;

        protected override FunctionParameters GetParametersDefinition()
        {
            var parameters = new FunctionParameters();
            
            parameters.AddProperty("check_type", 
                ParameterProperty.CreateString("检查类型", 
                    new List<string> { "functions", "config", "system", "all" }, "all"));

            return parameters;
        }

        protected override async Task<FunctionResult> ExecuteInternalAsync(FunctionCall functionCall, GISContext context)
        {
            Log($"开始执行系统诊断，参数: {functionCall.Arguments}");

            var parameters = ParseArguments<DiagnosticParameters>(functionCall);

            switch (parameters.CheckType.ToLower())
            {
                case "functions":
                    return await CheckFunctions(functionCall);

                case "config":
                    return await CheckConfig(functionCall);

                case "system":
                    return await CheckSystem(functionCall, context);

                case "all":
                default:
                    return await CheckAll(functionCall, context);
            }
        }

        private async Task<FunctionResult> CheckFunctions(FunctionCall functionCall)
        {
            try
            {
                var functionManager = OpenAIFunctionManager.Instance;
                var availableFunctions = functionManager.GetAvailableFunctions();
                var statistics = functionManager.GetStatistics();

                var functionDetails = availableFunctions.Select(f => new
                {
                    name = f.Name,
                    description = f.Description,
                    parameter_count = f.Parameters?.Properties?.Count ?? 0,
                    required_params = f.Parameters?.Required?.Count ?? 0
                }).ToArray();

                var result = new
                {
                    check_type = "functions",
                    summary = new
                    {
                        total_functions = availableFunctions.Count,
                        function_manager_status = "active"
                    },
                    functions = functionDetails,
                    statistics = statistics
                };

                return FunctionResult.CreateSuccessWithData(
                    functionCall.Id,
                    functionCall.Name,
                    result,
                    $"函数检查完成，共注册 {availableFunctions.Count} 个函数"
                );
            }
            catch (Exception ex)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"函数检查失败: {ex.Message}"
                );
            }
        }

        private async Task<FunctionResult> CheckConfig(FunctionCall functionCall)
        {
            try
            {
                var config = ConfigurationManager.GetAIServiceConfig();
                var validation = ConfigurationManager.ValidateConfig(config);

                var configInfo = new
                {
                    service_name = config.ServiceName,
                    api_endpoint = config.ApiEndpoint,
                    model_name = config.ModelName,
                    max_tokens = config.MaxTokens,
                    temperature = config.Temperature,
                    streaming_enabled = config.EnableStreaming,
                    timeout_seconds = config.TimeoutSeconds,
                    api_key_configured = !string.IsNullOrWhiteSpace(config.ApiKey),
                    config_valid = validation.IsValid,
                    validation_message = validation.ErrorMessage
                };

                var result = new
                {
                    check_type = "config",
                    summary = new
                    {
                        config_valid = validation.IsValid,
                        api_configured = !string.IsNullOrWhiteSpace(config.ApiKey),
                        service_ready = validation.IsValid && !string.IsNullOrWhiteSpace(config.ApiKey)
                    },
                    configuration = configInfo
                };

                return FunctionResult.CreateSuccessWithData(
                    functionCall.Id,
                    functionCall.Name,
                    result,
                    $"配置检查完成，状态: {(validation.IsValid ? "有效" : "无效")}"
                );
            }
            catch (Exception ex)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"配置检查失败: {ex.Message}"
                );
            }
        }

        private async Task<FunctionResult> CheckSystem(FunctionCall functionCall, GISContext context)
        {
            try
            {
                var systemInfo = new
                {
                    arcgis_pro_connected = context != null,
                    active_map = context?.MapName ?? "无活动地图",
                    layer_count = context?.ActiveLayers?.Count ?? 0,
                    selected_features = context?.SelectedFeatureCount ?? 0,
                    spatial_reference = context?.SpatialReference ?? "未知",
                    project_path = context?.ProjectPath ?? "未知",
                    has_unsaved_edits = context?.HasUnsavedEdits ?? false,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var result = new
                {
                    check_type = "system",
                    summary = new
                    {
                        system_healthy = context != null,
                        arcgis_connected = context != null,
                        map_available = !string.IsNullOrEmpty(context?.MapName)
                    },
                    system_info = systemInfo
                };

                return FunctionResult.CreateSuccessWithData(
                    functionCall.Id,
                    functionCall.Name,
                    result,
                    $"系统检查完成，ArcGIS Pro连接: {(context != null ? "正常" : "异常")}"
                );
            }
            catch (Exception ex)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"系统检查失败: {ex.Message}"
                );
            }
        }

        private async Task<FunctionResult> CheckAll(FunctionCall functionCall, GISContext context)
        {
            try
            {
                var functionsResult = await CheckFunctions(functionCall);
                var configResult = await CheckConfig(functionCall);
                var systemResult = await CheckSystem(functionCall, context);

                var functionsData = JsonConvert.DeserializeObject(functionsResult.Content);
                var configData = JsonConvert.DeserializeObject(configResult.Content);
                var systemData = JsonConvert.DeserializeObject(systemResult.Content);

                var overallHealth = functionsResult.Success && configResult.Success && systemResult.Success;

                var result = new
                {
                    check_type = "all",
                    summary = new
                    {
                        overall_health = overallHealth,
                        functions_ok = functionsResult.Success,
                        config_ok = configResult.Success,
                        system_ok = systemResult.Success,
                        timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    },
                    details = new
                    {
                        functions = functionsData,
                        configuration = configData,
                        system = systemData
                    }
                };

                return FunctionResult.CreateSuccessWithData(
                    functionCall.Id,
                    functionCall.Name,
                    result,
                    $"全面诊断完成，系统状态: {(overallHealth ? "健康" : "异常")}"
                );
            }
            catch (Exception ex)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"全面诊断失败: {ex.Message}"
                );
            }
        }

        /// <summary>
        /// 诊断参数模型
        /// </summary>
        private class DiagnosticParameters
        {
            [JsonProperty("check_type")]
            public string CheckType { get; set; } = "all";
        }
    }
}
