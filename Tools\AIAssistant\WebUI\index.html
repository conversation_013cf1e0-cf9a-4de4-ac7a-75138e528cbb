<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="lib/highlight.min.css">
</head>
<body>
    <div id="app">
        <!-- 顶部控制栏 -->
        <div class="top-controls">
            <button class="control-btn" id="historyToggle" title="历史记录" onclick="handleHistoryClick()">
                <span>☰</span>
            </button>

            <!-- 中间状态信息 -->
            <div class="status-info">
                <div class="status-left">
                    <span class="status-indicator" id="statusIndicator">🟢</span>
                    <span class="status-text" id="statusText">在线</span>
                </div>
                <div class="status-right">
                    <span class="model-info">Kimi K2</span>
                </div>
            </div>

            <div class="menu-dropdown">
                <button class="control-btn menu-btn" id="menuButton" title="菜单" onclick="handleMenuClick()">
                    <span>☰</span>
                </button>
                <div class="dropdown-content" id="dropdownMenu">
                    <div class="dropdown-item" id="newChat" onclick="handleNewChatClick()">新建对话</div>
                    <div class="dropdown-item" id="clearChat" onclick="handleClearChatClick()">清空对话</div>
                    <div class="dropdown-item" id="testTools" onclick="testToolBlocks()">测试工具块</div>
                </div>
            </div>

            <!-- 历史记录弹窗 -->
            <div class="history-bar" id="historyBar">
                <div class="history-content">
                    <div id="history-list">
                        <div class="history-item active">📝 当前对话 - GIS数据分析</div>
                        <div class="history-item">🗺️ 昨天 - 空间查询和缓冲区分析</div>
                        <div class="history-item">📊 上周 - 图层管理和符号化</div>
                        <div class="history-item">🔍 上周 - 属性查询和数据导出</div>
                        <div class="history-item">📐 两周前 - 地理坐标系转换</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消息区域 -->
        <div class="messages-area" id="messages-area">
            <!-- 消息将在这里动态添加 -->
        </div>

        <!-- 工具块容器 -->
        <div class="tool-blocks-container" id="toolBlocksContainer">
            <!-- 工具块将在这里动态添加 -->
        </div>

        <!-- 输入区域 -->
        <div class="input-area" id="inputArea">
            <!-- 工具调用指示器 -->
            <div class="tool-indicator" id="toolIndicator">
                <div class="tool-indicator-content">
                    <div class="loading-spinner"></div>
                    <span id="toolIndicatorText">正在调用工具...</span>
                </div>
            </div>

            <!-- 可拉伸分隔线 -->
            <div class="resize-handle" id="resizeHandle">
                <div class="resize-line"></div>
            </div>

            <!-- 输入容器 -->
            <div class="input-container">
                <!-- 文字输入区域（包含内嵌按钮） -->
                <div class="text-input-wrapper">
                    <textarea
                        id="messageInput"
                        placeholder="请输入您的GIS问题..."
                        rows="1"
                        maxlength="2000"></textarea>

                    <!-- 输入框内的工具栏 -->
                    <div class="input-inline-toolbar">
                        <!-- 左侧：图片附件和模式切换 -->
                        <div class="inline-left">
                            <button class="inline-btn" id="attachBtn" title="添加图片">
                                <span>📎</span>
                            </button>
                            <div class="mode-switch-compact">
                                <span class="mode-label active" id="chatLabel">Chat</span>
                                <div class="switch-container">
                                    <input type="checkbox" id="modeSwitch" class="switch-input">
                                    <label for="modeSwitch" class="switch-label">
                                        <span class="switch-slider"></span>
                                    </label>
                                </div>
                                <span class="mode-label" id="agentLabel">Agent</span>
                            </div>
                        </div>

                        <!-- 右侧：发送/停止按钮 -->
                        <div class="inline-right">
                            <button class="inline-action-btn send-btn" id="sendButton" title="发送消息">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                </svg>
                            </button>
                            <button class="inline-action-btn stop-btn" id="stopButton" title="停止生成" style="display: none;">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M6 6h12v12H6z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>



    <!-- JavaScript库 -->
    <script src="lib/marked.min.js"></script>
    <script src="lib/highlight.min.js"></script>

    <!-- 应用脚本 -->
    <script src="js/markdown-renderer.js"></script>
    <script src="js/stream-handler.js"></script>
    <script src="js/tool-blocks.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
