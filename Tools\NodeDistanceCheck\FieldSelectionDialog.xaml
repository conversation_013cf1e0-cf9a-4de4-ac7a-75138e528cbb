<Window x:Class="XIAOFUTools.Tools.NodeDistanceCheck.FieldSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:XIAOFUTools.Tools.NodeDistanceCheck"
        mc:Ignorable="d"
        Title="选择保留字段" Height="400" Width="350"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        MinHeight="300" MinWidth="300">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    
    <Grid Margin="12">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题说明 -->
        <TextBlock Grid.Row="0" Text="选择要保留到输出图层的原始字段：" 
                   FontWeight="Bold" Margin="0,0,0,10"/>
        
        <!-- 操作按钮 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
            <Button Content="全选" Width="60" Height="22" Margin="0,0,5,0"
                    Style="{StaticResource DefaultButtonStyle}"
                    Click="SelectAll_Click"/>
            <Button Content="全不选" Width="60" Height="22" Margin="0,0,5,0"
                    Style="{StaticResource DefaultButtonStyle}"
                    Click="SelectNone_Click"/>
            <Button Content="反选" Width="60" Height="22"
                    Style="{StaticResource DefaultButtonStyle}"
                    Click="InvertSelection_Click"/>
        </StackPanel>
        
        <!-- 字段列表 -->
        <Border Grid.Row="2" BorderBrush="#CDCDCD" BorderThickness="1" Margin="0,0,0,10">
            <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Padding="5">
                <ItemsControl x:Name="FieldsItemsControl" ItemsSource="{Binding FieldItems}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <CheckBox Content="{Binding DisplayName}"
                                     IsChecked="{Binding IsSelected}"
                                     Margin="3,3,3,3"
                                     Padding="5,2"
                                     Style="{StaticResource CheckBoxStyle}"
                                     FontSize="11"
                                     VerticalContentAlignment="Center"/>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Border>
        
        <!-- 选择统计 -->
        <TextBlock Grid.Row="3" Margin="0,0,0,10"
                   Text="{Binding SelectionSummary}"
                   Foreground="{StaticResource TextSecondaryBrush}"/>
        
        <!-- 按钮区域 -->
        <Border Grid.Row="4" BorderBrush="{StaticResource DividerBrush}"
               BorderThickness="0,1,0,0"
               Margin="0,5,0,0"
               Padding="0,10,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="确定" Width="80" Height="22" Margin="0,0,10,0"
                        Style="{StaticResource ExecuteButtonStyle}"
                        Click="OK_Click"/>
                <Button Content="取消" Width="80" Height="22"
                        Style="{StaticResource CancelButtonStyle}"
                        Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>