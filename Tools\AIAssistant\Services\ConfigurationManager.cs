using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;
using XIAOFUTools.Tools.AIAssistant.Models;

namespace XIAOFUTools.Tools.AIAssistant.Services
{
    /// <summary>
    /// 配置管理器
    /// </summary>
    public static class ConfigurationManager
    {
        private static readonly string ConfigDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "XIAOFU Tools", "AIAssistant");
        
        private static readonly string ConfigFilePath = Path.Combine(ConfigDirectory, "config.json");
        private static readonly byte[] EncryptionKey = Encoding.UTF8.GetBytes("XIAOFUTools2024!"); // 16字节密钥

        /// <summary>
        /// 获取AI服务配置
        /// </summary>
        /// <returns>AI服务配置</returns>
        public static AIServiceConfig GetAIServiceConfig()
        {
            try
            {
                if (File.Exists(ConfigFilePath))
                {
                    string encryptedJson = File.ReadAllText(ConfigFilePath);
                    string decryptedJson = DecryptString(encryptedJson);
                    return JsonConvert.DeserializeObject<AIServiceConfig>(decryptedJson);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取配置失败: {ex.Message}");
            }

            // 返回默认配置
            return AIServiceConfig.CreateMoonshotConfig();
        }

        /// <summary>
        /// 保存AI服务配置
        /// </summary>
        /// <param name="config">AI服务配置</param>
        public static void SaveAIServiceConfig(AIServiceConfig config)
        {
            try
            {
                // 确保目录存在
                Directory.CreateDirectory(ConfigDirectory);

                // 序列化并加密配置
                string json = JsonConvert.SerializeObject(config, Formatting.Indented);
                string encryptedJson = EncryptString(json);

                // 保存到文件
                File.WriteAllText(ConfigFilePath, encryptedJson);
            }
            catch (Exception ex)
            {
                throw new Exception($"保存配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 加密字符串
        /// </summary>
        /// <param name="plainText">明文</param>
        /// <returns>密文</returns>
        private static string EncryptString(string plainText)
        {
            try
            {
                using (Aes aes = Aes.Create())
                {
                    aes.Key = EncryptionKey;
                    aes.GenerateIV();

                    using (var encryptor = aes.CreateEncryptor())
                    using (var msEncrypt = new MemoryStream())
                    {
                        // 写入IV
                        msEncrypt.Write(aes.IV, 0, aes.IV.Length);

                        using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                        using (var swEncrypt = new StreamWriter(csEncrypt))
                        {
                            swEncrypt.Write(plainText);
                        }

                        return Convert.ToBase64String(msEncrypt.ToArray());
                    }
                }
            }
            catch
            {
                // 加密失败时返回原文（不安全，但确保功能可用）
                return plainText;
            }
        }

        /// <summary>
        /// 解密字符串
        /// </summary>
        /// <param name="cipherText">密文</param>
        /// <returns>明文</returns>
        private static string DecryptString(string cipherText)
        {
            try
            {
                byte[] cipherBytes = Convert.FromBase64String(cipherText);

                using (Aes aes = Aes.Create())
                {
                    aes.Key = EncryptionKey;

                    // 提取IV
                    byte[] iv = new byte[aes.IV.Length];
                    Array.Copy(cipherBytes, 0, iv, 0, iv.Length);
                    aes.IV = iv;

                    using (var decryptor = aes.CreateDecryptor())
                    using (var msDecrypt = new MemoryStream(cipherBytes, iv.Length, cipherBytes.Length - iv.Length))
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    using (var srDecrypt = new StreamReader(csDecrypt))
                    {
                        return srDecrypt.ReadToEnd();
                    }
                }
            }
            catch
            {
                // 解密失败时返回原文（可能是未加密的配置）
                return cipherText;
            }
        }

        /// <summary>
        /// 重置配置为默认值
        /// </summary>
        public static void ResetToDefault()
        {
            try
            {
                if (File.Exists(ConfigFilePath))
                {
                    File.Delete(ConfigFilePath);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"重置配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查配置文件是否存在
        /// </summary>
        /// <returns>配置文件是否存在</returns>
        public static bool ConfigExists()
        {
            return File.Exists(ConfigFilePath);
        }

        /// <summary>
        /// 验证API配置
        /// </summary>
        /// <param name="config">AI服务配置</param>
        /// <returns>验证结果</returns>
        public static (bool IsValid, string ErrorMessage) ValidateConfig(AIServiceConfig config)
        {
            if (config == null)
                return (false, "配置不能为空");

            if (string.IsNullOrWhiteSpace(config.ApiEndpoint))
                return (false, "API端点不能为空");

            if (string.IsNullOrWhiteSpace(config.ApiKey))
                return (false, "API密钥不能为空");

            if (string.IsNullOrWhiteSpace(config.ModelName))
                return (false, "模型名称不能为空");

            if (config.MaxTokens <= 0)
                return (false, "最大令牌数必须大于0");

            if (config.Temperature < 0 || config.Temperature > 2)
                return (false, "温度参数必须在0-2之间");

            if (config.TimeoutSeconds <= 0)
                return (false, "超时时间必须大于0");

            return (true, string.Empty);
        }
    }
}
