<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 核心功能蓝色渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>

    <!-- 信息图标渐变 -->
    <linearGradient id="infoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:0.9" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="infoShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="1" stdDeviation="2" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="32" height="32" rx="8" fill="url(#bgGradient)"/>

  <!-- 信息圆圈 -->
  <circle cx="16" cy="16" r="10" fill="url(#infoGradient)" filter="url(#infoShadow)"/>
  
  <!-- 圆圈边框 -->
  <circle cx="16" cy="16" r="10" fill="none" stroke="#667eea" stroke-width="0.5" opacity="0.3"/>

  <!-- 信息符号 "i" -->
  <g transform="translate(16,16)">
    <!-- i 的点 -->
    <circle cx="0" cy="-4" r="1.5" fill="#667eea"/>
    
    <!-- i 的竖线 -->
    <rect x="-1" y="-1" width="2" height="6" rx="1" fill="#667eea"/>
    
    <!-- 装饰性底线 -->
    <rect x="-2.5" y="4" width="5" height="1" rx="0.5" fill="#667eea" opacity="0.6"/>
  </g>

  <!-- 装饰元素 -->
  <circle cx="16" cy="16" r="12" fill="none" stroke="#ffffff" stroke-width="0.5" opacity="0.2"/>
  
  <!-- 角落装饰点 -->
  <circle cx="6" cy="6" r="1" fill="#ffffff" opacity="0.4"/>
  <circle cx="26" cy="6" r="1" fill="#ffffff" opacity="0.4"/>
  <circle cx="6" cy="26" r="1" fill="#ffffff" opacity="0.4"/>
  <circle cx="26" cy="26" r="1" fill="#ffffff" opacity="0.4"/>
</svg>
