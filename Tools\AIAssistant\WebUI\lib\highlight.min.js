// 简化的代码高亮库（用于演示）
// 在实际部署时，请使用完整的highlight.js库
(function() {
    'use strict';
    
    window.hljs = {
        highlight: function(code, options) {
            // 简化的语法高亮
            let highlightedCode = code;
            
            // 基本的关键字高亮
            const keywords = ['function', 'var', 'let', 'const', 'if', 'else', 'for', 'while', 'return', 'class', 'public', 'private', 'static'];
            keywords.forEach(keyword => {
                const regex = new RegExp('\\b' + keyword + '\\b', 'g');
                highlightedCode = highlightedCode.replace(regex, '<span class="hljs-keyword">' + keyword + '</span>');
            });
            
            // 字符串高亮
            highlightedCode = highlightedCode.replace(/"([^"]*)"/g, '<span class="hljs-string">"$1"</span>');
            highlightedCode = highlightedCode.replace(/'([^']*)'/g, '<span class="hljs-string">\'$1\'</span>');
            
            // 注释高亮
            highlightedCode = highlightedCode.replace(/\/\/(.*)/g, '<span class="hljs-comment">//$1</span>');
            highlightedCode = highlightedCode.replace(/\/\*([\s\S]*?)\*\//g, '<span class="hljs-comment">/*$1*/</span>');
            
            return { value: highlightedCode };
        },
        
        getLanguage: function(lang) {
            // 支持的语言列表
            const supportedLangs = ['javascript', 'python', 'java', 'csharp', 'html', 'css', 'sql'];
            return supportedLangs.includes(lang);
        }
    };
})();
