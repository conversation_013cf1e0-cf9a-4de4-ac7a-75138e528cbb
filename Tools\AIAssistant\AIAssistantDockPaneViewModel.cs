using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using XIAOFUTools.Tools.AIAssistant.Services;
using XIAOFUTools.Tools.AIAssistant.Models;

namespace XIAOFUTools.Tools.AIAssistant
{
    /// <summary>
    /// AI助手停靠窗格视图模型
    /// </summary>
    public class AIAssistantDockPaneViewModel : INotifyPropertyChanged
    {
        private readonly AIAssistantService _aiService;
        private readonly GISAgentService _gisAgent;
        private readonly ConversationManager _conversationManager;
        private bool _isProcessing;
        private string _statusMessage;

        public AIAssistantDockPaneViewModel()
        {
            _aiService = new AIAssistantService();
            _gisAgent = new GISAgentService();
            _conversationManager = new ConversationManager();
            _statusMessage = "AI助手已就绪";
        }

        #region 属性

        /// <summary>
        /// 是否正在处理
        /// </summary>
        public bool IsProcessing
        {
            get => _isProcessing;
            set
            {
                _isProcessing = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanSendMessage));
            }
        }

        /// <summary>
        /// 是否可以发送消息
        /// </summary>
        public bool CanSendMessage => !IsProcessing;

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 发送用户消息并获取AI回复
        /// </summary>
        /// <param name="userMessage">用户消息</param>
        /// <param name="onStreamUpdate">流式更新回调</param>
        /// <param name="mode">对话模式（chat/agent）</param>
        /// <param name="enableFunctions">是否启用函数调用</param>
        /// <returns>完整的AI回复</returns>
        public async Task<string> SendMessageAsync(string userMessage, Action<string> onStreamUpdate = null, string mode = "chat", bool enableFunctions = false)
        {
            if (IsProcessing || string.IsNullOrWhiteSpace(userMessage))
                return string.Empty;

            try
            {
                IsProcessing = true;
                StatusMessage = "AI正在思考...";

                // 添加用户消息到对话历史
                _conversationManager.AddMessage(ChatMessage.CreateUserMessage(userMessage));

                // 获取GIS上下文信息
                GISContext gisContext = null;
                try
                {
                    gisContext = await _gisAgent.GetCurrentGISContextAsync();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"获取GIS上下文失败: {ex.Message}");
                    // 继续处理，使用null上下文
                }

                // 构建包含GIS上下文的完整提示
                string enhancedPrompt = _gisAgent.EnhancePromptWithGISContext(userMessage, gisContext);

                // 获取对话历史
                var conversationHistory = _conversationManager.GetConversationHistory();

                // 调用AI服务获取回复
                string aiResponse = await _aiService.GetResponseAsync(enhancedPrompt, conversationHistory, onStreamUpdate, mode, enableFunctions);

                // 添加AI回复到对话历史
                _conversationManager.AddMessage(ChatMessage.CreateAssistantMessage(aiResponse));

                StatusMessage = "AI助手已就绪";
                return aiResponse;
            }
            catch (Exception ex)
            {
                StatusMessage = $"错误: {ex.Message}";
                return $"抱歉，处理您的请求时出现错误：{ex.Message}";
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// 清除对话历史
        /// </summary>
        public void ClearConversation()
        {
            _conversationManager.ClearHistory();
            StatusMessage = "对话历史已清除";
        }

        /// <summary>
        /// 获取对话历史
        /// </summary>
        /// <returns>对话历史</returns>
        public ChatMessage[] GetConversationHistory()
        {
            return _conversationManager.GetConversationHistory();
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
