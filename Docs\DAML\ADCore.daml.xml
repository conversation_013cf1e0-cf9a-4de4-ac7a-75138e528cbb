<?xml version="1.0" encoding="utf-8"?>
<ArcGIS defaultAssembly="ArcGIS.Desktop.Core.dll" 
        defaultNamespace="ArcGIS.Desktop.Core" 
        xmlns="http://schemas.esri.com/DADF/Registry" 
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:schemaLocation="http://schemas.esri.com/DADF/Registry \ArcGIS\ArcGISDesktop\UIFramework\ArcGIS.Desktop.Framework.xsd">

  <accelerators>
    <insertAccelerator refID="esri_core_openProjectButton" flags="Ctrl" key="O" />
    <insertAccelerator refID="esri_core_newProjectButton" flags="Ctrl" key="N" />
    <insertAccelerator refID="esri_core_saveProjectButton" flags="Ctrl" key="S" />
    <insertAccelerator refID="esri_core_editCopyButton" flags="Ctrl" key="C" />
    <insertAccelerator refID="esri_core_editPasteButton" flags="Ctrl" key="V" />
    <insertAccelerator refID="esri_core_editDeleteButton" flags="Ctrl" key="D" />
    <insertAccelerator refID="esri_core_editPasteSpecialButton" flags="Ctrl+Alt" key="V" />
    <insertAccelerator refID="esri_core_editCutButton" flags="Ctrl" key="X" />
    <insertAccelerator refID="esri_core_redoButton" flags="Ctrl" key="Y" />
    <insertAccelerator refID="esri_core_undoButton" flags="Ctrl" key="Z" />
    <insertAccelerator refID="esri_core_CommandSearchControl" flags="Alt" key="Q" />
    <insertAccelerator refID="esri_core_groupContext" flags="Ctrl" key="G" />
    <insertAccelerator refID="esri_core_ungroupContext" flags="Ctrl+Shift" key="G" />
    <insertAccelerator refID="esri_core_exportContext" flags="Ctrl" key="E" />
    <insertAccelerator refID="esri_core_printContext" flags="Ctrl" key="P" />
    <insertAccelerator refID="esri_core_editCopyPaths" flags="Ctrl+Alt" key="P" />
    <insertAccelerator refID="esri_core_SignOnRootCommand" flags="Alt" key="L"/>
    <insertAccelerator refID="esri_core_notificationButton" flags="Alt" key="W"/>
    <insertAccelerator refID="esri_core_Shortcuts" key="F12"/>
    <insertAccelerator refID="esri_core_monitorBtn" key="M" flags="Ctrl+Alt" isReadOnly="true"/>

    <insertAccelerator refID="esri_core_paneOptions" key="OemMinus" flags="Alt" isReadOnly="true"/>
    <insertAccelerator refID="esri_core_changeView" key="Tab" flags="Ctrl" isReadOnly="true"/>
    <insertAccelerator refID="esri_core_changePane" key="F7" flags="Alt" isReadOnly="true"/>
    <insertAccelerator refID="esri_core_switchView" key="F6" flags="Ctrl" isReadOnly="true"/>
    <insertAccelerator refID="esri_core_closeView" key="F4" flags="Ctrl" isReadOnly="true"/>
    <insertAccelerator refID="esri_core_viewHelp" key="F1" isReadOnly="true"/>
    <insertAccelerator refID="esri_core_minimizeRibbon" key="F1" flags="Ctrl" isReadOnly="true"/>
  </accelerators>

  <shortcutTables>
    <insertShortcutTable id="esri_core_catalogView_shortcuts"
                         targetID="esri_core_resourcesPane"
                         isPrimary="true" category="Catalog" caption="View"
                         description="Use Catalog view shortcuts to navigate the view, manage item collections, create and search for items, edit metadata, display the details pane, and access the location bar.">

      <!-- traditional -->
      <shortcut refID="esri_core_refresh" key="F5"/>
      <shortcut refID="esri_core_rename" key="F2"/>
      <shortcut refID="esri_core_rename_alias" key="F2" flags="Shift"/>
      <shortcut refID="esri_core_SearchCatalog" key="F" flags="Ctrl"/>
      <shortcut refID="esri_editing_externalTable_openTablePaneButton" flags="Ctrl+Shift" key="T" />

      <!-- Ctrl+Shift for operations (new/edit) -->
      <shortcut refID="esri_folderConnectionAddButton" key="C" flags="Ctrl+Shift"/>
      <shortcut refID="esri_core_newFolderButton" key="F" flags="Ctrl+Shift"/>
      <shortcut refID="esri_layouts_projectContainer_NewLayout" key="L" flags="Ctrl+Shift" />
      <shortcut refID="esri_GDBCreateNewFileGeodatabaseButton" key="D" flags="Ctrl+Shift"/>
      <shortcut refID="esri_geodatabase_databaseConnectionButton" key="E" flags="Ctrl+Shift"/>
      <shortcut refID="esri_geodatabase_newMobileGeoDatabaseButton" key="I" flags="Ctrl+Shift"/>
      <shortcut refID="esri_geoprocessing_newToolboxButton_atbx" key="B" flags="Ctrl+Shift"/>
      <shortcut refID="esri_projectItemEditMetadata" key="M" flags="Ctrl+Shift"/>
      <shortcut refID="esri_projectDefaultContextMenu" key="N" flags="Ctrl+Shift"/>
      
      <!-- Alt for navigation -->
      <shortcut refID="esri_core_ActivateCatalogPane" key="F3" flags="Alt"/>
      <shortcut refID="esri_core_FocusTOC" key="F5" flags="Alt"/>
      <shortcut refID="esri_core_Breadcrumb" key="L" flags="Ctrl"/>
      <shortcut refID="esri_core_ShowSortMenu" key="F6"/>
      <shortcut refID="esri_core_FocusContent" key="F6" flags="Alt"/>
      <shortcut refID="esri_core_ShowFilterMenu" key="F8" flags="Alt"/>
      <shortcut refID="esri_projectItemViewMetadata" key="M" flags="Alt"/>
      <shortcut refID="esri_core_ToggleDetails" key="D" flags="Alt"/>
      <shortcut refID="esri_core_MoveBack" key="Left" flags="Alt"/>
      <shortcut refID="esri_core_MoveForward" key="Right" flags="Alt"/>
      <shortcut refID="esri_core_MoveUp" key="Up" flags="Alt"/>
      <shortcut refID="esri_core_ShowProject" key="P" flags="Alt"/>
      <shortcut refID="esri_core_ShowPortal" key="O" flags="Alt"/>
      <shortcut refID="esri_core_ShowComputer" key="Z" flags="Alt"/>
      <shortcut refID="esri_core_ShowFavorites" key="F" flags="Alt"/>
      <shortcut refID="esri_core_ShowCatalogOptions" key="B" flags="Alt"/>
      <shortcut refID="esri_core_openFileLocation" key="E" flags="Alt"/>
    </insertShortcutTable>
    
    <insertShortcutTable id="esri_core_catalogPane_shortcuts"
                         targetID="esri_core_projectDockPane"
                         isPrimary="true" category="Catalog" caption="Pane"
                         description="Use Catalog pane shortcuts to navigate the pane; manage project, portal, computer, and favorite items; create items, and search for items.">

      <!-- traditional -->
      <shortcut refID="esri_core_refresh" key="F5"/>
      <shortcut refID="esri_core_rename" key="F2"/>
      <shortcut refID="esri_core_rename_alias" key="F2" flags="Shift"/>
      <shortcut refID="esri_core_SearchCatalog" key="F" flags="Ctrl"/>
      <shortcut refID="esri_editing_externalTable_openTablePaneButton" flags="Ctrl+Shift" key="T" />

      <!-- Ctrl+Shift for operations (new/edit) -->
      <shortcut refID="esri_folderConnectionAddButton" key="C" flags="Ctrl+Shift"/>
      <shortcut refID="esri_core_newFolderButton" key="F" flags="Ctrl+Shift"/>
      <shortcut refID="esri_layouts_projectContainer_NewLayout" key="L" flags="Ctrl+Shift" />
      <shortcut refID="esri_GDBCreateNewFileGeodatabaseButton" key="D" flags="Ctrl+Shift"/>
      <shortcut refID="esri_geodatabase_databaseConnectionButton" key="E" flags="Ctrl+Shift"/>
      <shortcut refID="esri_geodatabase_newMobileGeoDatabaseButton" key="I" flags="Ctrl+Shift"/>
      <shortcut refID="esri_geoprocessing_newToolboxButton_atbx" key="B" flags="Ctrl+Shift"/>
      <shortcut refID="esri_projectItemEditMetadata" key="M" flags="Ctrl+Shift"/>
      <shortcut refID="esri_projectDefaultContextMenu" key="N" flags="Ctrl+Shift"/>

      <!-- Alt for navigation -->
      <shortcut refID="esri_core_ActivateResourcesPane" key="F3" flags="Alt" />
      <shortcut refID="esri_core_ShowSortMenu" key="F6"/>
      <shortcut refID="esri_core_FocusContent" key="F6" flags="Alt"/>
      <shortcut refID="esri_core_ShowFilterMenu" key="F8" flags="Alt"/>
      <shortcut refID="esri_projectItemViewMetadata" key="M" flags="Alt"/>
      <shortcut refID="esri_core_MoveBack" key="Left" flags="Alt"/>
      <shortcut refID="esri_core_ProjectDP" key="P" flags="Alt"/>
      <shortcut refID="esri_core_PortalDP" key="O" flags="Alt"/>
      <shortcut refID="esri_core_ShowComputer" key="Z" flags="Alt"/>
      <shortcut refID="esri_core_FavoritesDP" key="F" flags="Alt"/>
      <shortcut refID="esri_core_ShowCatalogOptions" key="B" flags="Alt"/>
      <shortcut refID="esri_core_openFileLocation" key="E" flags="Alt"/>
    </insertShortcutTable>
  </shortcutTables>

  <DockPaneSets>
    <insertDockPaneSet id="esri_DockPaneReset_Mapping" caption="Mapping" smallImage="PaneStateMapping16">
      <DockPaneID refID="esri_core_projectDockPane"/>
      <DockPaneID refID="esri_core_contentsDockPane"/>
    </insertDockPaneSet>

    <insertDockPaneSet id="esri_DockPaneReset_Editing" caption="Editing" smallImage="PaneStateEditing16">
      <DockPaneID refID="esri_core_projectDockPane"/>
      <DockPaneID refID="esri_core_contentsDockPane"/>
      <DockPaneID refID="esri_editing_AttributesDockPane"/>
      <DockPaneID refID="esri_editing_EditFeaturesDockPane"/>
      <DockPaneID refID="esri_editing_CreateFeaturesDockPane"/>
    </insertDockPaneSet>

    <insertDockPaneSet id="esri_DockPaneReset_Geoprocessing" caption="Geoprocessing" smallImage="PaneStateGeoprocessing16">
      <DockPaneID refID="esri_core_contentsDockPane"/>
      <DockPaneID refID="esri_core_projectDockPane"/>
      <DockPaneID refID="esri_geoprocessing_toolBoxes"/>
    </insertDockPaneSet>
  </DockPaneSets>

  <products>
    <insertProduct id="esri_product_streetmapPremiumnNorthAmerica" caption="StreetMap Premium North America" description="Provides high-quality street data for display, geocoding, and routing" code="72"/>
    <insertProduct id="esri_product_streetmapPremiumnEurope" caption="StreetMap Premium Europe" description="Provides high-quality street data for display, geocoding, and routing" code="73"/>
    <insertProduct id="esri_product_streetmapPremiumnLatinAmerica" caption="StreetMap Premium Latin America" description="Provides high-quality street data for display, geocoding, and routing" code="74"/>
    <insertProduct id="esri_product_streetmapPremiumnAsiaPacific" caption="StreetMap Premium Asia Pacific" description="Provides high-quality street data for display, geocoding, and routing" code="75"/>
    <insertProduct id="esri_product_streetmapPremiumnMiddleEastAfrica" caption="StreetMap Premium Middle East and Africa" description="Provides high-quality street data for display, geocoding, and routing" code="76"/>
    <insertProduct id="esri_product_streetmapPremiumJapan" caption="StreetMap Premium Japan" description="Provides high-quality street data for display, geocoding, and routing" code="77"/>
    <insertProduct id="esri_product_publisher" caption="Publisher" description="Enables the sharing of content throughout the ArcGIS platform." code="15"/>
    <insertProduct id="esri_product_aviationCharting" caption="Aviation Charting" description="Provides tools and workflows for working with aeronautical data and charting." code="53"/>
    <insertProduct id="esri_product_aviationAirports" caption="Aviation Airports" description="Provides tools for airport planning, maintenance and analysis." code="70"/>
    <insertProduct id="esri_product_maritimeCharting" caption="Maritime" description="Provides tools and workflows for working with nautical data and charting." code="50"/>
    <insertProduct id="esri_product_businessanalyst" caption="Business Analyst" description="Provides advanced analysis tools and an extensive library of data from industry leading data providers to help you make better business decisions." code="33"/>
    <insertProduct id="esri_product_imageanalyst" caption="Image Analyst" description="Delivers an integrated image interpretation and analysis workstation which provides powerful image visualization, exploitation, and feature extraction tools." code="86"/>
    <insertProduct id="esri_product_locatext" caption="LocateXT" description="Discover and extract geocoordinates, place names, and other critical information from unstructured text and place them instantly on a map." code="87"/>
    <insertProduct id="esri_product_reality" caption="Reality" description="ArcGIS Reality." code="94"/>
  </products>

  <categories>
    <insertCategory id="esri_core_projectContainers">
      <component id="esri_displayunits_projectContainer" className="ArcGIS.Desktop.Internal.Core.DisplayUnitEnvironmentItemContainer">
        <content type="DisplayUnitEnvironment" displayName="DisplayUnitEnvironment"/>
      </component>
    </insertCategory>
    <insertCategory id="esri_item" />
    <insertCategory id="esri_cef_customSchemes"/>
    <insertCategory id="esri_itemInfoType" />
    <insertCategory id="esri_browseFilters" />
    <insertCategory id="esri_browseDialogFilters" /> <!--this is now obsolete, replaced by esri_browseFilters-->
    <insertCategory id="esri_browsePlaces" />
    <insertCategory id="esri_embeddableControls" />
    <insertCategory id="esri_tasks_embeddableControls" />
    <insertCategory id="esri_core_dataTemplates" />
    <insertCategory id="esri_core_multiSelectRecognizer" />
    <insertCategory id="esri_core_itemCategories" />
    <insertCategory id="esri_core_projectViewProviders" />
    <insertCategory id="esri_project_historyControls" />
    <insertCategory id="esri_sharingCategory" />
    <insertCategory id="esri_customizedCommandsCategory" />
    <insertCategory id="esri_commandSearchCategory" />
    <insertCategory id="esri_customItems" />
    
    <insertCategory id="portalTypesFilterEnterprise">
      <component id="portalEnterprise">
        <content>
          <typeGroup displayName="Maps" type="maps">
            <typeSubGroup displayName="Web Maps" type="webmaps"/>
            <typeSubGroup displayName="Map Files" type="mapfiles"/>
          </typeGroup>
          <typeGroup displayName="Layers" type="layers">
            <typeSubGroup displayName="Feature Layers" type="featurelayers"/>
            <typeSubGroup displayName="Tile Layers" type="tilelayers"/>
            <typeSubGroup displayName="Map Image Layers" type="mapimagelayers"/>
            <typeSubGroup displayName="Imagery Layers" type="imagerylayers"/>
            <typeSubGroup displayName="Tiled Imagery Layers" type="tiledimagerylayers"/>
            <typeSubGroup displayName="Scene Layers" type="scenelayers"/>
            <typeSubGroup displayName="Video Layers" type="videolayers"/>
            <typeSubGroup displayName="Elevation Layers" type="elevationlayers"/>
            <typeSubGroup displayName="Group Layers" type="grouplayers"/>
            <typeSubGroup displayName="Tables" type="tables"/>
            <typeSubGroup displayName="Layer Files" type="layerfiles"/>
            <typeSubGroup displayName="OGC Layers" type="ogclayers"/>
          </typeGroup>
          <typeGroup displayName="Scenes" type="scenes"/>
          <typeGroup displayName="Tools" type="tools">
            <typeSubGroup displayName="Locators" type="locators"/>
            <typeSubGroup displayName="Geoprocessing Tasks" type="geoprocessingtasks"/>
            <typeSubGroup displayName="Network Analysis" type="networkanalysis"/>
          </typeGroup>
          <typeGroup displayName="Styles" type="styles">
            <typeSubGroup displayName="Web styles" type="webstyles"/>
            <typeSubGroup displayName="Desktop styles" type="desktopstyles"/>
          </typeGroup>
          <typeGroup displayName="Notebooks" type="notebooks"/>
          <typeGroup displayName="Knowledge graphs" type="knowledgegraphs"/>
        </content>
      </component>
    </insertCategory>

    <insertCategory id="portalTypesFilterAGOL">
      <component id="portalAGOL">
        <content>
          <typeGroup displayName="Maps" type="maps">
            <typeSubGroup displayName="Web Maps" type="webmaps"/>
            <typeSubGroup displayName="Map Files" type="mapfiles"/>
          </typeGroup>
          <typeGroup displayName="Layers" type="layers">
            <typeSubGroup displayName="Feature Layers" type="featurelayers"/>
            <typeSubGroup displayName="Tile Layers" type="tilelayers"/>
            <typeSubGroup displayName="Map Image Layers" type="mapimagelayers"/>
            <typeSubGroup displayName="Imagery Layers" type="imagerylayers"/>
            <typeSubGroup displayName="Tiled Imagery Layers" type="tiledimagerylayers"/>
            <typeSubGroup displayName="Elevation layers" type="elevationlayers"/>
            <typeSubGroup displayName="Scene Layers" type="scenelayers"/>
            <typeSubGroup displayName="Group Layers" type="grouplayers"/>
            <typeSubGroup displayName="Tables" type="tables"/>
            <typeSubGroup displayName="Layer Files" type="layerfiles"/>
            <typeSubGroup displayName="OGC Layers" type="ogclayers"/>
          </typeGroup>
          <typeGroup displayName="Scenes" type="scenes"/>
          <typeGroup displayName="Tools" type="tools">
            <typeSubGroup displayName="Locators" type="locators"/>
            <typeSubGroup displayName="Geoprocessing Tasks" type="geoprocessingtasks"/>
            <typeSubGroup displayName="Network Analysis" type="networkanalysis"/>
          </typeGroup>
          <typeGroup displayName="Styles" type="styles">
            <typeSubGroup displayName="Web styles" type="webstyles"/>
            <typeSubGroup displayName="Desktop styles" type="desktopstyles"/>
          </typeGroup>
          <typeGroup displayName="Notebooks" type="notebooks"/>
        </content>
      </component>
    </insertCategory>

    <insertCategory id="portalSharingFilter">
      <component id="portalSharing">
        <content>
          <typeGroup displayName="Owner" type="owner">
            <typeSubGroup displayName="Shared with groups" type="sharedgroups"/>
            <typeSubGroup displayName="Not shared with groups" type="notsharedgroups"/>
          </typeGroup>
          <typeGroup displayName="Organization" type="organization"/>
          <typeGroup displayName="Everyone (public)" type="public"/>
        </content>
      </component>
    </insertCategory>

    <insertCategory id="portalStatusFilter">
      <component id="portalStatus">
        <content>
          <typeGroup displayName="Authoritative" type="authoritative"/>
          <typeGroup displayName="Deprecated" type="deprecated"/>
        </content>
      </component>
    </insertCategory>

    <insertCategory id="localTypesFilter">
      <component id="local">
        <content>
          <typeGroup displayName="Maps" type="allmaps">
            <typeSubGroup displayName="Maps" type="maps"/>
            <typeSubGroup displayName="Scenes" type="scenes"/>
            <typeSubGroup displayName="Stereo maps" type="stereoMaps"/>            
            <typeSubGroup displayName="Link charts" type="linkCharts"/>
            <typeSubGroup displayName="Basemaps" type="basemaps"/>
            <typeSubGroup displayName="Map files" type="mapFiles"/>
          </typeGroup>
          <typeGroup displayName="Layers" type="allLayers">
            <typeSubGroup displayName="Map and scene layers" type="mapAndSceneLayers"/>
            <typeSubGroup displayName="Standalone tables" type="standaloneTables"/>
            <!--Standalone videos will go here when indexing support for them is available-->
            <!--<typeSubGroup displayName="Standalone videos" type="standaloneTables"/>-->
            <typeSubGroup displayName="Layer files" type="layerFiles"/>            
          </typeGroup>
          <typeGroup displayName="Data" type="allData">
            <typeSubGroup displayName="Feature data" type="featureData"/>
            <typeSubGroup displayName="Tables" type="tables"/>
            <typeSubGroup displayName="Raster and imagery data" type="rasterData"/>
            <typeSubGroup displayName="Datasets" type="datasets"/>
            <typeSubGroup displayName="Connections and databases" type="connectionsAndDatabases"/>
            <typeSubGroup displayName="Folders" type="folders"/>            
          </typeGroup>
          <typeGroup displayName="Styles" type="styles">            
          </typeGroup>
          <typeGroup displayName="Layouts" type="layouts">
          </typeGroup>
          <typeGroup displayName="Reports" type="reports">
          </typeGroup>
          <typeGroup displayName="Investigations" type="investigations">
          </typeGroup>
          <typeGroup displayName="Analysis" type="analysis">
            <typeSubGroup displayName="Tools" type="tools"/>
            <typeSubGroup displayName="Toolboxes" type="toolBoxes"/>
            <typeSubGroup displayName="Tasks" type="tasks"/>
            <typeSubGroup displayName="Locators" type="locators"/>
            <typeSubGroup displayName="Notebooks" type="notebooks"/>            
            <typeSubGroup displayName="Spatial Analyst models" type="spatialAnalystModels"/>
            <typeSubGroup displayName="Deep learning" type="deepLearning"/>
          </typeGroup>
          <typeGroup displayName="Other files" type="otherFiles">
          </typeGroup>
          
        </content>
      </component>
    </insertCategory>

    <insertCategory id="localTypesFilterForFolders">
      <component id="localFolders">
        <content>
          <typeGroup displayName="Maps" type="allmaps">            
          </typeGroup>
          <typeGroup displayName="Layers" type="allLayers">            
          </typeGroup>
          <typeGroup displayName="Data" type="allData">
            <typeSubGroup displayName="Feature data" type="featureData"/>
            <typeSubGroup displayName="Tables" type="tables"/>
            <typeSubGroup displayName="Raster and imagery data" type="rasterData"/>
            <typeSubGroup displayName="Datasets" type="datasets"/>
            <typeSubGroup displayName="Connections and databases" type="connectionsAndDatabases"/>
            <typeSubGroup displayName="Folders" type="folders"/>
          </typeGroup>
          <typeGroup displayName="Styles" type="styles">
          </typeGroup>
          <typeGroup displayName="Layouts" type="layouts">
          </typeGroup>
          <typeGroup displayName="Reports" type="reports">
          </typeGroup>
          <!--Investigations will go here when indexing support for them is available-->
          <!--<typeGroup displayName="Investigations" type="investigations">
          </typeGroup>-->
          <typeGroup displayName="Analysis" type="analysis">
            <typeSubGroup displayName="Tools" type="tools"/>
            <typeSubGroup displayName="Toolboxes" type="toolBoxes"/>
            <typeSubGroup displayName="Tasks" type="tasks"/>
            <typeSubGroup displayName="Locators" type="locators"/>
            <typeSubGroup displayName="Notebooks" type="notebooks"/>
            <typeSubGroup displayName="Spatial Analyst models" type="spatialAnalystModels"/>
            <typeSubGroup displayName="Deep learning" type="deepLearning"/>
          </typeGroup>
          <typeGroup displayName="Other files" type="otherFiles">
          </typeGroup>

        </content>
      </component>
    </insertCategory>

    <insertCategory id="localTypesFilterForDatabases">
      <component id="localDatabases">
        <content>
          <typeGroup displayName="Feature classes" type="featureClasses"/>
          <typeGroup displayName="Tables" type="tables"/>
          <typeGroup displayName="Vector datasets" type="vectorDatasets">
            <typeSubGroup displayName="Feature datasets" type="featureDatasets"/>
            <typeSubGroup displayName="Catalog datasets" type="catalogDatasets"/>
            <typeSubGroup displayName="Network datasets" type="networkDatasets"/>
            <typeSubGroup displayName="Parcel datasets" type="parcelDatasets"/>
            <typeSubGroup displayName="Utility networks" type="utilityNetworks"/>
            <typeSubGroup displayName="Trajectory datasets" type="trajectoryDatasets"/>
            <typeSubGroup displayName="Trace networks" type="traceNetworks"/>
            <typeSubGroup displayName="Terrains" type="terrains"/>
            <typeSubGroup displayName="Geometric networks" type="geometricNetworks"/>
            <typeSubGroup displayName="Topologies" type="topologies"/>
            <typeSubGroup displayName="Parcel fabrics for ArcMap" type="parcelFabricsForArcMap"/>
          </typeGroup>
          <typeGroup displayName="Raster and imagery data" type="allRasterDatasets">
            <typeSubGroup displayName="Raster datasets" type="rasterDatasets"/>
            <typeSubGroup displayName="Mosaic datasets" type="mosaicDatasets"/>
            <typeSubGroup displayName="Oriented imagery datasets" type="orientedImageryDatasets"/>
          </typeGroup>
          <typeGroup displayName="Databases" type="databases">
            <typeSubGroup displayName="File geodatabases" type="fileGeodatabases"/>
            <typeSubGroup displayName="Mobile geodatabases" type="mobileGeodatabases"/>
            <typeSubGroup displayName="SQLite databases and geopackages" type="sqliteDatabasesAndGeoPackages"/>
          </typeGroup>
          <typeGroup displayName="Connections" type="allDatabaseConnections">
            <typeSubGroup displayName="Enterprise connections" type="enterpriseConnections"/>
            <typeSubGroup displayName="Database connections" type="databaseConnections"/>
            <typeSubGroup displayName="OLE DB Connection" type="oleDbConnections"/>
          </typeGroup>
        </content>
      </component>
    </insertCategory>

    <insertCategory id="localTypesFilterForMaps">
      <component id="localMaps">
        <content>
          <typeGroup displayName="Maps" type="allMaps">
            <typeSubGroup displayName="Maps" type="maps"/>
            <typeSubGroup displayName="Scenes" type="scenes"/>
            <typeSubGroup displayName="Stereo maps" type="stereoMaps"/>
            <typeSubGroup displayName="Basemaps" type="basemaps"/>
            <typeSubGroup displayName="Link charts" type="linkCharts"/>
          </typeGroup>
          <typeGroup displayName="Layers" type="layers">
            <typeSubGroup displayName="Map and scene layers" type="mapAndSceneLayers"/>
            <typeSubGroup displayName="Standalone tables" type="standaloneTables"/>
          </typeGroup>
        </content>
      </component>
    </insertCategory>
    
    <!-- browse places -->
    <updateCategory refID="esri_browsePlaces">
      <insertComponent id="project_gebrowse_place_project" className="BrowseProjectViewModelProvider">
        <content includeByDefault="True" contextMenuID="esri_projectDefaultContextMenu"/>
      </insertComponent>
    </updateCategory>

    <!-- Package Manager inner add-ins -->
    <insertCategory id="esri_packagemanagerTabs">
      <customControl id="python_old_backstage" className="ArcGIS.Desktop.Internal.Core.Conda.Backstage.CondaTab">
        <content displayName="Python" toolTip="Configure Conda Environments and Packages"/>
      </customControl>
    </insertCategory>

    <insertCategory id="esri_core_ai_assistant_extension_category" />
    <insertCategory id="esri_core_ai_enterprise_skill_category" />
  </categories>

  <ribbonToolbar>
    <!--<control refID="esri_activeTool" size="middle"/>-->
    <!--<control refID="esri_core_notificationControl" size="small"/>-->
    <!--<control refID="esri_core_notificationButton" size="small"/>-->
  </ribbonToolbar>
  
  <quickAccessToolbar>
    <control refID="esri_core_newProjectButton" size="small"/>
    <control refID="esri_core_openProjectButton" size="small"/>
    <control refID="esri_core_saveProjectButton" size="small"/>
    <control refID="esri_core_undoSplitButton" size="small"/>   
    <control refID="esri_core_redoSplitButton" size="small"/>
    <control refID="esri_dockPaneSetDynamicMenu" size ="small" isChecked="false"/>
    <control refID="esri_mapping_exploreTool" size ="small" isChecked="false"/>
    <control refID="esri_mapping_rectangleZoom" size ="small" isChecked="false"/>
    <control refID="esri_mapping_showLocateDockPane" size ="small" isChecked="false"/>
  </quickAccessToolbar>

  <modules>
    <insertModule id="esri_core_module" className="CoreModule" caption="Project Module" description="Project Module" autoLoad="true">

      <panes>
        <pane id="esri_core_startPage" caption="Start Page" className="StartPageViewModel" smallImage="StartPage16" isClosable="true">
          <content className="StartPage" />
        </pane>

        <pane id="esri_core_resourcesPane" caption="Catalog" extendedCaption="Catalog View" className="ProjectPaneVM" smallImage="ArcGISProject16" isClosable="true" defaultTab="esri_core_homeTab" isDropTarget="false">
          <content className="ProjectPane" />
          <keyCommands>
            <keyCommand id="esri_core_ShowCatalogOptions" caption="Catalog Browsing Options" tooltip="Open the Catalog Browsing tab in the Options dialog box."/>
            <keyCommand id="esri_core_SearchCatalog" caption="Search" tooltip="Go to the Search text box."/>
            <keyCommand id="esri_core_FocusContent" caption="Contents List" tooltip="Go to Catalog view contents list."/>
            <keyCommand id="esri_core_FocusTOC" caption="Contents Pane" tooltip="Go to the Catalog view's Contents pane."/>
            <keyCommand id="esri_core_ToggleDetails" caption="Show/hide Details Panel" tooltip="Show/hide the Details panel."/>
            <keyCommand id="esri_core_Breadcrumb" caption="Go To Location Bar" tooltip="Go to the location bar."/>
            <keyCommand id="esri_core_MoveBack" caption="Go Back" tooltip="Go back to the previous location."/>
            <keyCommand id="esri_core_MoveForward" caption="Go Forward" tooltip="Return to a visited location."/>
            <keyCommand id="esri_core_MoveUp" caption="Go Up" tooltip="Go up one level."/>
            <keyCommand id="esri_core_ShowSortMenu" caption="Sort Menu" tooltip="Show sort menu."/>
            <keyCommand id="esri_core_ShowFilterMenu" caption="Filter Menu" tooltip="Show filter menu."/>
            <keyCommand id="esri_core_ShowPortal" caption="Go To Portal" tooltip="Go to Portal."/>
            <keyCommand id="esri_core_ShowComputer" caption="Go To Computer" tooltip="Go to Computer."/>
            <keyCommand id="esri_core_ShowFavorites" caption="Go To Favorites" tooltip="Go to Favorites."/>
            <keyCommand id="esri_core_ShowProject" caption="Go To Project" tooltip="Go to Project."/>
            <keyCommand id="esri_core_ActivateCatalogPane" caption="Catalog Pane" tooltip="Go to the Catalog pane." />
          </keyCommands>
        </pane>
      </panes>

      <dockPanes>
        <dockPane id="esri_core_projectDockPane" caption="Catalog" extendedCaption="Catalog Pane"
                  className="ProjectDockPaneVM" dock="right" width="320" smallImage="CatalogShowTree16">
          <content className="ProjectDockPane" />
          <keyCommands>
            <keyCommand id="esri_core_MoveBack" caption="Go Back" tooltip="Go back to the previous location."/>
            <keyCommand id="esri_core_ShowSortMenu" caption="Sort Menu" tooltip="Show sort menu."/>
            <keyCommand id="esri_core_FocusContent" caption="Contents List" tooltip="Go to Catalog view contents list."/>
            <keyCommand id="esri_core_ShowFilterMenu" caption="Filter Menu" tooltip="Show filter menu."/>
            <keyCommand id="esri_core_ShowCatalogOptions" caption="Catalog Browsing Options" tooltip="Open the Catalog Browsing tab in the Options dialog box."/>
            <keyCommand id="esri_core_SearchCatalog" caption="Search" tooltip="Go to the Search text box."/>
            <keyCommand id="esri_core_PortalDP" caption="Go To Portal" tooltip="Go to Portal."/>
            <keyCommand id="esri_core_ShowComputer" caption="Go To Computer" tooltip="Go to Computer."/>
            <keyCommand id="esri_core_ProjectDP" caption="Go To Project" tooltip="Go to Project."/>
            <keyCommand id="esri_core_FavoritesDP" caption="Go To Favorites" tooltip="Go to Favorites."/>
            <keyCommand id="esri_core_ActivateResourcesPane" caption="Catalog View" tooltip="Go to the Catalog view." />
          </keyCommands>
        </dockPane>
        <!--<dockPane id="esri_core_projectDockPane" caption="Project" className="ProjectDockPaneViewModel" dock="right"  keytip="prj">
          <content className="ProjectDockPaneView" />
        </dockPane>-->
        <dockPane id="esri_core_contentsDockPane" caption="Contents" className="ContentsDockPaneViewModel" dock="left"
                  width="320" keytip="toc" delayLoadMessage="Open a map or layout view" smallImage="ContentsWindowShow16">
          <content className="ContentsDockPane" />
        </dockPane>
        <dockPane id="esri_core_notificationDockPane" smallImage="NotificationIndicator16" caption="Notifications" className="ArcGIS.Desktop.Framework.NotificationDockPaneViewModel"
            assembly="ArcGIS.Desktop.Framework.dll"  dock="right"  keytip="">
          <content className="ArcGIS.Desktop.Framework.NotificationDockPane" assembly="ArcGIS.Desktop.Framework.dll" />
        </dockPane>
        <dockPane id="esri_core_projectHistoryDockPane" smallImage="GenericHistory16" caption="History" className="ProjectHistoryDockPaneViewModel"
                  dock="group" dockWith="esri_core_projectDockPane">
          <content className="ProjectHistoryDockPane" />
        </dockPane>
      </dockPanes>

      <tabs>
        <tab id="esri_core_homeTab" caption="Catalog" condition="esri_core_resourcesPane" keytip="C">
          <group refID="esri_core_clipboardGroup" />
          <group refID="esri_core_organize" />
          <group refID="esri_core_create" />
          <group refID="esri_core_update" />
          <group refID="esri_metadata_metadataViewGroup" />
          <group refID="esri_core_portalProject"/>
        </tab>
        <tab id="esri_core_insertTab" caption="Insert"  keytip="N">
          <group refID="esri_core_projectData"/>
          <group refID="esri_reportTemplates_reportGroup"/>
          <group refID="esri_reports_reportGroup"/>
          <group refID="esri_reportTemplates_insertDataGroup"/>
          <group refID="esri_reports_insertDataGroup"/>
          <group refID="esri_reportTemplates_insertMapFrameGroup"/>
          <group refID="esri_reports_insertMapFrameGroup"/>
          <group refID="esri_reports_insertTextGroup"/>
          <group refID="esri_layouts_insertMapFrameGroup"/>
          <group refID="esri_layouts_insertMapSurroundGroup"/>
          <group refID="esri_project_layerTemplate"/>
          <group refID="esri_layouts_insertGraphicGroup"/>
          <group refID="esri_reports_insertGraphicGroup"/>
          <group refID="esri_presentations_insertPagesGroup"/>
          <group refID="esri_presentations_insertElementsGroup"/>
          <group refID="esri_project_styles"/>
          <group refID="esri_core_favoritesData"/>
        </tab>
        <tab id="esri_core_analysisTab" caption="Analysis"  keytip="A">
          <group refID="esri_geoprocessing_analysisTools" />
          <group refID="esri_geoprocessing_ToolsGallery" />
          <group refID="esri_geoprocessing_portal" />
          <group refID="esri_geoprocessing_analysis2" />
          <group refID="esri_datasourcesraster_rasterLayerTemplateGroup"/>
        </tab>
        <tab id="esri_core_viewTab" caption="View"  keytip="V">
          <group refID="esri_mapping_viewGroup"/>
          <group refID="esri_mapping_syncViewGroup"/>
          <group refID="esri_core_dockWindows" />
          <group refID="esri_core_projectViewOptionsGroup" />
          <group refID="esri_layouts_thumbnailGroup"/>
          <group refID="esri_mapping_thumbnailGroup"/>
          <group refID="esri_mapping_accessibilityGroup"/>
          <group refID="esri_mapping_animation"/>
          <group refID="esri_mapping_effectsGroup"/>
          <group refID="esri_mapping_sceneGroup"/>
          <group refID="esri_mapping_viewClippingGroup"/>
          <group refID="esri_mapping_profileViewGroup"/>
          <group refID="esri_mapping_navigation"/>      
        </tab>
        
        <tab id="esri_core_previewTab" caption="Preview" condition="esri_core_previewActiveCondition" tabGroupID="esri_core_previewTabGroup" keytip="W" insert="after">
          <group refID="esri_core_previewNavigateGroup" />
          <group refID="esri_core_previewPreviewGroup" />
          <group refID="esri_core_previewThumbnail" />
        </tab>

        <tab id="esri_core_helpTab" caption="Help" insert="after" keytip="H">
          <group refID="esri_core_customizeGroup"/>
          <group refID="esri_core_helpGroup"/>
          <group refID="esri_core_performanceGroup"/>
          <group refID="esri_core_contactGroup"/>
        </tab>
      </tabs>

      <tabGroups>
        <tabGroup caption="Catalog View" id="esri_core_previewTabGroup">
          <color A="255" R="238" G="170" B="90" />
          <borderColor A="0" R="251" G="226" B="195" />
        </tabGroup>
      </tabGroups>

      <groups>
        <group id="esri_core_helpGroup" caption="Help" keytip="H" smallImage="ArcGISProHelp16">
          <menu refID="esri_core_helpMenu" size="large"/>
          <button refID="esri_core_learningBtn" size="large" />
        </group>
        <group id="esri_core_customizeGroup" caption="Customize" keytip="C" smallImage="CogWheel16">
          <button refID="esri_core_ribbonBtn" size="middle" />
          <button refID="esri_core_qatBtn" size="middle" />
          <button refID="esri_core_Shortcuts" size="middle" />
        </group>
        <group id="esri_core_performanceGroup" caption="Performance" keytip="P" smallImage="DiagnosticsMonitor16">
          <button refID="esri_core_monitorBtn" size="large" />
        </group>
        <group id="esri_core_contactGroup" caption="Contact Us" keytip="C" smallImage="SpeechBubbleUI16">
          <button refID="esri_core_communityBtn" size="large" />
          <button refID="esri_core_supportBtn" size="large" />
        </group>

        <group id="esri_core_projectData" caption="Project" sizePriorities="25,125,225" smallImage="ArcGISProject16">
          <button refID="esri_mapping_newMapPalette"/>
          <gallery refID="esri_layouts_gallery"/>
          <button refID="esri_reports_newReportSplitButton"/>
          
          <button refID="esri_presentations_newPresentationSplitButton" size="middle" />
          <splitButton refID="esri_geodatabase_AddNewOrExistingNotebookSplitButton" size="middle"/>
          <buttonPalette refID="esri_geoprocessing_insertMenu" size="middle"/>
          
          <button refID="esri_layouts_importMap" size="middle" />
          <gallery refID="esri_layouts_templateGallery" size="middle" />
          <buttonPalette refID="esri_task_insertMenu" size="middle"/>
          
          <menu refID="esri_geodatabase_insertMenu" size="large"/>
          <button refID="esri_geodatabase_addFolderConnectionButton" size="large"/>       
        </group>

        <group id="esri_core_favoritesData" caption="Favorites" sizePriorities="25,125,225" smallImage="FavoriteStar16">

        </group>

        <group id="esri_core_projectViewOptionsGroup" caption="Options" smallImage="GenericOptions16" condition="esri_core_projectItem_resourcesPane">
          <buttonPalette refID="esri_core_popupTooltipPalette" size="large" />
          <button refID="esri_core_projectViewLayoutCheckBoxOpen" size="middle" />
          <buttonPalette refID="esri_core_projectViewDisplayType" size="middle" />
        </group>

        <group id="esri_core_clipboardGroup" caption="Clipboard" smallImage="EditPaste16">
          <buttonPalette refID="esri_core_editPasteSplitButton" size="large"  />
          <subgroup refID="esri_core_editBtns"/>
        </group>

        <group id="esri_core_organize" caption="Organize" smallImage="EditPaste16">
          <button refID="esri_core_openButton" size="large" />
          <subgroup refID="esri_core_organizeItems1"/>
          <subgroup refID="esri_core_organizeItems2"/>
          <subgroup refID="esri_core_organizeItems3"/>
        </group>

        <group id="esri_core_update" caption="Update" smallImage="EditPaste16">
        </group>

        <group id="esri_core_create" caption="Create" smallImage="EditPaste16">
          <dynamicMenu refID="esri_core_newItemsDynamicMenu"/>
          <dynamicMenu refID="esri_core_addItemsDynamicMenu"/>
          <dynamicMenu refID="esri_core_importItemsDynamicMenu"/>
          <!--<subgroup refID="esri_core_newItems"/>-->
        </group>

        <group id="esri_core_dockWindows" caption="Windows" smallImage="ArcGISProjectWindow16" >
          <dynamicMenu refID="esri_dockPaneSetDynamicMenu" size ="large"/>
          <subgroup refID="esri_core_WindowControls"/>
          <subgroup refID="esri_core_WindowControlsGP"/>
          <subgroup refID="esri_core_WindowControlsExtensions"/>
        </group>

        <group id="esri_core_previewNavigateGroup" caption="Navigate">
          <button refID="esri_core_previewZoomFullButton" size="small" />
          <button refID="esri_core_previewFixedZoomInButton" size="small" />
          <button refID="esri_core_previewPrevExtentButton" size="small" />
          <button refID="esri_core_previewZoomToSelectionButton" size="small" />
          <button refID="esri_core_previewFixedZoomOutButton" size="small" />
          <button refID="esri_core_previewNextExtentButton" size="small" />
        </group>

        <group id="esri_core_previewPreviewGroup" caption="Preview">
          <button refID="esri_core_previewShowBasemap" size="middle" />
        </group>

        <group id="esri_core_previewThumbnail" caption="Thumbnail">
          <button refID="esri_core_previewCaptureThumbnail" size="large" />
        </group>
      </groups>

      <palettes>
        <buttonPalette id="esri_core_popupTooltipPalette" caption="Item Pop-ups" keytip="SC" itemsInRow="1" showItemCaption="true" bindChecked="false">
          <tooltip heading="Display Type">Choose how popups are displayed.</tooltip>
          <button refID="esri_core_HideItemTooltips"/>
          <button refID="esri_core_ShowItemTooltips"/>
          <button refID="esri_core_ShowDetailedItemTooltips"/>
        </buttonPalette>
        
        <buttonPalette id="esri_core_projectViewDisplayType" caption="Display Type" temsInRow="1" showItemCaption="true" keytip="DT">
          <button refID="esri_core_projectDetailsView"/>
          <button refID="esri_core_projectTilesView"/>
        </buttonPalette>
      
    </palettes>

      <controls>
        <!-- Actipro shortcuts-->
        <button id="esri_core_paneOptions" caption="Pane Options">
          <tooltip heading="">Access options to float, dock, or close the active view or pane.</tooltip>
        </button>
        <button id="esri_core_changePane" caption="Change Active Pane">
          <tooltip heading="">Browse available panes and change the active pane.</tooltip>
        </button>
        <button id="esri_core_changeView" caption="Change Active View">
          <tooltip heading="">Browse available views and change the active view.</tooltip>
        </button>
        <button id="esri_core_switchView" caption="Switch Active View">
          <tooltip heading="">Switch the active view to the next available view.</tooltip>
        </button>
        <button id="esri_core_closeView" caption="Close Active View">
          <tooltip heading="">Close the active view.</tooltip>
        </button>
        <button id="esri_core_minimizeRibbon" caption="Minimize the Ribbon">
          <tooltip heading="">Show only the tab names on the ribbon.</tooltip>
        </button>
        <button id="esri_core_viewHelp" caption="Help">
          <tooltip heading="">View help.</tooltip>
        </button>
        <!-- end Actipro shortcuts -->
        <button id="esri_core_onlineHelpBtn" caption="Online Help" keytip="O"
                className="ArcGIS.Desktop.Framework.Help.OnlineHelpCmd" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="" largeImage="">
          <tooltip heading="Online Help">Open the web help.</tooltip>
        </button>
        <button id="esri_core_offlineHelpBtn" caption="Offline Help" keytip="H" loadOnClick="false"
                className="ArcGIS.Desktop.Framework.Help.OfflineHelpCmd" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="" largeImage="">
          <tooltip heading="Offline Help">Open the offline help.
            <disabledText>Install the offline help to enable this command.</disabledText>
          </tooltip>
        </button>
        <button id="esri_core_languageBtn" caption="Language Settings..." keytip="L" helpContextID="120001217"
                className="ArcGIS.Desktop.Framework.Help.LanguageCmd" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="GenericOptions16" largeImage="GenericOptions32">
          <tooltip heading="Language Settings">Choose the language to display. Languages appear as offline help options after their corresponding language pack for the help is installed.</tooltip>
        </button>
        <button id="esri_core_learningBtn" caption="Learning Resources" keytip="L" helpContextID="120003935"
                className="ArcGIS.Desktop.Framework.Help.LearningCmd" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="ArcGISLearnColor16" largeImage="ArcGISLearnColor32">
          <tooltip>Build your ArcGIS Pro skills with tutorials and other resources.</tooltip>
        </button>
        <button id="esri_core_ribbonBtn" caption="Ribbon" keytip="R" helpContextID="120001218"
                className="ArcGIS.Desktop.Framework.Help.RibbonCmd" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="CustomizeRibbon16" largeImage="CustomizeRibbon32">
          <tooltip heading="Customize the Ribbon">Arrange commands on ribbon tabs to make your interaction more efficient.</tooltip>
        </button>
        <button id="esri_core_qatBtn" caption="Quick Access Toolbar" keytip="Q" helpContextID="120001461"
                className="ArcGIS.Desktop.Framework.Help.QatCmd" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="QuickAccessToolbar16" largeImage="QuickAccessToolbar32">
          <tooltip heading="Customize Quick Access Toolbar">Add, remove, and reorder commands on the Quick Access Toolbar.</tooltip>
        </button>
        <button id="esri_core_monitorBtn" caption="Diagnostic Monitor" keytip="M" helpContextID="120002882"
                className="ArcGIS.Desktop.Framework.Help.MonitorCmd" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="DiagnosticsMonitor16" largeImage="DiagnosticsMonitor32">
          <tooltip heading="Diagnostic Monitor">Identify the cause of application problems by monitoring the flow of status information, logs, and events.</tooltip>
        </button>
        <button id="esri_core_supportBtn" caption="Technical Support" keytip="T"
                className="ArcGIS.Desktop.Framework.Help.SupportCmd" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="BusinessAnalystSupport32" largeImage="BusinessAnalystSupport32">
          <tooltip heading="Technical Support">Contact Esri Technical Support to troubleshoot software problems.</tooltip>
        </button>
        <button id="esri_core_communityBtn" caption="Esri Community" keytip="C"
                className="ArcGIS.Desktop.Framework.Help.CommunityCmd" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="ArcGISProGeoNetColor16" largeImage="ArcGISProGeoNetColor32">
          <tooltip heading="Esri Community">Engage with the community of ArcGIS Pro users to find answers to your questions, provide answers to others, suggest ideas, and more.</tooltip>
        </button>

        <button id="esri_core_notificationButton" className="NotificationButton" caption="Notification" keytip="W">
          <tooltip heading="Notifications">
            See messages regarding ArcGIS Pro, the project, or items in the project. Notifications are available when there is a blue dot.<disabledText></disabledText>
          </tooltip>
        </button>
        
        <button id="esri_core_SignOnRootCommand" className="SignInRootCmd" caption="SignOn">
          <tooltip heading="Sign-in Status">
            Sign in or out of ArcGIS Online or your ArcGIS organization.
          </tooltip>
        </button>
           
        <button id="esri_itemInfoRefreshButton" className="RefreshCmd" loadOnClick="false"
          caption="Refresh" extendedCaption="Refresh the selected container in the Project pane; on context menu"
          smallImage="GenericRefresh16" helpContextID="">
          <tooltip heading="">Update the contents list to include any items that were recently added to this location.</tooltip>
        </button>

        <!-- show project dock pane -->
        <button id="esri_core_showProjectDockPane" className="esri_core_module:ShowProjectDockPane"
                caption="Catalog Pane" extendedCaption="Show the Catalog pane; on ribbon"
                condition="esri_mapping_openProjectCondition" keytip="CP" helpContextID="120003938"
                largeImage="ArcGISProjectWindow32"
                smallImage="ArcGISProjectWindow16">
          <tooltip heading="Catalog">
            Show the Catalog pane. You can access items in and available to the project, whether they are available on a local or network computer, or from a database, a server, ArcGIS Online, or your organization's portal.

With the Catalog pane you can drag and drop items onto a map, a model, or a tool parameter. Access geoprocessing and raster function history, and add your favorite items to the project.<disabledText></disabledText>
          </tooltip>
        </button>
        <!-- show project view -->
        <button id="esri_core_showProjectView" className="esri_core_module:ShowProjectView"
                caption="Catalog View" extendedCaption="Show the Catalog view; on ribbon"
                condition="esri_mapping_openProjectCondition" keytip="CV" helpContextID="120003939"
                largeImage="ArcGISProject32"
                smallImage="ArcGISProject16">
          <tooltip heading="">
            Open a view that provides access to items in and available to the project. All items and actions that are available in the Catalog pane are also available in the Catalog view. You can also access detailed information about available items.

With the Catalog view you can sort items, preview spatial data, edit an item’s metadata, manage styles and other items, and add your favorite items to the project.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_projectDetailsView"
                className="ArcGIS.Desktop.Internal.Core.CatalogViewDetails"
                condition="esri_core_resourcesPane"
                smallImage="ModelBuilderAddList16"
                largeImage="ModelBuilderAddList32"
                caption="Columns" extendedCaption="Display Type Columns" keytip="C" loadOnClick="true">
          <tooltip heading="Columns">
            Display information for each item in columns.
          </tooltip>
        </button>

        <button id="esri_core_projectTilesView"
                className="ArcGIS.Desktop.Internal.Core.CatalogViewTiles"
                condition="esri_core_resourcesPane"
                smallImage="ViewGallery16"
                largeImage="ViewGallery32"
                caption="Tiles" extendedCaption="Display Type Tiles" keytip="T" loadOnClick="true">
          <tooltip heading="Tiles">
            Display a thumbnail and other information on tiles.
          </tooltip>
        </button>

        <!-- Backstage -->
        <button id="esri_core_showOptionsSheetButton" className="esri_core_module:ArcGIS.Desktop.Internal.Core.IInternalCoreModule.ShowOptionsSheet"
                caption="Options" extendedCaption="Show the Options dialog; on the Project tab"
                largeImage="GenericOptions32"
                smallImage="GenericOptions16" keytip="T">
          <tooltip heading="">
            Show the Options dialog box.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_showHelpButton" className="esri_core_module:ShowHelp" caption="Help" extendedCaption="Show the Help system; on the Project tab" keytip="H">
          <tooltip>
            Get Help for the application.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_exitApplicationButton" className="esri_core_module:ExitApplication" caption="Exit" extendedCaption="Stop ArcGIS Pro; on the Project tab" smallImage="Close16" keytip="X">
          <tooltip heading="">
            Close the ArcGIS Pro application.<disabledText></disabledText>
          </tooltip>
        </button>
        <!--Home - Clipboard-->
        <button id="esri_core_editPasteButton" caption="Paste" extendedCaption="Paste the item on the clipboard"
                keytip="P" menuKeytip="P" loadOnClick="false"
                className="ArcGIS.Desktop.Framework.PasteCMD" assembly="ArcGIS.Desktop.Framework.dll"
                largeImage="EditPaste32"
                smallImage="EditPaste16" >
          <tooltip heading="">
            Paste from clipboard.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_editPasteSpecialButton" caption="Paste Special" extendedCaption="Paste the item on the clipboard using special settings" keytip="S" loadOnClick="false"
                className="ArcGIS.Desktop.Framework.PasteSpecialCMD" assembly="ArcGIS.Desktop.Framework.dll"
                largeImage="EditPaste32"
                smallImage="EditPaste16"
                helpContextID="120002789">
          <tooltip heading="">
            Paste special from clipboard.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_editCutButton" caption="Cut" extendedCaption="Cut the selected item and place the item on the clipboard" loadOnClick="false" keytip="X" menuKeytip="U"
                className="ArcGIS.Desktop.Framework.CutCMD" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="EditCut16"
                largeImage="EditCut32">
          <tooltip heading="">
            Cut selection to the clipboard.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_editCopyButton" caption="Copy" extendedCaption="Copy the highlighted item(s) to the clipboard"
                loadOnClick="false" keytip="CO" menuKeytip="C"
                className="ArcGIS.Desktop.Framework.CopyCMD" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="EditCopy16"
                largeImage="EditCopy32">
          <tooltip heading="">
            Copy selection to the clipboard.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_duplicateButton" caption="Duplicate" extendedCaption="Duplicate the highlighted item(s)"
              loadOnClick="false" keytip="CO" menuKeytip="U"
              className="ArcGIS.Desktop.Framework.DuplicateCMD" assembly="ArcGIS.Desktop.Framework.dll"
              smallImage="DuplicateItem16"
              largeImage="DuplicateItem32">
          <tooltip heading="">
            Duplicate the selected item(s).<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_editCopyPaths" caption="Copy Path"
                extendedCaption="Copy the path of the selected items to the Clipboard."
                loadOnClick="false" keytip="CP" menuKeytip="Y"
                className="ArcGIS.Desktop.Framework.CopyPathCMD" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="CopyPath16"
                largeImage="CopyPath32">
          <tooltip heading="">
            Copy the path of the selected items to the Clipboard.
            <disabledText>
              Requires a selection in an active Catalog pane or view, or in the catalog view's Contents pane.
            </disabledText>
          </tooltip>
        </button>
        <button id="esri_core_Remove" className="RemoveProjectItemCmd" loadOnClick="false" keytip="RM"
                caption="Remove" extendedCaption="Remove the item from the project."
                smallImage="GenericDeleteBlack16">
          <tooltip heading="">
            Remove the selected items from the project or remove the selected items from the Favorites collection. This command does not delete items.
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_editDeleteButton"
                caption="Delete" extendedCaption="Delete the selection; on map editing context menu"
                loadOnClick="false" keytip="D" menuKeytip="D"
                className="ArcGIS.Desktop.Framework.DeleteCMD" assembly="ArcGIS.Desktop.Framework.dll"
                smallImage="GenericDiscard16"
                largeImage="GenericDiscard32">
          <tooltip heading="">
            Delete the selected items. For items that reference a file or folder on disk, the file or folder is deleted in the file system.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_newFolderButton"
                caption="Folder" extendedCaption="Add new folder"
                loadOnClick="false" keytip="NF"
                className="CreateFolderCmd"
                smallImage="FolderNew16"
                largeImage="FolderNew32">
          <tooltip heading="">
            Create a new folder.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_repairProjectItem"
                caption="Repair" extendedCaption="Repair the broken project item"
                loadOnClick="false" keytip="RI"
                className="RepairProjectItemCmd"
                helpContextID="120003936"
                smallImage="ProjectRepair16"
                largeImage="ProjectRepair32">
          <tooltip heading="">
            Repair the broken project item. 
            <disabledText>A project item can only be repaired when the project's reference to the item is broken, for example, when the file or folder associated with the project item cannot be located on the file system.</disabledText>
          </tooltip>
        </button>

        <!--Project related buttons-->
        <button id="esri_core_saveProjectButton" className="Project+ProjectSaveCmd" flipImageRTL="true" caption="Save Project" extendedCaption="Save changes to the current project; on Project tab" loadOnClick="false" condition="esri_mapping_openProjectCondition" largeImage="ArcGISProjectSave32" smallImage="ArcGISProjectSave16" keytip="S">
          <tooltip heading="">
            Save the project.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_saveProjectAsButton" className="Project+ProjectSaveAsCmd" flipImageRTL="true" caption="Save Project As" extendedCaption="Save the current project to a new file; on Project tab" condition="esri_mapping_openProjectCondition" largeImage="ArcGISProjectSaveAs32" smallImage="ArcGISProjectSaveAs16" keytip="A">
          <tooltip heading="">
            Save a copy of the ArcGIS Project file to a new location or with a new name.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_newProjectButton" className="esri_core_module:CreateNewProject" caption="New Project" smallImage="ArcGISProjectNew16">
          <tooltip heading="New Project">
            Create a new project.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_openProjectButton" className="esri_core_module:OpenProject" caption="Open Project" largeImage="ArcGISProjectOpenState32" smallImage="ArcGISProjectOpenState16">
          <tooltip heading="Open Project">
            Open an existing project.<disabledText></disabledText>
          </tooltip>
        </button>

        <labelControl id="esri_emptyLabel1" caption=" " hasTwoLines="false" />

        <!--Undo / Redo controls-->
        <customControl id="esri_core_undoHistory" caption="Undo" extendedCaption="Show the history of previous operations that can be undone; on quick access toolbar" className="ArcGIS.Desktop.Framework.UndoRedo.UndoHistoryVM" assembly="ArcGIS.Desktop.Framework.dll" smallImage="EditUndo_B_16" loadOnClick="false">
          <content className="ArcGIS.Desktop.Framework.UndoRedo.UndoRedoControl" assembly="ArcGIS.Desktop.Framework.dll" />
          <tooltip heading="Undo">
            Undo the previous operation.<disabledText></disabledText>
          </tooltip>
        </customControl>
        <customControl id="esri_core_redoHistory" caption="Redo" extendedCaption="Show the history of previous operations that can be redone; on quick access toolbar" className="ArcGIS.Desktop.Framework.UndoRedo.RedoHistoryVM" assembly="ArcGIS.Desktop.Framework.dll" smallImage="EditRedo_B_16" loadOnClick="false">
          <content className="ArcGIS.Desktop.Framework.UndoRedo.UndoRedoControl" assembly="ArcGIS.Desktop.Framework.dll" />
          <tooltip heading="Redo">
            Redo the previous operation.<disabledText></disabledText>
          </tooltip>
        </customControl>
        <button id="esri_core_undoButton" caption="Undo" extendedCaption="Undo the previous operation; on quick access toolbar" className="ArcGIS.Desktop.Framework.UndoRedo.UndoCommand" assembly="ArcGIS.Desktop.Framework.dll" flipImageRTL="true" largeImage="EditUndo_B_32" smallImage="EditUndo_B_16" loadOnClick="false">
          <tooltip heading="">
            Undo the previous operation.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_redoButton" caption="Redo" extendedCaption="Redo the previous operation; on quick access toolbar" className="ArcGIS.Desktop.Framework.UndoRedo.RedoCommand" assembly="ArcGIS.Desktop.Framework.dll" flipImageRTL="true" largeImage="EditRedo_B_32" smallImage="EditRedo_B_16" loadOnClick="false">
          <tooltip heading="">
            Redo the previous operation.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_Shortcuts" caption="Shortcuts" extendedCaption="Show current shortcuts" keytip="S"
                className="ArcGIS.Desktop.Internal.Framework.Shortcuts.ShortcutsCmd" assembly="ArcGIS.Desktop.Framework.dll"
                largeImage="Keyboard32"
                smallImage="Keyboard16"
                helpContextID="120003886"
                loadOnClick="false">
          <tooltip heading="Keyboard Shortcuts">Review or modify current keyboard shortcuts and add new ones.</tooltip>
        </button>

        <!--Dockpane control-->
        <dynamicMenu id="esri_dockPaneSetDynamicMenu"
                     className="ArcGIS.Desktop.Framework.PaneStates.DockStateMenu"
                     assembly="ArcGIS.Desktop.Framework.dll"
                     caption="Pane Sets" extendedCaption="Reset application panes."
                     largeImage="ResetPanes32" smallImage="ResetPanes16"
                     keytip="RP" hidden="false">
          <tooltip heading="Pane Sets">
            Reset application panes to a predefined set.
            <disabledText></disabledText>
          </tooltip>
        </dynamicMenu>
           

        <!-- basic operations on project items -->
        <button id="esri_core_rename" caption="Rename" keytip="RN" menuKeytip="R" extendedCaption="Rename the selected item; on context menu"
                className="RenameCmd"
                smallImage="Rename16"
                largeImage="Rename32"
                loadOnClick="false">
          <tooltip heading="Rename">
            Rename the selected item. For project items that reference a file or folder on disk, the file or folder is renamed in the file system.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_openFileLocation" caption="Show In File Explorer" keytip="OF" menuKeytip="X" extendedCaption="Open the file location for the selected item; on context menu"
           className="OpenFileLocationCmd"
           smallImage="GenericOpen16"
           largeImage="GenericOpen32"
           loadOnClick="false">
          <tooltip heading="Show In File Explorer">
            Open the file location for the selected item.<disabledText></disabledText>
          </tooltip>
        </button>
        
        <button id="esri_core_rename_alias" caption="Change Alias" keytip="CA" extendedCaption="Change the selected item's alias"
                className="RenameAliasCmd"
                smallImage="GenericPencil16"
                largeImage="GenericPencil32"
                loadOnClick="false">
          <tooltip heading="Change Alias">
            Change the selected item's alias.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_remove_alias" caption="Remove Alias" keytip="RA" extendedCaption="Remove the selected item's alias"
                className="RemoveAliasCmd"
                smallImage="GenericDeleteBlack16"
                largeImage="GenericDeleteBlack32"
                loadOnClick="false">
          <tooltip heading="Remove Alias">
            Remove the selected item's alias.<disabledText></disabledText>
          </tooltip>
        </button>

        <!--ribbonToolbar controls-->
        <customControl id="esri_activeTool" isDropDown="false" className="ArcGIS.Desktop.Framework.Controls.ActiveToolControlViewModel"
                       assembly="ArcGIS.Desktop.Framework.dll"
                       smallImage="User16"
                       loadOnClick="false">
          <content className="ArcGIS.Desktop.Framework.Controls.ActiveTool" assembly="ArcGIS.Desktop.Framework.dll"/>
          <tooltip heading="Active Tool">
            The current active tool. Click to see the active short-cuts.<disabledText></disabledText>
          </tooltip>
        </customControl>
        
        <customControl id="esri_core_notificationControl" isDropDown="false" className="ArcGIS.Desktop.Framework.NotificationControlViewModel"
            assembly="ArcGIS.Desktop.Framework.dll" smallImage="User16" loadOnClick="false" keytip="W">
          <content className="ArcGIS.Desktop.Framework.NotificationControlView" assembly="ArcGIS.Desktop.Framework.dll"/>
          <tooltip heading="Notifications">
            See messages regarding ArcGIS Pro, the project, or items in the project. Notifications are available when there is a blue dot.<disabledText></disabledText>
          </tooltip>
        </customControl>

        <customControl id="esri_core_CommandSearchControl" caption="Command Search" isDropDown="false" className="ArcGIS.Desktop.Framework.TellMeControlViewModel" helpContextID="120002723"
                  assembly="ArcGIS.Desktop.Framework.dll" smallImage="Search_Find16" largeImage="Search_Find32" loadOnClick="false" keytip="Q" hidden="true">
          <content className="ArcGIS.Desktop.Framework.TellMeControlView" assembly="ArcGIS.Desktop.Framework.dll"/>
          <tooltip heading="Command Search">
            Search for and execute commands, tools, and help.<disabledText></disabledText>
          </tooltip>
        </customControl>
        <button id="esri_core_showContentsDockPane" className="esri_core_module:ShowContents" caption="Contents"
                extendedCaption="Show the Contents pane; on ribbon" flipImageRTL="true" keytip="CT"
                largeImage="ContentsWindowShow32"
                smallImage="ContentsWindowShow16"
                helpContextID="120003937">
          <tooltip heading="">
            Show the Contents pane.<disabledText></disabledText>
          </tooltip>
        </button>

        <!-- linked items -->
        <button id="esri_core_unlinkPortalItemButton" className="esri_core_module:UnlinkPortalItem" caption="Unlink" smallImage="GenericOverlayLinked16" loadOnClick="false">
          <tooltip heading="">
            Unlink portal item<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_syncPortalItemButton" className="esri_core_module:SyncPortalItem" caption="Update" extendedCaption="Delete the copy of the item in the project and re-add the original portal item; on map context menu" smallImage="GenericOnlineSync16" loadOnClick="false">
          <tooltip heading="Update Map or Scene">
            Delete this map or scene and replace it with the current version of its linked portal item. To preserve changes you have made in ArcGIS Pro, make a copy of the map or scene, or save it as a map file, before you update.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_projectViewClearSort" className="CatalogSortButton"
                condition="esri_core_resourcesPane"
                smallImage="SortCustom16"
                largeImage="SortCustom32"
          caption="Catalog Sort" extendedCaption="Clear sorting" keytip="S" loadOnClick="false">
          <tooltip heading="Catalog Sort">
            Remove sorting that was applied to the contents list. Returns items in the list to their original order.
            <disabledText>Sorting can only be cleared from the contents list when using the columns display type.</disabledText>
          </tooltip>
        </button>

        <button id="esri_core_projectViewLayoutCheckBoxOpen" className="ArcGIS.Desktop.Internal.Core.ProjectViewChangeLayoutCheckBox" 
                condition="esri_core_resourcesPane"
                largeImage="HideDetailsPanel32"
                smallImage="HideDetailsPanel16"                
                caption="Details Panel" extendedCaption="Show the Details panel in the Catalog view, e.g. to see metadata or style items; on ribbon" keytip="SD" loadOnClick="false">
          <tooltip heading="">
            Show the Details panel in the Catalog view. This lets you preview an item's data, view its metadata, and manage style items within a style.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_HideItemTooltips" className="ArcGIS.Desktop.Internal.Core.HideTooltips"
                largeImage="CatalogPopUpDisable32"
                smallImage="CatalogPopUpDisable16"
                caption="Hide Pop-ups" extendedCaption="Show pop-ups in the catalog pane." keytip="NP" loadOnClick="false">
          <tooltip heading="Hide Pop-ups">
            Do not show information such as an item's location in a pop-up when you mouse over it in the Catalog pane.
          </tooltip>
        </button>
        <button id="esri_core_ShowItemTooltips" className="ArcGIS.Desktop.Internal.Core.ShowItemTooltips"
                caption="Show Pop-ups" extendedCaption="Show simple pop-ups in the catalog pane." keytip="SP" loadOnClick="false"
                largeImage="CatalogPopUp32"
                smallImage="CatalogPopUp16">
          <tooltip heading="Show Pop-ups">
            Show information such as an item's location in a pop-up when you mouse over it in the Catalog pane.
          </tooltip>
        </button>
        <button id="esri_core_ShowDetailedItemTooltips" className="ArcGIS.Desktop.Internal.Core.ShowDetailedItemTooltips"
                caption="Show Detailed Pop-ups" extendedCaption="Show detailed pop-ups in the catalog pane." keytip="AP" loadOnClick="false"
                largeImage="CatalogPopUpDetailed32"
                smallImage="CatalogPopUpDetailed16">
          <tooltip heading="Show Detailed Pop-ups">
Show information such as an item's thumbnail and tags as well as its location in a pop-up when you mouse over the item in the Catalog pane. This option can help you find an item when you are not familiar with the contents of a portal, disk, database, or server. However, it can reduce overall performance when browsing enterprise geodatabases and ArcGIS Server services, and when accessing items with a slow internet or network connection.
          </tooltip>
        </button>

        <button id="esri_core_previewZoomToSelectionButton" condition="esri_core_previewZoomToSelectionButton"
                className="CorePreviewItemZoomToSelection"
                caption="Zoom To Selection" extendedCaption="Zoom to selected features" keytip="ZS" loadOnClick="false"
                smallImage="SelectionZoomToSelected16">
          <tooltip heading="">
            Zoom to the selected features of all layers.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_previewZoomFullButton" caption="Zoom Full Extent" keytip="FE" className="CorePreviewItemZoomToFull" smallImage="ZoomFullExtent16" loadOnClick="false">
          <tooltip heading="Full Extent">
            Zoom to the full extent of the data in the view.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_previewFixedZoomInButton" caption="Fixed Zoom In" extendedCaption="Fixed zoom in" keytip="ZI" className="CorePreviewItemFixedZoomIn" smallImage="ZoomFixedZoomIn_B_16" loadOnClick="false">
          <tooltip heading="Fixed Zoom In">
            Zoom in a fixed distance.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_previewFixedZoomOutButton" caption="Fixed Zoom Out" extendedCaption="Fixed zoom out" keytip="ZO" className="CorePreviewItemFixedZoomOut" smallImage="ZoomFixedZoomOut_B_16" loadOnClick="false">
          <tooltip heading="Fixed Zoom Out">
            Zoom out a fixed distance.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_previewPrevExtentButton" caption="Previous Extent" extendedCaption="Previous extent" keytip="PE" flipImageRTL="true" className="CorePreviewItemPrevExtent" smallImage="GenericBlueLeftArrowLongTail16" loadOnClick="false">
          <tooltip heading="Previous Extent">
            Go to the previous extent.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_core_previewNextExtentButton" caption="Next Extent" extendedCaption="Next extent" keytip="NE" flipImageRTL="true" className="CorePreviewItemNextExtent" smallImage="GenericBlueRightArrowLongTail16" loadOnClick="false">
          <tooltip heading="Next Extent">
            Go to the next extent.<disabledText></disabledText>
          </tooltip>
        </button>
        <checkBox id="esri_core_previewShowBasemap" condition="esri_core_previewShowBasemapCondition" keytip="SB"
                  caption="Show Basemap" extendedCaption="" className="CorePreviewItemShowBasemapCheckBox" loadOnClick="false">
          <tooltip heading="">
            Toggles visibility of the basemap in preview.<disabledText></disabledText>
          </tooltip>
        </checkBox>
        <button id="esri_core_previewCaptureThumbnail" caption="Create" className="CorePreviewItemCaptureThumbnail"
                condition="esri_core_previewCaptureThumbnail_condition" keytip="TC"
                largeImage="ItemThumbnailCreate32"
                smallImage="ItemThumbnailCreate16" >
          <tooltip heading="">
            Captures thumbnail from the preview area and updates metadata.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_refresh" caption="Refresh" keytip="RF" menuKeytip="R" className="RefreshCmd" loadOnClick="false" 
                extendedCaption="Refresh the selected item" helpContextID="120002719"
                largeImage="GenericRefresh32"
                smallImage="GenericRefresh16">
          <tooltip heading="Refresh">Update the contents of a location or selected item.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_openButton" caption="Open" keytip="O" className="OpenCmd" loadOnClick="false"
                extendedCaption="Browse into the selected item"
                largeImage="launch32"
                smallImage="launch16">
          <tooltip heading="Open">Open the item in its view or pane, or browse into the item to see its contents.
            <disabledText></disabledText>
          </tooltip>
        </button>
        
        <button id="esri_core_addToProject" caption="Add To Project" keytip="AP" className="AddToProjectCmd" loadOnClick="false"
                largeImage="GenericAddGreen32"
                smallImage="GenericAddGreen16" menuKeytip="A">
          <tooltip heading="">Add an existing item to the project. The project saves a reference to the file or folder stored on the file system.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_removeFromProject" className="esri_core_module:RemoveProjectItem" keytip="RFP" menuKeytip="V" loadOnClick="false"
                caption="Remove From Project" extendedCaption="Remove item from the project"
                smallImage="GenericDeleteBlack16"
                helpContextID="">
          <tooltip heading="">Remove the item from the project.</tooltip>
        </button>
        
        <button id="esri_addFavoriteButton" className="AddToFavoritesCmd" loadOnClick="false"
                caption="Add To Favorites" keytip="FA" menuKeytip="F"
                extendedCaption="Add an item to project favorites; on context menu for folders, databases, and servers that have been added to the project"
                condition="esri_projectItem_isValidAndOpenProjectCondition"
                smallImage="FavoriteStar16" helpContextID="">
          <tooltip heading="">
            Add the item to project favorites. Project favorites saves a reference to the existing item. If the item is not in a location that is accessible from other machines, it won't be available when using a different computer.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_addProjectItemToNewProjectsButton" className="AddItemToNewProjectsCmd" loadOnClick="false"
                caption="Add To New Projects" keytip="FP" extendedCaption="Add to new projects"
                condition="esri_projectItem_isValidAndOpenProjectCondition"
                smallImage="PinFavorite16"
                largeImage="PinFavorite32"
                helpContextID="" menuKeytip="J">
          <tooltip heading="">
            Add the item to project favorites and to all new projects. Project favorites saves a reference to the existing item. When a new project is created, a reference to the item is automatically added.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_sortClear" caption="Clear" className="SortClearCmd"
                largeImage="SortCustom32"
                smallImage="SortCustom16">
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_sortType" caption="Type" keytip="T" className="SortTypeCmd"
                largeImage="SortCustom32"
                smallImage="SortCustom16">
          <tooltip heading="">Sort the contents list alphabetically by type. Items are initially sorted in ascending order. When sorted by type a second time, items are arranged in descending order.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_sortGeometry" caption="Geometry" keytip="G" className="SortGeometryCmd"
        largeImage="SortCustom32"
        smallImage="SortCustom16">
          <tooltip heading="">
            Sort the contents list alphabetically by geometry type. Items are initially sorted in ascending order. When sorted by type a second time, items are arranged in descending order.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_sortDate" caption="Date Modified" keytip="D" className="SortDateCmd"
                largeImage="SortCustom32"
                smallImage="SortCustom16">
          <tooltip heading="">Sort the contents list by date. Items are initially sorted in from newest to oldest. When sorted by date a second time, items are arranged from oldest to newest.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_sortDateCreated" caption="Date Created" keytip="C" className="SortDateCreatedCmd"
        largeImage="SortCustom32"
        smallImage="SortCustom16">
          <tooltip heading="">
            Sort the contents list by date created. Items are initially sorted in from newest to oldest. When sorted by date a second time, items are arranged from oldest to newest.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_sortName" caption="Name" keytip="N" className="SortNameCmd"
                largeImage="SortCustom32"
                smallImage="SortCustom16">
          <tooltip heading="">Sort the contents list alphabetically by name. Items are initially sorted in ascending order. When sorted by name a second time, items are arranged in descending order.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_sortAlias" caption="Alias" keytip="A" className="SortAliasCmd"
                largeImage="SortCustom32"
                smallImage="SortCustom16">
          <tooltip heading="">Sort the contents list alphabetically by alias. Items are initially sorted in ascending order. When sorted by alias a second time, items are arranged in descending order.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_sortPath" caption="Path" keytip="P" className="SortPathCmd"
        largeImage="SortCustom32"
        smallImage="SortCustom16">
          <tooltip heading="">
            Sort the contents list alphabetically by path. Items are initially sorted in ascending order. When sorted by name a second time, items are arranged in descending order.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_sortSize" caption="Size" keytip="S" className="SortSizeCmd"
        largeImage="SortCustom32"
        smallImage="SortCustom16">
          <tooltip heading="">
            Sort the contents list alphabetically by size. Items are initially sorted in ascending order. When sorted by name a second time, items are arranged in descending order.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_sortCategory" caption="Category" keytip="C" className="SortCategoryCmd"
                largeImage="SortCustom32"
                smallImage="SortCustom16">
          <tooltip heading="">Sort the contents list alphabetically by category. Items are initially sorted in ascending order. When sorted by category a second time, items are arranged in descending order.
            <disabledText></disabledText>
          </tooltip>
        </button>
        
        <button id="esri_core_sortKey" caption="Key" keytip="K" className="SortKeyCmd"
                largeImage="SortCustom32"
                smallImage="SortCustom16">
          <tooltip heading="">Sort the contents list using the internal key for style items. Items are initially sorted in ascending order. When sorted by key a second time, items are arranged in descending order.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_core_deviceLocation_openBackstageOptionsButton"
                className="ArcGIS.Desktop.Internal.Core.DeviceLocation.LocationDeviceOptionsButton"
                caption="Device Location Options" extendedCaption="Open location device options">
          <tooltip heading="Device Location Options">
            Set device location options.<disabledText></disabledText>
          </tooltip>
        </button>

        <dynamicMenu id="esri_core_sortColumns" className="SortDynamicMenu" caption="Sort" keytip="SO" loadOnClick="false"
                     largeImage="SortCustom32"
                     smallImage="SortCustom16">
          <tooltip heading="Sort">Sort the contents list using item properties.
            <disabledText></disabledText>
          </tooltip>
        </dynamicMenu>

        <dynamicMenu id="esri_core_addItemsDynamicMenu" className="AddItemsDynamicMenu" 
                     caption="Add" keytip="AI" 
                     loadOnClick="false" 
                     largeImage="GenericAddGreen32"
                     smallImage="GenericAddGreen16">
          <tooltip heading="Add">Add an existing item to the project.
            <disabledText></disabledText>
          </tooltip>
        </dynamicMenu>

        <dynamicMenu id="esri_core_addItemsDynamicMenuForCatalogDockPaneWhitespaceContextMenu" className="AddItemsDynamicMenuForCatalogDockPaneWhitespaceContextMenu"
                     caption="Add" keytip="AI"
                     loadOnClick="false"
                     largeImage="GenericAddGreen32"
                     smallImage="GenericAddGreen16">
          <tooltip heading="Add">
            Add an existing item to the project.
            <disabledText></disabledText>
          </tooltip>
        </dynamicMenu>

        <dynamicMenu id="esri_core_newItemsDynamicMenu" className="NewItemsDynamicMenu" 
                     caption="New" keytip="NI" 
                     loadOnClick="false" 
                     largeImage="GenericNewSparkleLarge32"
                     smallImage="GenericNewSparkleLarge16">
          <tooltip heading="New">Create a new project item.
            <disabledText></disabledText>
          </tooltip>
         
        </dynamicMenu>

        <dynamicMenu id="esri_core_newItemsDynamicMenuForCatalogDockPaneWhitespaceContextMenu" className="NewItemsDynamicMenuForCatalogDockPaneWhitespaceContextMenu"
                     caption="New" keytip="NI"
                     loadOnClick="false"
                     largeImage="GenericNewSparkleLarge32"
                     smallImage="GenericNewSparkleLarge16">
          <tooltip heading="New">
            Create a new project item.
            <disabledText></disabledText>
          </tooltip>

        </dynamicMenu>        

        <dynamicMenu id="esri_core_importItemsDynamicMenu" keytip="II" className="ImportItemsDynamicMenu"
                     caption="Import"
                     loadOnClick="false" autoDisableWhenPopupContentIsDisabled="false"
                     largeImage="GenericImport32"
                     smallImage="GenericImport16">
          <tooltip heading="Import">
            Import an item into the project. The original item's content will be converted to the ArcGIS Pro format.
            <disabledText></disabledText>
          </tooltip>
        </dynamicMenu>

        <dynamicMenu id="esri_core_importItemsDynamicMenuForCatalogDockPaneWhitespaceContextMenu" keytip="II" className="ImportItemsDynamicMenuForCatalogDockPaneWhitespaceContextMenu"
                     caption="Import"
                     loadOnClick="false" autoDisableWhenPopupContentIsDisabled="false"
                     largeImage="GenericImport32"
                     smallImage="GenericImport16">
          <tooltip heading="Import">
            Import an item into the project. The original item's content will be converted to the ArcGIS Pro format.
            <disabledText></disabledText>
          </tooltip>
        </dynamicMenu>

        <button id="esri_core_groupContext" className="esri_core_module:GroupAsync" caption="Group" extendedCaption="Group selected items based on context."/>
        <button id="esri_core_ungroupContext" className="esri_core_module:UngroupAsync" caption="Ungroup" extendedCaption="Ungroup selected items based on context."/>
        <button id="esri_core_exportContext" className="esri_core_module:Export" caption="Export" extendedCaption="Export based on context."/>
        <button id="esri_core_printContext" className="esri_core_module:Print" caption="Print" extendedCaption="Print based on context."/>
      </controls>

      <galleries>
      </galleries>
      
      <subgroups>
        <!--<subgroup id="esri_core_options" size="AlwaysMedium">
          <checkBox refID="esri_core_projectViewLayoutCheckBoxOpen" size="middle" />
          <menu refID="esri_core_projectViewDisplayType" size="large" />
        </subgroup>-->

        <subgroup id="esri_core_WindowControls" size="Default">
          <button refID="esri_core_showProjectDockPane"/>
          <tool refID="esri_core_showProjectView"/>
          <button refID="esri_core_showContentsDockPane"/>
        </subgroup>

        <subgroup id="esri_core_WindowControlsGP" size="Default">
          <button refID="esri_geoprocessing_toolsViewTabButton" />
          <button refID="esri_geoprocessing_pythonButton" />
          <button refID="esri_tasks_ShowTasks" />
        </subgroup>

        <subgroup id="esri_core_WindowControlsExtensions" size="Default">
          <button refID="esri_workflow_client_open_workflow_pane" />
          <button refID="esri_aviation_aviationViewPalette"/>
          <button refID="esri_indoors_indoorsViewPalette"/>
        </subgroup>
                  

        <subgroup id="esri_core_editBtns" size="MediumThenSmallWhenSmall">
          <button refID="esri_core_editCutButton"/>
          <button refID="esri_core_editCopyButton"/>
          <button refID="esri_core_editCopyPaths"/>
        </subgroup>

        <subgroup id="esri_core_organizeItems1" size="MediumThenSmallWhenSmall">
          <button refID="esri_core_Remove"/>
          <button refID="esri_core_rename" />
          <button refID="esri_core_refresh"/>
        </subgroup>

        <subgroup id="esri_core_organizeItems2" size="MediumThenSmallWhenSmall">
          <button refID="esri_core_editDeleteButton"/>
          <button refID="esri_core_repairProjectItem" />
          <menu refID="esri_core_sortColumns"/>
        </subgroup>

        <subgroup id="esri_core_organizeItems3"
                  size="MediumThenSmallWhenSmall"
                  verticalAlignment="Top">
          <button refID="esri_core_addToProject" separator="true"/>
          <button refID="esri_addFavoriteButton" />
          <button refID="esri_addProjectItemToNewProjectsButton" />
        </subgroup>

        <subgroup id="esri_core_newItems" size="MediumThenSmallWhenSmall">
          <button refID="esri_core_newFolderButton" />
          <button refID="esri_GDBCreateNewFileGeodatabaseButton"/>
          <button refID="esri_geodatabase_databaseConnectionButton"/>
        </subgroup>
      </subgroups>

      <menus>
        <menu id="esri_core_helpMenu" caption="Help" keytip="H" flipImageRTL="true" skipIfHebrew="true"
              smallImage="ArcGISProHelp16" largeImage="ArcGISProHelp32">
          <tooltip heading="Help">Open the help documentation on the web or on your computer and set language options. </tooltip>
          <button refID="esri_core_onlineHelpBtn"/>
          <button refID="esri_core_offlineHelpBtn"/>
          <button refID="esri_core_languageBtn"/>
        </menu>
        <menu id="esri_core_NewMaps" caption="New Map" keytip="NM"
              smallImage="MapNew16">
          <tooltip heading="New Map options"/>
          <button refID="esri_mapping_newMapButton"/>
          <button refID="esri_mapping_newSceneButton"/>
          <button refID="esri_mapping_newLocalSceneButton"/>
          <button refID="esri_mapping_newStereoMapButton"/>
          <button refID="esri_mapping_newBasemapButton"/>
        </menu>
        <menu id="esri_core_NewDatabases" caption="New Database" keytip="ND"
              smallImage="GeodatabaseNew16">
          <tooltip heading="New Database options"/>
          <button refID="esri_geodatabase_newGeoDatabaseButton"/>
          <button refID="esri_geodatabase_newMobileGeoDatabaseButton"/>
          <button refID="esri_geodatabase_databaseConnectionButtonOnContextMenu"/>
          <button refID="esri_geodatabase_newOLEDBConnectionButton"/>
          <button refID="esri_geodatabase_newGeoPackageButton"/>
        </menu>
        <menu id="esri_core_NewToolboxes" caption="New Toolbox" keytip="NT"
              smallImage="GeoprocessingToolboxNew16">
          <tooltip heading="New Toolbox options"/>
          <button refID="esri_geoprocessing_newToolboxButton_atbx"/>
          <!--<button refID="esri_geoprocessing_newToolboxButton"/>-->
          <button refID="esri_geoprocessing_newPythonToolboxButton"/>
        </menu>
        <menu id="esri_coreNewServer" caption="New Server" keytip="NS"
              smallImage="ServerArcGISNew16">
          <tooltip heading="New Server options"/>
          <button refID="esri_mapping_AGSConnectionButton"/>
          <button refID="esri_mapping_OGCAPIConnectionButton"/>
          <button refID="esri_mapping_WCSConnectionButton"/>
          <button refID="esri_mapping_WFSConnectionButton"/>
          <button refID="esri_mapping_WMSConnectionButton"/>
          <button refID="esri_mapping_WMTSConnectionButton"/>
        </menu>
        
        <menu id="esri_BrowseDialog_SingleItem" caption="Browse Dialog Single Rename Item">
          <button refID="esri_core_rename"/>
          <button refID="esri_core_editCopyPaths"/>          
        </menu>

        <menu id="esri_BrowseDialog_SingleItemWithAlias" caption="Browse Dialog Single Item Rename And Alias">
          <button refID="esri_core_rename"/>
          <button refID="AliasProjectItemMenu"/>
          <button refID="esri_core_editCopyPaths"/>
        </menu>

        <menu id="esri_BrowseDialog_SingleItemAliasOnly" caption="Browse Dialog Single Item Alias">
          <button refID="esri_core_rename_alias"/>
          <button refID="esri_core_remove_alias" />
        </menu>

        <menu id="esri_BrowseDialog_FolderGeneral" caption="Folder General Item" extendedCaption="Context menu for folders within a folder connection">
          <!--<menu refID="FolderConnectionProjectNewItemMenu2" separator="true" />-->
          <button refID="esri_core_newFolderButton"/>
          <button refID="esri_GDBCreateNewFileGeodatabaseButton" />
          <button refID="esri_GDBCreateNewMobileGeodatabaseButton" />
          <button refID="esri_geodatabase_databaseConnectionButton" />
          <button refID="esri_GDBCreateNewToolboxAtbxButtonInFolder" />
          <button refID="esri_itemInfoRefreshButton" separator="true" />
        </menu>
        
        <menu id="esri_projectDefaultContextMenu" caption="Project Menu">
          <tooltip>Show the project menu.</tooltip>
          <dynamicMenu refID="esri_core_newItemsDynamicMenu"/>
          <dynamicMenu refID="esri_core_addItemsDynamicMenu"/>
          <dynamicMenu refID="esri_core_importItemsDynamicMenu"/>
        </menu>

        <menu id="esri_projectDefaultContextMenuForCatalogDockPaneWhitespace" caption="-">
          <dynamicMenu refID="esri_core_newItemsDynamicMenuForCatalogDockPaneWhitespaceContextMenu"/>
          <dynamicMenu refID="esri_core_addItemsDynamicMenuForCatalogDockPaneWhitespaceContextMenu"/>
          <dynamicMenu refID="esri_core_importItemsDynamicMenuForCatalogDockPaneWhitespaceContextMenu"/>
        </menu>

        <menu id="esri_favoritesItemDefaultContextMenu" caption="-">
          <button refID="esri_favoriteItemAddToProjectButton"/>
          <dynamicMenu refID="esri_favoritesItemPersistDynamicMenu" inline="true"/>
          <button refID="esri_removeFavoriteButton" separator="true"/>
          <menu refID="AliasProjectItemMenu"/>
          <!--<dynamicMenu refID="esri_favoritesGeodatabasePropertyMenu" inline="true"/>-->
        </menu>
      </menus>

      <splitButtons>
        <splitButton id="esri_core_undoSplitButton">
          <button refID="esri_core_undoButton" />
          <customControl refID="esri_core_undoHistory" />
        </splitButton>
        <splitButton id="esri_core_redoSplitButton">
          <button refID="esri_core_redoButton" />
          <customControl refID="esri_core_redoHistory" />
        </splitButton>
        <splitButton id="esri_core_editPasteSplitButton" keytip="V" >
          <button refID="esri_core_editPasteButton"/>
          <button refID="esri_core_editPasteSpecialButton"/>
        </splitButton>

      </splitButtons>
      
    </insertModule>    
  </modules>
  
  <options>
    <insertStartPane refID="esri_core_startPage" />
  </options>  
  
  
  <propertySheets>
    <insertSheet id="esri_core_optionsPropertySheet" caption="Options" allowPagesWithErrors="true" resizable="true" pageHeight="565" pageWidth="792" hasGroups="true" hidden="false">
      <page id="esri_core_ProjectOptionsPropertyPage" caption="Current Settings" className="ProjectOptionsViewModel" group="Project">
        <content className="ProjectOptionsView" />
      </page>
      <page id="esri_core_ApplicationOptionsPropertyPage" caption="General" className="ApplicationOptionsViewModel" group="Application">
        <content className="ApplicationOptionsView" />
      </page>

      <page id="esri_core_CatalogBrowsingOptionsPropertyPage" caption="Catalog Browsing" className="CatalogBrowsingOptionsViewModel" group="Application">
        <content className="CatalogBrowsingOptionsView"/>
      </page>
      
      
<!--      <page id="esri_core_ApplicationNotificationsPropertyPage" caption="Notifications" assembly="ArcGIS.Desktop.Framework.dll"
            className="ArcGIS.Desktop.Framework.ApplicationNotificationsViewModel" group="Application" insert="after" placeWith="esri_core_ApplicationOptionsPropertyPage">
        <content assembly="ArcGIS.Desktop.Framework.dll" className="ArcGIS.Desktop.Framework.ApplicationNotificationsView"/>
      </page> -->

      <page id="esri_core_Proofing" caption="Proofing" className="ArcGIS.Desktop.Framework.DictionaryViewModel"
            assembly="ArcGIS.Desktop.Framework.dll" group="Application" insert="after">
        <content className="ArcGIS.Desktop.Framework.DictionaryControl" assembly="ArcGIS.Desktop.Framework.dll"/>
      </page>
      
      <page id="esri_core_LanguagePropertyPage" caption="Language" className="ArcGIS.Desktop.Internal.Core.Language.LanguageVM" group="Application"
            insert="after">
        <content className="ArcGIS.Desktop.Internal.Core.Language.Language"/>
      </page>
      <page id="esri_core_userInterfacePropertyPage" caption="User Interface" className="UserInterfaceViewModel" group="Application"
                  insert="after" placeWith="esri_core_LanguagePropertyPage">
        <content className="UserInterfaceView"/>
      </page>
      <page id="esri_mapping_customizePropertyPage" caption="Customize the Ribbon" className="ArcGIS.Desktop.Framework.CustomizeViewModel" 
            assembly="ArcGIS.Desktop.Framework.dll" group="Application" insert="after" condition="esri_core_noCustomize"/>
      <page id="esri_mapping_customizeQATPropertyPage" caption="Quick Access Toolbar" className="ArcGIS.Desktop.Framework.CustomizeQATViewModel" 
            assembly="ArcGIS.Desktop.Framework.dll" group="Application" insert="after" condition="esri_core_noCustomize"/>
      <page id="esri_sharing_SharingPropertyPage" caption="Share and Download" className="LocationOptionsViewModel" group="Application"
                  insert="before" placeWith="esri_metadata_MetadataOptionsPropertyPage">
        <content className="LocationOptionsView"/>
      </page>
      <page id="esri_core_locationSourcesPage" caption="Device Location" className="ArcGIS.Desktop.Internal.Core.DeviceLocation.LocationSourcesViewModel" group="Application"
                  insert="after" placeWith="esri_core_ApplicationOptionsPropertyPage" >
        <content className="ArcGIS.Desktop.Internal.Core.DeviceLocation.LocationSourcesView" />
      </page>
      <page id="esri_core_AuthenticationPropertyPage" caption="Authentication" className="OAuthConnectionsViewModel" group="Application"
                  insert="after" placeWith="esri_sharing_SharingPropertyPage">
        <content className="OAuthConnectionsView"/>
      </page>
      <page id="esri_core_SecurityPropertyPage" caption="Security" className="SecurityOptionsViewModel" group="Application"
            placeWith="esri_mapping_customizePropertyPage" insert="before">
        <content className="SecurityOptionsView"/>
      </page>
    </insertSheet>
  </propertySheets>

  <conditions>
    <insertCondition id="esri_core_search" caption="Search tab">
      <state id="esri_search_State" />
    </insertCondition>
   
    <insertCondition id="esri_browsePlaces_Online_Condition" caption="Browse Places Online">
      <state id="esri_browsePlaces_Online_State" />
    </insertCondition>
    
    <insertCondition id="esri_core_noPanesCondition" caption="No panes active">
      <state id="NoPanes" />
    </insertCondition>

    <insertCondition id="esri_core_projectItem_resourcesOnlyPane" caption="">
      <and>
        <state id="esri_core_resourcesPane" />
      </and>
      <not>
        <state id="esri_metadata_metadataPane" />
      </not>
    </insertCondition>
    <insertCondition id="esri_show_analysisTab">
      <or>
        <state id="esri_core_resourcesPane" />
        <state id="esri_metadata_metadataPane" />
        <state id="esri_mapping_mapPane"/>
        <state id="esri_editing_tablePane"/>
        <state id="esri_mapping_fieldsPane" />
        <state id="esri_mapping_domainsPane" />
        <state id="esri_mapping_subtypesPane" />
        <state id="esri_mapping_versionsPane" />
        <state id="esri_mapping_attributeRulesPane" />
        <state id="esri_mapping_contingentValuesPane" />
        <state id="esri_editing_versionConflictsPane" />
        <state id="esri_knowledgeGraph_knowledgeGraphView"/>
        <state id="esri_layouts_layoutPane" />
        <state id="esri_reports_reportPane" />
        <state id="esri_geoprocessing_modelBuilderPane"/>
        <state id="esri_geoprocessing_MBReportPane"/>
        <state id="esri_geoprocessing_proNotebookPane"/>
        <state id="esri_geoprocessing_dataEngineeringPaneView"/>
        <state id="esri_knowledgeGraph_knowledgeGraphView"/>
        <state id="esri_aviation_extractionQueriesPane"/>
        <state id="esri_workflow_jobView"/>
        <state id="esri_workflow_workflowPane"/>
        <state id="esri_workflow_client_openAppPane"/>
        <state id="esri_dataReviewer_reviewerRulesPane"/>
        <state id="NoPanes"/>
      </or>
    </insertCondition>
    <insertCondition id="esri_core_projectItem_resourcesPane">
      <or>
        <state id="esri_core_resourcesPane" />
        <state id="esri_metadata_metadataPane" />
      </or>
    </insertCondition>
    <insertCondition id="esri_core_showViewTab" >
      <or>
        <state id="esri_core_resourcesPane" />
        <state id="esri_metadata_metadataPane" />
        <state id="esri_mapping_mapPane"/>
        <state id="esri_editing_tablePane"/>
        <state id="esri_mapping_fieldsPane" />
        <state id="esri_mapping_domainsPane" />
        <state id="esri_mapping_subtypesPane" />
        <state id="esri_mapping_versionsPane" />
        <state id="esri_mapping_attributeRulesPane" />
        <state id="esri_mapping_contingentValuesPane" />
        <state id="esri_editing_versionConflictsPane" />
        <state id="esri_knowledgeGraph_knowledgeGraphView"/>
        <state id="esri_layouts_layoutPane" />
        <state id="esri_reports_reportPane" />
        <state id="esri_geoprocessing_modelBuilderPane"/>
        <state id="esri_geoprocessing_MBReportPane"/>
        <state id="esri_geoprocessing_proNotebookPane"/>
        <state id="esri_geoprocessing_dataEngineeringPaneView"/>
        <state id="esri_workflow_jobView"/>
        <state id="esri_workflow_workflowPane"/>
        <state id="esri_workflow_client_openAppPane"/>
        <state id="esri_aviation_extractionQueriesPane"/>
        <state id="esri_dataReviewer_reviewerRulesPane"/>
        <state id="NoPanes" />
      </or>
    </insertCondition>
    <insertCondition id="esri_core_showInsertTab" caption="">
      <or>
        <and>
          <not>
            <state id="esri_mapping_stereoMapState" />
          </not>
          <not>
            <state id="esri_mapping_mapTypeNetworkDiagramState" />
          </not>
          <or>
            <state id="esri_mapping_mapPane"/>
            <state id="esri_editing_tablePane"/>
            <state id="NoPanes" />
          </or>
        </and>
        <state id="esri_core_resourcesPane" />
        <state id="esri_metadata_metadataPane" />
        <state id="esri_knowledgeGraph_knowledgeGraphView"/>
        <state id="esri_layouts_layoutPane"/>
        <state id="esri_reports_reportPane"/>
        <state id="esri_geoprocessing_modelBuilderPane"/>
        <state id="esri_geoprocessing_MBReportPane"/>
        <state id="esri_geoprocessing_proNotebookPane"/>
        <state id="esri_geoprocessing_dataEngineeringPaneView"/>
        <state id="esri_workflow_jobView"/>
        <state id="esri_workflow_workflowPane"/>
        <state id="esri_workflow_client_openAppPane"/>
        <state id="esri_dataReviewer_reviewerRulesPane"/>
        <state id="esri_knowledgeGraph_knowledgeGraphView"/>
      </or>
    </insertCondition>
    <insertCondition id="esri_core_search_orgOnlyCondition" caption="">
      <and>        
        <state id="esri_core_search_orgOnly" />
      </and>
    </insertCondition>
    <insertCondition id="esri_core_noCustomize">
      <not>
        <state id="esri_core_blockCustomizeDialog" />
      </not>
    </insertCondition>
    
    <!--Preview Item-->
    <insertCondition id="esri_core_previewActiveCondition">
      <and>
        <state id="esri_core_resourcesPane"/>
        <state id="esri_core_projectViewHasSelectedItems"/>
        <state id="esri_core_previewSelectedState"/>
      </and>
    </insertCondition>
    
    <insertCondition id="esri_core_previewShowBasemapCondition">
      <and>
        <state id="esri_core_previewCanEditBasemapState"/>
      </and>
    </insertCondition>

  <insertCondition id="esri_core_portalProjectCondition" caption="">
      <state id="esri_core_currentProjectIsPortalProject_state" />
    </insertCondition>

    <insertCondition id="esri_core_not_portalProjectCondition" caption="">
      <not>
        <state id="esri_core_currentProjectIsPortalProject_state" />
      </not>
    </insertCondition>
  </conditions>

  <backstage>

    <insertTab id="esri_core_newProjectBackStageTab" caption="New" extendedCaption="New project page; on Project tab" className="ProjectNewBackStageViewModel" keytip="N">
      <content className="ProjectNewBackStage" />
    </insertTab>

    <insertTab id="esri_core_openProjectBackStageTab" caption="Open" extendedCaption="Open project page; on Project tab" className="ProjectOpenBackStageViewModel" keytip="O">
      <content className="ProjectOpenBackStage" />
    </insertTab>

    <insertTab id="esri_core_infoTab" caption="Info" extendedCaption="Project information; on Project tab" className="InfoBackstageViewModel" condition="esri_mapping_openProjectCondition"  keytip="I">
      <content className="InfoBackstageView" />
    </insertTab>

    <insertButton refID="esri_core_saveProjectButton" />
    <insertButton refID="esri_core_saveProjectAsButton" />


    <!--
      TODO: remove this view and view model!!!!
    <insertTab id="esri_core_recentProjectBackStageTab" caption="Recent" className="RecentProjectsViewModel">
      <content className="RecentProjectsView" />
    </insertTab>
    -->
    <insertTab id="esri_core_PortalTab" caption="Portals" extendedCaption="Portals page; on Project tab" className="PortalBackstageViewModel" separator="true" keytip="P">
      <content className="PortalBackstage" />
    </insertTab>
    
    <insertTab id="esri_core_LicensingTab" caption="Licensing" extendedCaption="Licensing page; on Project tab" className="LicensingBackstageViewModel" separator="false" keytip="L">
      <content className="ArcGIS.Desktop.Internal.Core.LicensingBackstage" />
    </insertTab>

    <!-- TODO: make the options a TAB -->
    <insertButton refID="esri_core_showOptionsSheetButton" />

    <insertTab id="esri_core_CondaTab" caption="Package Manager" extendedCaption="Package Manager page; on Project tab" className="ArcGIS.Desktop.Internal.Core.Conda.Backstage.CondaBackstageViewModel" separator="false" keytip="Y">
      <content className="ArcGIS.Desktop.Internal.Core.Conda.Backstage.CondaBackstage"/>
    </insertTab>

    <insertTab id="esri_core_AddInsTab" caption="Add-In Manager" extendedCaption="Add-In Manager page; on Project tab" className="ArcGIS.Desktop.Framework.AddIns.AddInManagerDlgViewModel" assembly="ArcGIS.Desktop.Framework.dll" keytip="D">
      <content className="ArcGIS.Desktop.Framework.AddIns.AddInManagerDlg" assembly="ArcGIS.Desktop.Framework.dll"/>
    </insertTab>

    <insertButton refID="esri_core_showHelpButton" separator="true" />

    <insertTab id="esri_core_aboutTab" caption="About" extendedCaption="About page; on Project tab" className="ArcGIS.Desktop.Framework.AboutViewModel" assembly="ArcGIS.Desktop.Framework.dll" keytip="B">
      <content className="ArcGIS.Desktop.Framework.AboutView" assembly="ArcGIS.Desktop.Framework.dll"/>
    </insertTab>

    <insertTab id="esri_core_learningResourcesTab" caption="Learning Resources" extendedCaption="Learning resources page; on Project tab" className="LearningResourcesBackstageViewModel"  keytip="R">
      <content className="LearningResourcesBackstageView" />
    </insertTab>

    <insertButton refID="esri_core_exitApplicationButton" separator="true"/>
  </backstage>

  <quickAccessToolbar>
    <control refID="esri_core_newProjectButton" size="small"/>
    <control refID="esri_core_openProjectButton" size="small"/>
    <control refID="esri_core_saveProjectButton" size="small"/>
    <control refID="esri_core_undoSplitButton" size="small"/>
    <control refID="esri_core_redoSplitButton" size="small"/>
  </quickAccessToolbar>

  <wizards>
    <insertWizard id="esri_core_newPortalProjectWizard" pageHeight="666" minHeight="440" pageWidth="613" minWidth="613" isPageListVisible="false" resizable="true" showTitleBar="false" saveWindowPosition="true" leftMargin="0" rightMargin="0" navigationButtonsLeftMargin="10" navigationButtonsRightMargin="10" caption="New Portal Project" >
      <page id="esri_catalog_newPortalProjectIntroductionPage" className="ArcGIS.Desktop.Core.IntroductionPageViewModel">
        <content className="ArcGIS.Desktop.Core.IntroductionPageView" />
      </page>
      <page id="esri_catalog_newPortalProjectCollaborationChoicePage" className="ArcGIS.Desktop.Core.CollaborationChoicePageViewModel">
        <content className="ArcGIS.Desktop.Core.CollaborationChoicePageView" />
      </page>
      <page id="esri_catalog_newPortalProjectDetailsPage" className="ArcGIS.Desktop.Core.DetailsPageViewModel">
        <content className="ArcGIS.Desktop.Core.DetailsPageView" />
      </page>
      <page id="esri_catalog_newPortalProjectShareWithPage" className="ArcGIS.Desktop.Core.ShareWithPageViewModel">
        <content className="ArcGIS.Desktop.Core.ShareWithPageView" />
      </page>
      <page id="esri_catalog_newPortalProjectDefaultHomePage" className="ArcGIS.Desktop.Core.DefaultHomePageViewModel">
        <content className="ArcGIS.Desktop.Core.DefaultHomePageView" />
      </page>
      <page id="esri_catalog_newPortalProjectDefaultGeodatabasePage" className="ArcGIS.Desktop.Core.DefaultGeodatabasePageViewModel">
        <content className="ArcGIS.Desktop.Core.DefaultGeodatabasePageView" />
      </page>
      <page id="esri_catalog_newPortalProjectDefaultToolboxPage" className="ArcGIS.Desktop.Core.DefaultToolboxPageViewModel">
        <content className="ArcGIS.Desktop.Core.DefaultToolboxPageView" />
      </page>
      <page id="esri_catalog_newPortalProjectMetadataPage" className="ArcGIS.Desktop.Core.MetadataPageViewModel">
        <content className="ArcGIS.Desktop.Core.MetadataPageView" />
      </page>
      <page id="esri_catalog_newPortalProjectSummaryPage" className="ArcGIS.Desktop.Core.SummaryPageViewModel">
        <content className="ArcGIS.Desktop.Core.SummaryPageView" />
      </page>
    </insertWizard>
  </wizards>

</ArcGIS>
