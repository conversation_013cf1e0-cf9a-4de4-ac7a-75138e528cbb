<UserControl x:Class="XIAOFUTools.Tools.ViewArea.ViewAreaDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.ViewArea"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="400"
             d:DataContext="{Binding Path=ViewAreaDockPaneViewModel}"
             Loaded="UserControl_Loaded">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- 加载动画样式 -->
            <Style x:Key="LoadingSpinnerStyle" TargetType="Ellipse">
                <Setter Property="Stroke" Value="#FF0078D4"/>
                <Setter Property="StrokeThickness" Value="3"/>
                <Setter Property="StrokeDashArray" Value="12,4"/>
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <RotateTransform/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsVisible" Value="True">
                        <Trigger.EnterActions>
                            <BeginStoryboard>
                                <Storyboard RepeatBehavior="Forever">
                                    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                                                   From="0" To="360" Duration="0:0:1.2"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.EnterActions>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 选择状态信息 -->
        <TextBlock Grid.Row="0"
                   Text="{Binding SelectionInfo}"
                   FontWeight="Bold"
                   FontSize="11"
                   Margin="0,0,0,5"
                   TextWrapping="Wrap"/>

        <!-- 设置区域 -->
        <Grid Grid.Row="1" Margin="0,0,0,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="50"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0"
                       Text="小数位数:"
                       VerticalAlignment="Center"
                       FontSize="10"
                       Margin="0,0,5,0"/>

            <TextBox Grid.Column="1"
                     Text="{Binding DecimalPlaces, UpdateSourceTrigger=PropertyChanged}"
                     VerticalAlignment="Center"
                     FontSize="10"
                     Padding="2"
                     Margin="0,0,5,0"/>

            <Button Grid.Column="2"
                    Content="刷新"
                    Command="{Binding RefreshCommand}"
                    Style="{StaticResource PrimaryButtonStyle}"
                    FontSize="10"
                    Width="40"
                    Height="20"
                    Margin="0,0,5,0"/>

            <Button Grid.Column="4"
                    Content="复制结果"
                    Command="{Binding CopyResultsCommand}"
                    Style="{StaticResource TextButtonStyle}"
                    FontSize="10"
                    Width="60"
                    Height="20"/>
        </Grid>

        <!-- 分隔线 -->
        <Separator Grid.Row="2" Margin="0,0,0,5"/>

        <!-- 结果显示区域 -->
        <Grid Grid.Row="3">
            <!-- 主要内容 -->
            <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!-- 有结果时显示的内容 -->
                <StackPanel Visibility="{Binding HasResults, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <!-- 合并计算结果标题 -->
                    <TextBlock Text="合并计算结果"
                               FontWeight="Bold"
                               FontSize="12"
                               Margin="0,0,0,5"/>

                <!-- 合并面积结果 -->
                <StackPanel Visibility="{Binding HasAreaResults, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="面积"
                               FontWeight="Bold"
                               FontSize="11"
                               Margin="0,0,0,3"/>

                    <DataGrid ItemsSource="{Binding CombinedAreaResults}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              HeadersVisibility="Column"
                              GridLinesVisibility="All"
                              RowHeight="22"
                              FontSize="10"
                              Margin="0,0,0,8"
                              MouseDoubleClick="DataGrid_MouseDoubleClick">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="类型" Binding="{Binding CalculationType}" Width="60">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="平方米" Binding="{Binding Unit1Value}" Width="65">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="公顷" Binding="{Binding Unit2Value}" Width="55">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="亩" Binding="{Binding Unit3Value}" Width="55">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="平方公里" Binding="{Binding Unit4Value}" Width="65">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>

                <!-- 合并长度结果 -->
                <StackPanel Visibility="{Binding HasLengthResults, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="长度"
                               FontWeight="Bold"
                               FontSize="11"
                               Margin="0,0,0,3"/>

                    <DataGrid ItemsSource="{Binding CombinedLengthResults}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              HeadersVisibility="Column"
                              GridLinesVisibility="All"
                              RowHeight="22"
                              FontSize="10"
                              Margin="0,0,0,8"
                              MouseDoubleClick="DataGrid_MouseDoubleClick">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="类型" Binding="{Binding CalculationType}" Width="60">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="米" Binding="{Binding Unit1Value}" Width="80">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="千米" Binding="{Binding Unit2Value}" Width="80">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>

                <!-- 分图层结果 -->
                <StackPanel Visibility="{Binding ShowLayerResults, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <!-- 分隔线 -->
                    <Separator Margin="0,5,0,5"/>

                    <TextBlock Text="分图层计算结果"
                               FontWeight="Bold"
                               Margin="0,0,0,5"
                               FontSize="12"/>

                    <ItemsControl ItemsSource="{Binding LayerResults}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border BorderBrush="LightGray"
                                        BorderThickness="0.5"
                                        Margin="0,0,0,8"
                                        Padding="5">
                                    <StackPanel>
                                        <TextBlock FontWeight="Bold"
                                                   FontSize="11"
                                                   Margin="0,0,0,3">
                                            <Run Text="{Binding LayerName}"/>
                                            <Run Text=" ("/>
                                            <Run Text="{Binding FeatureCount}"/>
                                            <Run Text=" 个要素)" Foreground="Gray"/>
                                        </TextBlock>

                                        <!-- 图层面积结果 -->
                                        <StackPanel Visibility="{Binding HasAreaResults, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <DataGrid ItemsSource="{Binding AreaResults}"
                                                      AutoGenerateColumns="False"
                                                      IsReadOnly="True"
                                                      HeadersVisibility="Column"
                                                      GridLinesVisibility="All"
                                                      RowHeight="20"
                                                      FontSize="9"
                                                      Margin="0,0,0,5"
                                                      MouseDoubleClick="DataGrid_MouseDoubleClick">
                                                <DataGrid.Columns>
                                                    <DataGridTextColumn Header="类型" Binding="{Binding CalculationType}" Width="50">
                                                        <DataGridTextColumn.ElementStyle>
                                                            <Style TargetType="TextBlock">
                                                                <Setter Property="TextAlignment" Value="Center"/>
                                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                            </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                    </DataGridTextColumn>
                                                    <DataGridTextColumn Header="平方米" Binding="{Binding Unit1Value}" Width="55">
                                                        <DataGridTextColumn.ElementStyle>
                                                            <Style TargetType="TextBlock">
                                                                <Setter Property="TextAlignment" Value="Center"/>
                                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                            </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                    </DataGridTextColumn>
                                                    <DataGridTextColumn Header="公顷" Binding="{Binding Unit2Value}" Width="45">
                                                        <DataGridTextColumn.ElementStyle>
                                                            <Style TargetType="TextBlock">
                                                                <Setter Property="TextAlignment" Value="Center"/>
                                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                            </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                    </DataGridTextColumn>
                                                    <DataGridTextColumn Header="亩" Binding="{Binding Unit3Value}" Width="45">
                                                        <DataGridTextColumn.ElementStyle>
                                                            <Style TargetType="TextBlock">
                                                                <Setter Property="TextAlignment" Value="Center"/>
                                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                            </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                    </DataGridTextColumn>
                                                    <DataGridTextColumn Header="平方公里" Binding="{Binding Unit4Value}" Width="55">
                                                        <DataGridTextColumn.ElementStyle>
                                                            <Style TargetType="TextBlock">
                                                                <Setter Property="TextAlignment" Value="Center"/>
                                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                            </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                    </DataGridTextColumn>
                                                </DataGrid.Columns>
                                            </DataGrid>
                                        </StackPanel>

                                        <!-- 图层长度结果 -->
                                        <StackPanel Visibility="{Binding HasLengthResults, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <DataGrid ItemsSource="{Binding LengthResults}"
                                                      AutoGenerateColumns="False"
                                                      IsReadOnly="True"
                                                      HeadersVisibility="Column"
                                                      GridLinesVisibility="All"
                                                      RowHeight="20"
                                                      FontSize="9"
                                                      Margin="0,0,0,5"
                                                      MouseDoubleClick="DataGrid_MouseDoubleClick">
                                                <DataGrid.Columns>
                                                    <DataGridTextColumn Header="类型" Binding="{Binding CalculationType}" Width="50">
                                                        <DataGridTextColumn.ElementStyle>
                                                            <Style TargetType="TextBlock">
                                                                <Setter Property="TextAlignment" Value="Center"/>
                                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                            </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                    </DataGridTextColumn>
                                                    <DataGridTextColumn Header="米" Binding="{Binding Unit1Value}" Width="70">
                                                        <DataGridTextColumn.ElementStyle>
                                                            <Style TargetType="TextBlock">
                                                                <Setter Property="TextAlignment" Value="Center"/>
                                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                            </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                    </DataGridTextColumn>
                                                    <DataGridTextColumn Header="千米" Binding="{Binding Unit2Value}" Width="70">
                                                        <DataGridTextColumn.ElementStyle>
                                                            <Style TargetType="TextBlock">
                                                                <Setter Property="TextAlignment" Value="Center"/>
                                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                            </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                    </DataGridTextColumn>
                                                </DataGrid.Columns>
                                            </DataGrid>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>

                </StackPanel>

                <!-- 无选择时的提示 -->
                <TextBlock Text="请在地图中选择面要素或线要素来查看面积/长度信息"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Margin="20"
                           Foreground="Gray"
                           FontStyle="Italic">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding HasResults}" Value="False">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>

                <!-- 椭球计算说明 -->
                <Border Background="#F5F5F5"
                        BorderBrush="LightGray"
                        BorderThickness="0.5"
                        Margin="0,5,0,0"
                        Padding="5"
                        Visibility="{Binding HasResults, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="椭球面积和测地线长度使用ArcGIS Pro内置高精度算法计算"
                               FontSize="9"
                               Foreground="Gray"
                               TextWrapping="Wrap"/>
                </Border>

            </StackPanel>
            </ScrollViewer>

            <!-- 计算中的转圈圈加载动画（窗口中间） -->
            <Grid Visibility="{Binding IsCalculating, Converter={StaticResource BooleanToVisibilityConverter}}"
                  Background="#80FFFFFF">
                <StackPanel HorizontalAlignment="Center"
                           VerticalAlignment="Center">
                    <!-- 转圈圈动画 -->
                    <Grid Width="50" Height="50" Margin="0,0,0,15">
                        <!-- 背景圆圈 -->
                        <Ellipse Width="50" Height="50"
                                Stroke="#FF0078D4"
                                StrokeThickness="2"
                                Opacity="0.2"/>
                        <!-- 旋转的加载圆圈 -->
                        <Ellipse Width="50" Height="50"
                                Style="{StaticResource LoadingSpinnerStyle}"/>
                    </Grid>

                    <!-- 计算状态文字 -->
                    <TextBlock Text="正在计算面积和长度..."
                              FontSize="12"
                              Foreground="#FF0078D4"
                              FontWeight="Medium"
                              HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Grid>

        <!-- 底部进度条 -->
        <Border Grid.Row="4"
                Height="28"
                Background="#FFF0F0F0"
                BorderBrush="#FFCCCCCC"
                BorderThickness="0,1,0,0"
                Visibility="{Binding IsCalculating, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid Margin="8,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 进度文字 -->
                <TextBlock Grid.Column="0"
                          Text="正在计算面积和长度..."
                          FontSize="10"
                          Foreground="#FF555555"
                          VerticalAlignment="Center"
                          FontWeight="Medium"/>

                <!-- 进度条 -->
                <ProgressBar Grid.Column="1"
                            IsIndeterminate="True"
                            Height="8"
                            VerticalAlignment="Center"
                            Margin="10,0,10,0"
                            Background="#FFDDDDDD"
                            Foreground="#FF0078D4"
                            BorderThickness="0"/>

                <!-- 进度条装饰 -->
                <Rectangle Grid.Column="1"
                          Height="8"
                          VerticalAlignment="Center"
                          Margin="10,0,10,0"
                          Fill="Transparent"
                          Stroke="#FFAAAAAA"
                          StrokeThickness="0.5"
                          RadiusX="4"
                          RadiusY="4"/>

                <!-- 状态图标 -->
                <Ellipse Grid.Column="2"
                        Width="12" Height="12"
                        Fill="#FF0078D4"
                        VerticalAlignment="Center">
                    <Ellipse.RenderTransform>
                        <ScaleTransform x:Name="PulseTransform" ScaleX="1" ScaleY="1"/>
                    </Ellipse.RenderTransform>
                    <Ellipse.Triggers>
                        <EventTrigger RoutedEvent="Loaded">
                            <BeginStoryboard>
                                <Storyboard RepeatBehavior="Forever">
                                    <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PulseTransform"
                                                                  Storyboard.TargetProperty="ScaleX">
                                        <LinearDoubleKeyFrame KeyTime="0:0:0" Value="1"/>
                                        <LinearDoubleKeyFrame KeyTime="0:0:0.75" Value="1.3"/>
                                        <LinearDoubleKeyFrame KeyTime="0:0:1.5" Value="1"/>
                                    </DoubleAnimationUsingKeyFrames>
                                    <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PulseTransform"
                                                                  Storyboard.TargetProperty="ScaleY">
                                        <LinearDoubleKeyFrame KeyTime="0:0:0" Value="1"/>
                                        <LinearDoubleKeyFrame KeyTime="0:0:0.75" Value="1.3"/>
                                        <LinearDoubleKeyFrame KeyTime="0:0:1.5" Value="1"/>
                                    </DoubleAnimationUsingKeyFrames>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Ellipse.Triggers>
                </Ellipse>
            </Grid>
        </Border>
    </Grid>
</UserControl>
