/* AI助手界面样式 - 基于参考WebUI */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Microsoft YaHei UI', sans-serif;
    font-size: 12px;
    line-height: 1.4;
    color: #242424;
    background: #f8f8f8;
    overflow: hidden;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #ffffff;
}

/* 顶部控制栏 */
.top-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #ffffff;
    border-bottom: 1px solid #e5e5e5;
    position: relative;
    z-index: 100;
}

.control-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 6px;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;
    transition: all 0.15s ease;
}

.control-btn:hover {
    background: #f0f0f0;
    color: #333;
}

/* 状态信息 */
.status-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    justify-content: center;
}

.status-left {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-indicator {
    font-size: 8px;
}

.status-text {
    font-size: 11px;
    color: #666;
}

.status-right .model-info {
    font-size: 11px;
    color: #999;
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 3px;
}

/* 菜单下拉 */
.menu-dropdown {
    position: relative;
}

.dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    min-width: 120px;
    z-index: 1000;
    display: none;
}

.dropdown-content.show {
    display: block;
}

.dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 12px;
    color: #333;
    transition: background 0.15s ease;
}

.dropdown-item:hover {
    background: #f5f5f5;
}

/* 历史记录栏 */
.history-bar {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #f8f8f8;
    border-bottom: 1px solid #e5e5e5;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
}

.history-bar.show {
    display: block;
}

.history-content {
    padding: 8px 0;
}

.history-item {
    padding: 8px 16px;
    cursor: pointer;
    font-size: 12px;
    color: #666;
    transition: all 0.15s ease;
    border-left: 3px solid transparent;
}

.history-item:hover {
    background: #f0f0f0;
    color: #333;
}

.history-item.active {
    background: #e8f4fd;
    color: #1890ff;
    border-left-color: #1890ff;
}

/* 聊天容器 */
#chat-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 消息区域 */
.messages-area {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    background: #ffffff;
    scroll-behavior: smooth;
}

/* 工具块容器 - Augment/Cursor风格 */
.tool-blocks-container {
    position: relative;
    background: #ffffff;
    border-top: 1px solid #e5e5e5;
    max-height: 300px;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.tool-blocks-container:empty {
    display: none;
}

/* 单个工具块 */
.tool-block {
    margin: 8px 12px;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    background: #fafbfc;
    overflow: hidden;
    transition: all 0.2s ease;
    animation: toolBlockSlideIn 0.3s ease-out;
}

.tool-block:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.tool-block.executing {
    border-color: #faad14;
    background: #fffbe6;
}

.tool-block.completed {
    border-color: #52c41a;
    background: #f6ffed;
}

.tool-block.failed {
    border-color: #ff4d4f;
    background: #fff2f0;
}

/* 工具块头部 */
.tool-block-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
}

.tool-block-header:hover {
    background: #fafafa;
}

.tool-block-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    font-size: 13px;
    color: #262626;
}

.tool-block-icon {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    background: #f0f0f0;
    color: #666;
}

.tool-block.executing .tool-block-icon {
    background: #faad14;
    color: white;
    animation: pulse 1.5s infinite;
}

.tool-block.completed .tool-block-icon {
    background: #52c41a;
    color: white;
}

.tool-block.failed .tool-block-icon {
    background: #ff4d4f;
    color: white;
}

.tool-block-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: #8c8c8c;
}

.tool-block-status .status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #d9d9d9;
}

.tool-block.executing .status-dot {
    background: #faad14;
    animation: pulse 1.5s infinite;
}

.tool-block.completed .status-dot {
    background: #52c41a;
}

.tool-block.failed .status-dot {
    background: #ff4d4f;
}

/* 工具块内容 */
.tool-block-content {
    padding: 12px 16px;
    font-size: 12px;
    line-height: 1.5;
    color: #595959;
    display: none;
}

.tool-block.expanded .tool-block-content {
    display: block;
}

.tool-block-params {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    margin: 8px 0;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
    white-space: pre-wrap;
    word-break: break-all;
}

.tool-block-result {
    margin-top: 8px;
    padding: 8px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
    white-space: pre-wrap;
    word-break: break-all;
}

.tool-block.completed .tool-block-result {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #389e0d;
}

.tool-block.failed .tool-block-result {
    background: #fff2f0;
    border: 1px solid #ffccc7;
    color: #cf1322;
}

/* 工具块展开/收起按钮 */
.tool-block-toggle {
    transition: transform 0.2s ease;
}

.tool-block.expanded .tool-block-toggle {
    transform: rotate(180deg);
}

/* 动画效果 */
@keyframes toolBlockSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 消息样式 */
.message {
    margin-bottom: 12px;
    display: flex;
    flex-direction: column;
}

.user-message {
    align-items: flex-end;
}

.assistant-message {
    align-items: flex-start;
}

.message-content {
    max-width: 90%;
    padding: 8px 12px;
    border-radius: 8px;
    position: relative;
}

.user-message .message-content {
    background-color: #007acc;
    color: white;
    border-bottom-right-radius: 3px;
}

.assistant-message .message-content {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-bottom-left-radius: 3px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message-text {
    line-height: 1.4;
    font-size: 13px;
}

.message-text h1, .message-text h2, .message-text h3, .message-text h4 {
    margin-bottom: 6px;
    color: #2c3e50;
    font-size: 14px;
}

.message-text p {
    margin-bottom: 6px;
}

.message-text ul, .message-text ol {
    margin-left: 16px;
    margin-bottom: 6px;
}

.message-text li {
    margin-bottom: 2px;
    font-size: 12px;
}

.message-text code {
    background-color: #f4f4f4;
    padding: 1px 3px;
    border-radius: 2px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
}

.message-text pre {
    background-color: #f8f8f8;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
    overflow-x: auto;
    margin: 6px 0;
    font-size: 11px;
}

.message-text pre code {
    background: none;
    padding: 0;
}

/* 输入区域 */
.input-area {
    position: relative;
    background: #ffffff;
    border-top: 1px solid #e5e5e5;
}

/* 工具调用指示器 */
.tool-indicator {
    display: none;
    background: #f8f9fa;
    border-bottom: 1px solid #e5e5e5;
    padding: 8px 16px;
}

.tool-indicator.show {
    display: block;
}

.tool-indicator-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #666;
}

.loading-spinner {
    width: 12px;
    height: 12px;
    border: 2px solid #e5e5e5;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 可拉伸分隔线 */
.resize-handle {
    height: 4px;
    background: transparent;
    cursor: ns-resize;
    position: relative;
}

.resize-handle:hover .resize-line {
    background: #1890ff;
}

.resize-line {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 2px;
    background: #e5e5e5;
    border-radius: 1px;
    transition: background 0.15s ease;
}

/* 输入容器 */
.input-container {
    padding: 12px 16px;
}

.text-input-wrapper {
    position: relative;
    background: #ffffff;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    transition: all 0.15s ease;
}

.text-input-wrapper:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

#messageInput {
    width: 100%;
    min-height: 36px;
    max-height: 120px;
    padding: 8px 12px 36px 12px;
    border: none;
    outline: none;
    resize: none;
    font-size: 13px;
    line-height: 1.4;
    font-family: inherit;
    background: transparent;
}

#messageInput::placeholder {
    color: #bfbfbf;
}

/* 输入框内工具栏 */
.input-inline-toolbar {
    position: absolute;
    bottom: 6px;
    left: 8px;
    right: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.inline-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.inline-right {
    display: flex;
    align-items: center;
    gap: 4px;
}

.inline-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;
    transition: all 0.15s ease;
}

.inline-btn:hover {
    background: #f5f5f5;
    color: #333;
}

/* 模式切换开关 */
.mode-switch-compact {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
}

.mode-label {
    color: #bfbfbf;
    transition: color 0.15s ease;
}

.mode-label.active {
    color: #1890ff;
    font-weight: 500;
}

.switch-container {
    position: relative;
}

.switch-input {
    display: none;
}

.switch-label {
    display: block;
    width: 32px;
    height: 16px;
    background: #f0f0f0;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.15s ease;
}

.switch-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    transition: transform 0.15s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.switch-input:checked + .switch-label {
    background: #1890ff;
}

.switch-input:checked + .switch-label .switch-slider {
    transform: translateX(16px);
}

/* 发送/停止按钮 */
.inline-action-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease;
}

.send-btn {
    background: #1890ff;
    color: white;
}

.send-btn:hover {
    background: #40a9ff;
}

.send-btn:disabled {
    background: #f5f5f5;
    color: #bfbfbf;
    cursor: not-allowed;
}

.stop-btn {
    background: #ff4d4f;
    color: white;
}

.stop-btn:hover {
    background: #ff7875;
}

.send-btn {
    background-color: #007acc;
    color: white;
}

.send-btn:hover {
    background-color: #005a9e;
}

.send-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* 加载指示器 */
#loading-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background-color: #f9f9f9;
    border-top: 1px solid #e0e0e0;
}

.loading-dots {
    display: flex;
    gap: 4px;
}

.loading-dots span {
    width: 6px;
    height: 6px;
    background-color: #007acc;
    border-radius: 50%;
    animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.loading-text {
    font-size: 14px;
    color: #666;
}

/* 工具类 */
.hidden {
    display: none !important;
}

/* 滚动条样式 */
#messages-area::-webkit-scrollbar {
    width: 6px;
}

#messages-area::-webkit-scrollbar-track {
    background: #f1f1f1;
}

#messages-area::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#messages-area::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 320px;
    height: 100vh;
    background-color: white;
    border-left: 1px solid #e0e0e0;
    box-shadow: -2px 0 8px rgba(0,0,0,0.1);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.sidebar:not(.hidden) {
    transform: translateX(0);
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.sidebar-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.close-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background-color: transparent;
    color: #666;
    cursor: pointer;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.close-btn:hover {
    background-color: #e9ecef;
    color: #333;
}

.sidebar-content {
    padding: 16px;
    height: calc(100vh - 64px);
    overflow-y: auto;
}

/* 历史记录样式 */
#history-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.history-item {
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.history-item:hover {
    background-color: #f8f9fa;
    border-color: #007acc;
}

.history-item-title {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.history-item-time {
    font-size: 12px;
    color: #666;
}

/* 设置面板样式 */
.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.setting-group select,
.setting-group input[type="number"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d0d0d0;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.setting-group select:focus,
.setting-group input[type="number"]:focus {
    border-color: #007acc;
}

.setting-group input[type="range"] {
    width: calc(100% - 40px);
    margin-right: 8px;
}

#temperature-value {
    font-weight: 500;
    color: #007acc;
    min-width: 32px;
    display: inline-block;
}

/* 响应式设计 */
@media (max-width: 480px) {
    #messages-area {
        padding: 12px;
    }

    #input-container {
        padding: 12px;
    }

    .message-content {
        max-width: 95%;
    }

    .sidebar {
        width: 100%;
    }

    #toolbar {
        padding: 6px 8px;
    }

    .toolbar-btn span {
        display: none;
    }
}
