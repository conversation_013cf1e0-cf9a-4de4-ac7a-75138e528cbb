<UserControl x:Class="XIAOFUTools.Tools.RangeClipTool.RangeClipToolDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.RangeClipTool"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="400"
             d:DataContext="{Binding Path=RangeClipToolViewModel}"
             Loaded="UserControl_Loaded">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Margin="12">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="120"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 范围图层选择 -->
        <TextBlock Grid.Row="0" Grid.Column="0" Text="范围图层:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <Grid Grid.Row="0" Grid.Column="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <ComboBox Grid.Column="0"
                    Style="{StaticResource ComboBoxStyle}"
                    ItemsSource="{Binding RangeLayers}"
                    SelectedItem="{Binding SelectedRangeLayer}"
                    DisplayMemberPath="Name"/>
            <Button Grid.Column="1" Width="22" Height="22" Margin="5,0,0,0"
                    Style="{StaticResource DefaultButtonStyle}"
                    Command="{Binding RefreshLayersCommand}"
                    ToolTip="刷新图层列表"
                    VerticalAlignment="Center">
                <TextBlock Text="⟲" FontSize="14" FontWeight="Bold"
                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Button>
        </Grid>

        <!-- 范围字段选择 -->
        <TextBlock Grid.Row="1" Grid.Column="0" Text="范围字段:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <ComboBox Grid.Row="1" Grid.Column="1" Margin="0,0,0,10"
                Style="{StaticResource ComboBoxStyle}"
                ItemsSource="{Binding RangeFieldNames}"
                SelectedItem="{Binding SelectedRangeField}"
                IsEnabled="{Binding HasSelectedRangeLayer}"/>

        <!-- 需裁剪要素图层 -->
        <TextBlock Grid.Row="2" Grid.Column="0" Text="需裁剪要素图层:" VerticalAlignment="Top" Margin="0,5,10,10"/>
        <Grid Grid.Row="2" Grid.Column="1" Margin="0,0,0,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- 全选/反选按钮 -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,5">
                <Button Content="全选" Style="{StaticResource DefaultButtonStyle}" 
                        Command="{Binding SelectAllLayersCommand}" Width="50"/>
                <Button Content="反选" Style="{StaticResource DefaultButtonStyle}" 
                        Command="{Binding InvertSelectionCommand}" Width="50"/>
            </StackPanel>
            
            <!-- 图层列表 -->
            <Border Grid.Row="2" BorderBrush="#CDCDCD" BorderThickness="1" MaxHeight="120">
                <ListBox ItemsSource="{Binding ClipLayerItems}" 
                         ScrollViewer.VerticalScrollBarVisibility="Auto"
                         Background="White" BorderThickness="0">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <CheckBox Content="{Binding LayerName}" 
                                      IsChecked="{Binding IsSelected}"
                                      Style="{StaticResource CheckBoxStyle}"
                                      Margin="2"/>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </Border>
        </Grid>

        <!-- 输出文件夹 -->
        <TextBlock Grid.Row="3" Grid.Column="0" Text="输出文件夹:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <Grid Grid.Row="3" Grid.Column="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <TextBox Grid.Column="0" Text="{Binding OutputFolder}" VerticalAlignment="Center" Height="22"
                    Style="{StaticResource TextBoxStyle}"/>
            <Button Grid.Column="1" Content="浏览..." Height="22" Margin="5,0,0,0" Command="{Binding BrowseFolderCommand}"
                    Style="{StaticResource DefaultButtonStyle}" VerticalAlignment="Center"/>
        </Grid>

        <!-- 是否单独创建文件夹 -->
        <TextBlock Grid.Row="4" Grid.Column="0" Text="单独创建文件夹:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <CheckBox Grid.Row="4" Grid.Column="1" Margin="0,0,0,10"
                Style="{StaticResource CheckBoxStyle}"
                IsChecked="{Binding CreateSubFolder}"
                Content="是否根据范围字段创建"/>

        <!-- 进度条 -->
        <ProgressBar Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="2"
                    Style="{StaticResource ProgressBarStyle}"
                    Height="6" Margin="0,0,0,2"
                    Value="{Binding Progress}"
                    Minimum="0" Maximum="100"
                    IsIndeterminate="{Binding IsProgressIndeterminate}"/>

        <!-- 日志窗口 -->
        <Border Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="2"
               BorderBrush="#CDCDCD" BorderThickness="1"
               Margin="0,0,0,10">
            <TextBox
                  Style="{StaticResource LogTextBoxStyle}"
                  Text="{Binding LogContent, Mode=OneWay}"
                  BorderThickness="0"
                  VerticalAlignment="Stretch"
                  HorizontalAlignment="Stretch"/>
        </Border>

        <!-- 状态消息和按钮区域 -->
        <Grid Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 执行状态 -->
            <TextBlock Grid.Row="0" Text="{Binding StatusMessage}" 
                    TextWrapping="Wrap" VerticalAlignment="Center" Margin="0,0,0,10"/>
            
            <!-- 按钮区域 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- 帮助按钮 -->
                <Button Grid.Column="0" Content="?" 
                        Style="{StaticResource HelpButtonStyle}"
                        Command="{Binding ShowHelpCommand}"
                        ToolTip="显示帮助信息"/>
                
                <!-- 停止按钮 -->
                <Button Grid.Column="2" Content="停止" 
                        Style="{StaticResource CancelButtonStyle}"
                        Command="{Binding CancelCommand}"/>
                
                <!-- 开始按钮 -->
                <Button Grid.Column="3" Content="开始" 
                        Style="{StaticResource ExecuteButtonStyle}"
                        Command="{Binding RunCommand}"
                        IsEnabled="{Binding CanProcess}"/>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
