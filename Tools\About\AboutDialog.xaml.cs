using System;
using System.Reflection;
using System.Windows;
using XIAOFUTools.Common;

namespace XIAOFUTools.Tools.About
{
    /// <summary>
    /// AboutDialog.xaml 的交互逻辑
    /// </summary>
    public partial class AboutDialog : Window
    {
        public AboutDialog()
        {
            InitializeComponent();
            LoadVersionInfo();
        }

        /// <summary>
        /// 加载版本信息
        /// </summary>
        private void LoadVersionInfo()
        {
            try
            {
                // 使用手动指定的版本号
                string versionString = XIAOFUTools.Common.VersionInfo.CurrentVersion;
                VersionText.Text = $"版本 {versionString}";
                CurrentVersionText.Text = versionString;
                
                // 获取编译日期（可选）
                Assembly assembly = Assembly.GetExecutingAssembly();
                var buildDate = GetBuildDate(assembly);
                if (buildDate.HasValue)
                {
                    DevelopmentEnvironmentText.Text += $"\n编译日期: {buildDate.Value:yyyy-MM-dd HH:mm:ss}";
                }
            }
            catch (Exception ex)
            {
                // 错误处理
                VersionText.Text = "版本信息获取失败";
                CurrentVersionText.Text = "未知";
                System.Diagnostics.Debug.WriteLine($"加载版本信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取程序集编译日期
        /// </summary>
        /// <param name="assembly">程序集</param>
        /// <returns>编译日期</returns>
        private DateTime? GetBuildDate(Assembly assembly)
        {
            try
            {
                var attribute = assembly.GetCustomAttribute<AssemblyMetadataAttribute>();
                if (attribute != null && attribute.Key == "BuildDate")
                {
                    if (DateTime.TryParse(attribute.Value, out DateTime buildDate))
                    {
                        return buildDate;
                    }
                }
                
                // 备用方法：从文件创建时间获取
                string assemblyLocation = assembly.Location;
                if (!string.IsNullOrEmpty(assemblyLocation) && System.IO.File.Exists(assemblyLocation))
                {
                    return System.IO.File.GetCreationTime(assemblyLocation);
                }
            }
            catch
            {
                // 忽略错误
            }
            
            return null;
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = true;
            this.Close();
        }

        /// <summary>
        /// 窗口加载完成事件
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // 可以在这里添加额外的初始化逻辑
        }
    }
}
