namespace XIAOFUTools.Tools.AIAssistant.Models
{
    /// <summary>
    /// AI服务配置模型
    /// </summary>
    public class AIServiceConfig
    {
        /// <summary>
        /// 服务名称
        /// </summary>
        public string ServiceName { get; set; }

        /// <summary>
        /// API端点
        /// </summary>
        public string ApiEndpoint { get; set; }

        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey { get; set; }

        /// <summary>
        /// 模型名称
        /// </summary>
        public string ModelName { get; set; }

        /// <summary>
        /// 最大令牌数
        /// </summary>
        public int MaxTokens { get; set; }

        /// <summary>
        /// 温度参数
        /// </summary>
        public double Temperature { get; set; }

        /// <summary>
        /// 是否启用流式输出
        /// </summary>
        public bool EnableStreaming { get; set; }

        /// <summary>
        /// 请求超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; }

        /// <summary>
        /// 创建默认的Moonshot配置
        /// </summary>
        /// <returns>Moonshot配置</returns>
        public static AIServiceConfig CreateMoonshotConfig()
        {
            return new AIServiceConfig
            {
                ServiceName = "Moonshot",
                ApiEndpoint = "https://api.moonshot.cn/v1",
                ApiKey = "sk-Khak6GZI5AWDbhZAFdJd6C9T6dgguhcX1WnbRATiZyzk7fCm",
                ModelName = "moonshot-v1-128k", // 使用正确的Moonshot模型名称
                MaxTokens = 4096, // 合理的输出长度
                Temperature = 0.7,
                EnableStreaming = true,
                TimeoutSeconds = 120 // 增加超时时间以支持长文本
            };
        }
    }
}
