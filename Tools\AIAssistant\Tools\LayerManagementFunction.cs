using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArcGIS.Desktop.Framework.Threading.Tasks;
using ArcGIS.Desktop.Mapping;
using Newtonsoft.Json;
using XIAOFUTools.Tools.AIAssistant.Models;
using XIAOFUTools.Tools.AIAssistant.Tools.Base;

namespace XIAOFUTools.Tools.AIAssistant.Tools
{
    /// <summary>
    /// 图层管理函数
    /// 提供图层的基本操作功能，如显示/隐藏、重命名等
    /// </summary>
    public class LayerManagementFunction : BaseOpenAIFunction
    {
        public override string Name => "manage_layers";

        public override string Description => "管理ArcGIS Pro地图图层，包括显示/隐藏图层、获取图层属性、重命名图层、列出所有图层等操作。";

        protected override FunctionParameters GetParametersDefinition()
        {
            var parameters = new FunctionParameters();
            
            parameters.AddProperty("action", 
                ParameterProperty.CreateString("要执行的操作", 
                    new List<string> { "show", "hide", "toggle_visibility", "get_info", "rename", "list" }), 
                true);
            
            parameters.AddProperty("layer_name", 
                ParameterProperty.CreateString("图层名称（对于list操作可选）"));
            
            parameters.AddProperty("new_name", 
                ParameterProperty.CreateString("新的图层名称（仅用于rename操作）"));

            return parameters;
        }

        protected override async Task<FunctionResult> ExecuteInternalAsync(FunctionCall functionCall, GISContext context)
        {
            Log($"开始执行图层管理操作，参数: {functionCall.Arguments}");

            if (context == null)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    "无法获取ArcGIS Pro上下文信息"
                );
            }

            // 解析参数
            var parameters = ParseArguments<LayerManagementParameters>(functionCall);

            try
            {
                return await QueuedTask.Run(() =>
                {
                    var mapView = MapView.Active;
                    if (mapView?.Map == null)
                    {
                        return FunctionResult.CreateFailure(
                            functionCall.Id,
                            functionCall.Name,
                            "没有活动的地图视图"
                        );
                    }

                    var map = mapView.Map;

                    switch (parameters.Action.ToLower())
                    {
                        case "list":
                            return ListLayers(functionCall, map);

                        case "show":
                        case "hide":
                        case "toggle_visibility":
                            return ChangeLayerVisibility(functionCall, map, parameters);

                        case "get_info":
                            return GetLayerInfo(functionCall, map, parameters.LayerName);

                        case "rename":
                            return RenameLayer(functionCall, map, parameters);

                        default:
                            return FunctionResult.CreateFailure(
                                functionCall.Id,
                                functionCall.Name,
                                $"不支持的操作: {parameters.Action}"
                            );
                    }
                });
            }
            catch (Exception ex)
            {
                Log($"执行图层管理操作时发生错误: {ex.Message}", LogLevel.Error);
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"操作失败: {ex.Message}"
                );
            }
        }

        private FunctionResult ListLayers(FunctionCall functionCall, Map map)
        {
            var layers = map.GetLayersAsFlattenedList().OfType<Layer>().Select(layer => new
            {
                name = layer.Name,
                type = layer.GetType().Name,
                visible = layer.IsVisible,
                uri = layer.URI
            }).ToArray();

            var result = new
            {
                total_layers = layers.Length,
                layers = layers
            };

            return FunctionResult.CreateSuccessWithData(
                functionCall.Id,
                functionCall.Name,
                result,
                $"成功列出 {layers.Length} 个图层"
            );
        }

        private FunctionResult ChangeLayerVisibility(FunctionCall functionCall, Map map, LayerManagementParameters parameters)
        {
            if (string.IsNullOrWhiteSpace(parameters.LayerName))
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    "图层名称不能为空"
                );
            }

            var layer = map.FindLayers(parameters.LayerName).FirstOrDefault();
            if (layer == null)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"未找到图层: {parameters.LayerName}"
                );
            }

            bool newVisibility;
            switch (parameters.Action.ToLower())
            {
                case "show":
                    newVisibility = true;
                    break;
                case "hide":
                    newVisibility = false;
                    break;
                case "toggle_visibility":
                    newVisibility = !layer.IsVisible;
                    break;
                default:
                    return FunctionResult.CreateFailure(
                        functionCall.Id,
                        functionCall.Name,
                        $"无效的可见性操作: {parameters.Action}"
                    );
            }

            layer.SetVisibility(newVisibility);

            var result = new
            {
                layer_name = layer.Name,
                action = parameters.Action,
                new_visibility = newVisibility,
                previous_visibility = !newVisibility
            };

            return FunctionResult.CreateSuccessWithData(
                functionCall.Id,
                functionCall.Name,
                result,
                $"成功{(newVisibility ? "显示" : "隐藏")}图层: {layer.Name}"
            );
        }

        private FunctionResult GetLayerInfo(FunctionCall functionCall, Map map, string layerName)
        {
            if (string.IsNullOrWhiteSpace(layerName))
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    "图层名称不能为空"
                );
            }

            var layer = map.FindLayers(layerName).FirstOrDefault();
            if (layer == null)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"未找到图层: {layerName}"
                );
            }

            var layerInfo = new
            {
                name = layer.Name,
                type = layer.GetType().Name,
                visible = layer.IsVisible,
                uri = layer.URI
            };

            return FunctionResult.CreateSuccessWithData(
                functionCall.Id,
                functionCall.Name,
                layerInfo,
                $"成功获取图层信息: {layer.Name}"
            );
        }

        private FunctionResult RenameLayer(FunctionCall functionCall, Map map, LayerManagementParameters parameters)
        {
            if (string.IsNullOrWhiteSpace(parameters.LayerName))
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    "原图层名称不能为空"
                );
            }

            if (string.IsNullOrWhiteSpace(parameters.NewName))
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    "新图层名称不能为空"
                );
            }

            var layer = map.FindLayers(parameters.LayerName).FirstOrDefault();
            if (layer == null)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"未找到图层: {parameters.LayerName}"
                );
            }

            var oldName = layer.Name;
            layer.SetName(parameters.NewName);

            var result = new
            {
                old_name = oldName,
                new_name = parameters.NewName,
                layer_type = layer.GetType().Name
            };

            return FunctionResult.CreateSuccessWithData(
                functionCall.Id,
                functionCall.Name,
                result,
                $"成功重命名图层: {oldName} → {parameters.NewName}"
            );
        }

        /// <summary>
        /// 图层管理参数模型
        /// </summary>
        private class LayerManagementParameters
        {
            [JsonProperty("action")]
            public string Action { get; set; }

            [JsonProperty("layer_name")]
            public string LayerName { get; set; }

            [JsonProperty("new_name")]
            public string NewName { get; set; }
        }
    }
}
