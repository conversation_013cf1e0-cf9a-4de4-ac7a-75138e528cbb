using System.Threading.Tasks;
using XIAOFUTools.Tools.AIAssistant.Models;

namespace XIAOFUTools.Tools.AIAssistant.Tools.Base
{
    /// <summary>
    /// OpenAI Function Calling兼容的函数接口
    /// 基于OpenAI Function Calling规范设计
    /// </summary>
    public interface IOpenAIFunction
    {
        /// <summary>
        /// 获取OpenAI函数定义
        /// </summary>
        /// <returns>OpenAI函数定义</returns>
        OpenAIFunction GetFunctionDefinition();

        /// <summary>
        /// 执行函数调用
        /// </summary>
        /// <param name="functionCall">函数调用请求</param>
        /// <returns>函数执行结果</returns>
        Task<FunctionResult> ExecuteAsync(FunctionCall functionCall);

        /// <summary>
        /// 验证函数调用参数
        /// </summary>
        /// <param name="functionCall">函数调用请求</param>
        /// <returns>验证结果</returns>
        bool ValidateCall(FunctionCall functionCall);
    }
}
