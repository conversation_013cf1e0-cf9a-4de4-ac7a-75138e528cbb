# XIAOFUTools 技术架构文档

## 项目结构

### 目录组织
```
XIAOFUTools/
├── Tools/                    # 工具模块目录
│   ├── AIAssistant/         # AI助手模块
│   ├── BatchAddData/        # 批量添加数据模块
│   ├── Authorization/       # 授权管理模块
│   └── ...                  # 其他工具模块
├── Common/                  # 公共组件
├── Data/                    # 数据文件
├── Images/                  # 图标资源
├── Styles/                  # 样式文件
├── Config.daml             # ArcGIS Pro插件配置
├── Module1.cs              # 主模块入口
└── XIAOFUTools.csproj      # 项目文件
```

### 核心技术栈
- **.NET 8.0**: 现代化的.NET框架
- **ArcGIS Pro SDK**: GIS功能核心API
- **WPF**: 主要UI框架
- **MVVM**: 架构设计模式
- **WebView2**: 现代Web界面支持
- **DuckDB.NET**: 高性能数据处理
- **Newtonsoft.Json**: JSON数据处理

## 架构设计模式

### MVVM架构
每个工具模块都采用MVVM（Model-View-ViewModel）架构：

```
Tool Module/
├── ToolButton.cs           # 按钮控制器
├── ToolDockPane.cs         # 停靠窗格
├── ToolDockPaneView.xaml   # 视图界面
├── ToolDockPaneView.xaml.cs # 视图代码
└── ToolViewModel.cs        # 视图模型
```

### 组件职责分离
1. **Button**: 工具入口，处理授权检查和窗格显示
2. **DockPane**: 窗格容器，管理视图生命周期
3. **View**: UI界面定义，纯XAML声明式界面
4. **ViewModel**: 业务逻辑，数据绑定和命令处理

## 授权系统架构

### 授权检查流程
```csharp
// 每个工具按钮都包含授权检查
protected override void OnClick()
{
    if (!AuthorizationChecker.CheckAuthorizationWithPrompt("工具名称"))
    {
        return; // 授权失败，阻止工具启动
    }
    // 授权成功，继续执行工具逻辑
}
```

### 授权管理组件
- **AuthorizationChecker**: 授权验证核心类
- **AuthorizationManager**: 授权状态管理
- **AuthorizationStatus**: 授权状态枚举
- **AuthorizationDockPane**: 授权管理界面

## 数据处理架构

### ArcGIS Pro SDK集成
- **数据访问**: 使用ArcGIS.Core.Data命名空间
- **几何处理**: 使用ArcGIS.Core.Geometry命名空间
- **地图操作**: 使用ArcGIS.Desktop.Mapping命名空间
- **编辑操作**: 使用ArcGIS.Desktop.Editing命名空间

### 异步操作模式
```csharp
// 所有耗时操作都使用异步模式
await QueuedTask.Run(() =>
{
    // 在ArcGIS Pro主线程中执行GIS操作
});
```

## AI助手架构

### 技术实现
- **前端**: 基于WebView2的现代Web界面
- **后端**: C# WebAPI服务
- **通信**: JavaScript与C#双向通信
- **模型支持**: OpenAI、Claude、Kimi等多种LLM

### 组件结构
```
AIAssistant/
├── WebUI/                  # Web前端界面
│   ├── index.html         # 主页面
│   ├── script.js          # 前端逻辑
│   └── style.css          # 样式文件
├── Services/              # 后端服务
│   ├── AIService.cs       # AI服务核心
│   └── ConfigService.cs   # 配置管理
├── Models/                # 数据模型
└── Tools/                 # 工具集成
```

## 性能优化策略

### 内存管理
- 使用`using`语句确保资源释放
- 及时释放ArcGIS对象引用
- 避免内存泄漏的事件订阅

### 异步处理
- 所有IO操作使用异步方法
- 长时间运行的任务使用后台线程
- 提供取消令牌支持用户中断操作

### 缓存机制
- 图层信息缓存
- 坐标系定义缓存
- 用户设置本地缓存

## 错误处理机制

### 异常处理策略
```csharp
try
{
    // 业务逻辑
}
catch (Exception ex)
{
    // 记录错误日志
    Logger.Error($"操作失败: {ex.Message}");
    
    // 用户友好的错误提示
    MessageBox.Show($"操作失败: {ex.Message}", "错误");
}
```

### 日志系统
- 使用结构化日志记录
- 分级别记录（Error、Warning、Info、Debug）
- 支持日志文件轮转

## 扩展开发指南

### 新工具开发步骤
1. 在Tools目录创建工具文件夹
2. 实现Button、DockPane、View、ViewModel类
3. 在Config.daml中注册工具
4. 添加授权检查逻辑
5. 编写单元测试

### 代码规范
- 使用C# 8.0+语法特性
- 遵循Microsoft编码规范
- 使用有意义的命名
- 添加XML文档注释

### 测试策略
- 单元测试覆盖核心逻辑
- 集成测试验证ArcGIS交互
- 用户界面自动化测试
- 性能基准测试

## 部署架构

### 构建流程
1. .NET编译生成程序集
2. 资源文件打包
3. 生成ArcGIS Pro Add-in包
4. 数字签名验证

### 安装部署
- 支持.esriAddinX格式安装包
- 自动检测ArcGIS Pro版本兼容性
- 支持静默安装和卸载
- 提供安装日志和错误诊断

## 维护和监控

### 版本管理
- 语义化版本号（SemVer）
- 自动化版本号生成
- 变更日志自动生成

### 监控指标
- 工具使用频率统计
- 错误率和性能指标
- 用户反馈收集
- 授权状态监控
