﻿<UserControl x:Class="XIAOFUTools.Tools.OvertureLoader.Views.WizardDockpaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             xmlns:ui="clr-namespace:ArcGIS.Desktop.Framework.Controls;assembly=ArcGIS.Desktop.Framework"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.OvertureLoader.Views"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="400"
             d:DataContext="{d:DesignInstance local:WizardDockpaneViewModel, IsDesignTimeCreatable=True}">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <extensions:DesignOnlyResourceDictionary Source="pack://application:,,,/ArcGIS.Desktop.Framework;component\Themes\Default.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <BitmapImage x:Key="OvertureIcon" UriSource="../Images/Overture16.png" />
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <!-- Header with title, version info and help button -->
        <Grid Grid.Row="0" Margin="16,12,16,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" Text="{Binding LatestRelease, StringFormat='Overture 版本: {0}'}" Style="{DynamicResource Esri_TextBlockH2}" Foreground="{DynamicResource Esri_TextCaptionActiveBrush}"/>
            <Button Grid.Column="1"
                    Content="?"
                    Width="18"
                    Height="18"
                    FontSize="10"
                    FontWeight="Bold"
                    ToolTip="关于 Overture Maps 数据加载器"
                    Command="{Binding ShowHelpCommand}"
                    Style="{DynamicResource Esri_SimpleButton}"
                    Margin="6,0,0,0"
                    Padding="0"
                    MinWidth="0"
                    MinHeight="0"/>
        </Grid>
        <!-- Main content area with TabControl -->
        <TabControl Grid.Row="1" Margin="0,8,0,0" SelectedIndex="{Binding SelectedTabIndex, Mode=TwoWay}">
            <!-- Select Data Tab -->
            <TabItem Header="选择数据">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16,12,16,12" Orientation="Vertical" >
                        <!-- 1. Choose Themes -->
                        <TextBlock Text="1. 选择主题" Margin="0,4,0,4"/>
                        <Border 
                                BorderBrush="{DynamicResource Esri_BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="2"
                                Padding="8"
                                Margin="0,0,0,12">
                            <StackPanel>
                                <CheckBox Content="选择所有主题"
                                          IsChecked="{Binding IsSelectAllChecked, Mode=TwoWay}"
                                          Margin="0,0,0,8"
                                          />
                                <ItemsControl ItemsSource="{Binding Themes}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate DataType="{x:Type local:SelectableThemeItem}">
                                            <Expander Style="{DynamicResource Esri_Expander}" Header="{Binding DisplayName}" IsExpanded="{Binding IsExpanded, Mode=TwoWay}" Margin="0,2">
                                                <Expander.HeaderTemplate>
                                                    <DataTemplate>
                                                        <StackPanel Orientation="Horizontal">
                                                            <CheckBox IsChecked="{Binding DataContext.IsSelected, RelativeSource={RelativeSource AncestorType=Expander}, Mode=TwoWay, TargetNullValue={x:Null}}"
                                                                      IsThreeState="True" 
                                                                      Visibility="{Binding DataContext.IsExpandable, RelativeSource={RelativeSource AncestorType=Expander}, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                                      VerticalAlignment="Center" Margin="0,0,5,0"/>
                                                            <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                        </StackPanel>
                                                    </DataTemplate>
                                                </Expander.HeaderTemplate>
                                                <ItemsControl ItemsSource="{Binding SubItems}" Margin="20,5,0,5">
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate DataType="{x:Type local:SelectableThemeItem}">
                                                            <CheckBox Content="{Binding DisplayName}" 
                                                                      IsChecked="{Binding IsSelected, Mode=TwoWay}" 
                                                                      Style="{DynamicResource Esri_CheckBox}" 
                                                                      Margin="0,2,0,2"
                                                                      />
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </Expander>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>
                        <!-- 2. Select Area -->
                        <TextBlock Text="2. 选择区域" Margin="0,0,0,4"/>
                        <Border
                                BorderBrush="{DynamicResource Esri_BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="2"
                                Padding="8"
                                Margin="0,0,0,12">
                            <StackPanel>
                                <TextBlock Text="数据范围:" Margin="0,0,0,4"/>
                                <RadioButton Content="当前地图范围" IsChecked="{Binding UseCurrentMapExtent}" Margin="0,2,0,0" />
                                <RadioButton Content="自定义范围" IsChecked="{Binding UseCustomExtent}"  Margin="0,2,0,0"/>
                                <Button Content="设置自定义范围..." HorizontalAlignment="Left" Margin="16,4,0,0" Command="{Binding SetCustomExtentCommand}" Style="{DynamicResource Esri_Button}" IsEnabled="{Binding UseCustomExtent}"/>
                                <Border  BorderBrush="{DynamicResource Esri_BorderBrush}" BorderThickness="1" Padding="6" Margin="16,4,0,0" Visibility="{Binding HasCustomExtent, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <StackPanel>
                                        <TextBlock Text="自定义范围坐标:"   FontWeight="SemiBold" Margin="0,0,0,2" Foreground="{DynamicResource Esri_TextStyleDefaultBrush}"/>
                                        <TextBlock Text="{Binding CustomExtentDisplay}"  TextWrapping="Wrap" Foreground="{DynamicResource Esri_TextStyleDefaultBrush}"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Border>
                        <!-- 3. Output Settings -->
                        <TextBlock Text="3. 输出设置"  Margin="0,0,0,4"/>
                        <Border
                                BorderBrush="{DynamicResource Esri_BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="2"
                                Padding="8"
                                Margin="0,0,0,12">
                            <StackPanel>
                                <TextBlock TextWrapping="Wrap" Margin="0,4,0,4">
                                    文件将以 GeoParquet 格式保存。加载数据后，您可以选择从"创建 MFC"选项卡创建多文件要素连接 (MFC)。
                                </TextBlock>
                                <Grid Margin="0,4,0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="数据输出位置:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBox Grid.Column="1" Text="{Binding DataOutputPath}" VerticalAlignment="Center" IsReadOnly="True" Margin="0,0,4,0" Style="{DynamicResource Esri_TextBox}"/>
                                    <Button Grid.Column="2" Content="浏览..." Style="{DynamicResource Esri_Button}" Command="{Binding BrowseDataLocationCommand}" Width="70"/>
                                </Grid>
                            </StackPanel>
                        </Border>
                        <!-- Selected Themes Preview -->
                        <TextBlock Text="已选主题预览" Margin="0,0,0,4"/>
                        <Border
                                BorderBrush="{DynamicResource Esri_BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="2"
                                Padding="8"
                                Margin="0,0,0,12">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                    <Image Width="24" Height="24" Margin="0,0,8,0" VerticalAlignment="Center" Source="{StaticResource OvertureIcon}"/>
                                    <TextBlock Text="已选数据类型:"  VerticalAlignment="Center" Margin="0,0,4,0"/>
                                    <TextBlock Text="{Binding SelectedLeafItemCount}" Style="{DynamicResource Esri_TextBlockH4}" Foreground="{DynamicResource Esri_TextCaptionActiveBrush}" VerticalAlignment="Center"/>
                                </StackPanel>
                                <Border Background="{DynamicResource Esri_DialogClientAreaBackgroundBrush}" BorderBrush="{DynamicResource Esri_BorderBrush}" BorderThickness="1" Padding="8" Margin="0,0,0,8" Visibility="{Binding SelectedLeafItemCount, Converter={StaticResource BooleanToVisibilityConverter}, FallbackValue=Collapsed}">
                                    <StackPanel>
                                        <TextBlock Text="已选数据类型:"  Foreground="{DynamicResource Esri_TextStyleSubduedBrush}" Margin="0,0,0,4"/>
                                        <ItemsControl ItemsSource="{Binding AllSelectedLeafItemsForPreview}">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding DisplayName}" Style="{DynamicResource Esri_TextBlockRegular}" Foreground="{DynamicResource Esri_TextStyleDefaultBrush}" Margin="8,0,0,2"/>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </StackPanel>
                                </Border>
                                <Border  BorderBrush="{DynamicResource Esri_BorderBrush}" BorderThickness="1" Padding="8" Margin="0,0,0,8">
                                    <TextBlock Text="{Binding ThemeDescription}" TextWrapping="Wrap"/>
                                </Border>
                                <Grid Margin="0,0,0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Image Grid.Column="0" Width="16" Height="16" Margin="0,0,8,0" Source="{StaticResource OvertureIcon}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" Text="预计要素数量:" Margin="0,0,4,0"/>
                                    <TextBlock Grid.Column="2" Text="{Binding EstimatedFeatures}"/>
                                </Grid>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Image Grid.Column="0" Width="16" Height="16" Margin="0,0,8,0" Source="{StaticResource OvertureIcon}" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" Text="预计大小:" Margin="0,0,4,0"/>
                                    <TextBlock Grid.Column="2" Text="{Binding EstimatedSize}"/>
                                </Grid>
                            </StackPanel>
                        </Border>
                        <!-- Action Buttons -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <Button Content="取消" Command="{Binding CancelCommand}" Style="{DynamicResource Esri_Button}" Margin="0,0,8,0" Width="80"/>
                            <Button Content="加载数据" Command="{Binding LoadDataCommand}" Style="{DynamicResource Esri_ButtonBlue}" Width="100"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            <!-- Status Tab -->
            <TabItem Header="状态">
                <StackPanel Margin="16,12,16,12">
                    <TextBlock Text="操作进度" Style="{DynamicResource Esri_TextBlockH3}" Margin="0,0,0,4"/>
                    <Border Background="{DynamicResource Esri_DialogClientAreaBackgroundBrush}"
                            BorderBrush="{DynamicResource Esri_BorderBrush}"
                            BorderThickness="1"
                            CornerRadius="2"
                            Padding="8"
                            Margin="0,0,0,12">
                        <StackPanel>
                            <TextBlock Text="{Binding StatusText}" Style="{DynamicResource Esri_TextBlockRegular}" Foreground="{DynamicResource Esri_TextStyleDefaultBrush}" TextWrapping="Wrap" Margin="0,0,0,4"/>
                            <ProgressBar Value="{Binding ProgressValue}" Height="20" Margin="0,4,0,0" Style="{DynamicResource Esri_ProgressBar}"/>
                        </StackPanel>
                    </Border>
                    <TextBlock Text="日志输出" Style="{DynamicResource Esri_TextBlockH3}" Margin="0,0,0,4"/>
                    <Border Background="{DynamicResource Esri_DialogClientAreaBackgroundBrush}"
                            BorderBrush="{DynamicResource Esri_BorderBrush}"
                            BorderThickness="1"
                            CornerRadius="2"
                            Padding="8">
                        <TextBox x:Name="LogTextBox" Height="300" IsReadOnly="True" Text="{Binding LogOutputText, Mode=OneWay}" 
                                 TextWrapping="Wrap" VerticalScrollBarVisibility="Auto" Margin="0,0,0,12" 
                                 FontFamily="Consolas" Background="{DynamicResource Esri_DialogClientAreaBackgroundBrush}" 
                                 Foreground="{DynamicResource Esri_TextControlBrush}" Style="{DynamicResource Esri_TextBoxMedium}"
                                 TextChanged="LogTextBox_TextChanged"/>
                    </Border>

                    <!-- Navigation Buttons -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                        <Button Content="转到创建 MFC" Command="{Binding GoToCreateMfcTabCommand}" Style="{DynamicResource Esri_Button}" Width="120"/>
                    </StackPanel>
                </StackPanel>
            </TabItem>
            <!-- Create MFC Tab -->
            <TabItem Header="创建 MFC">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16,12,16,12" Orientation="Vertical">
                        <TextBlock Text="创建多文件要素连接" Style="{DynamicResource Esri_TextBlockH3}" Margin="0,0,0,12"/>

                        <Border Background="{DynamicResource Esri_DialogClientAreaBackgroundBrush}"
                                BorderBrush="{DynamicResource Esri_BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="2"
                                Padding="8"
                                Margin="0,0,0,12">
                            <TextBlock TextWrapping="Wrap" Foreground="{DynamicResource Esri_TextStyleDefaultBrush}">
                                多文件要素连接 (MFC) 允许 ArcGIS Pro 将多个 GeoParquet 文件作为单个数据集高效处理。这使得可视化和分析 Overture Maps 数据变得更加容易。
                                <LineBreak/><LineBreak/>
                                注意：创建 MFC 可能需要一些时间，因为它会索引您的所有数据文件。
                            </TextBlock>
                        </Border>

                        <TextBlock Text="MFC 设置" Style="{DynamicResource Esri_TextBlockH4}" Margin="0,0,0,4"/>
                        <Border
                                BorderBrush="{DynamicResource Esri_BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="2"
                                Padding="8"
                                Margin="0,0,0,12">
                            <StackPanel>
                                <!-- Data Source Group -->
                                <TextBlock Text="数据源:" FontWeight="SemiBold" Margin="0,0,0,2"/>
                                <RadioButton Content="使用之前加载的数据（推荐）" GroupName="MfcDataSource" IsChecked="{Binding UsePreviouslyLoadedData}" Margin="0,0,0,5"
                                             ToolTipService.ToolTip="从此插件在当前会话中下载的数据创建 MFC。"/>
                                <RadioButton Content="使用自定义数据文件夹" GroupName="MfcDataSource" IsChecked="{Binding UseCustomDataFolder}"
                                             ToolTipService.ToolTip="从您在不同文件夹中的 GeoParquet 文件创建 MFC。"/>
                                <StackPanel Orientation="Horizontal" Margin="20,5,0,5" IsEnabled="{Binding UseCustomDataFolder}">
                                    <TextBox Text="{Binding CustomDataFolderPath, UpdateSourceTrigger=PropertyChanged}" Width="250" Margin="0,0,5,0"
                                             ToolTipService.ToolTip="包含 GeoParquet 文件的文件夹路径。"/>
                                    <Button Content="浏览..." Command="{Binding BrowseCustomDataFolderCommand}"/>
                                </StackPanel>
                                <TextBlock Margin="20,2,0,10" IsEnabled="{Binding UseCustomDataFolder}" TextWrapping="Wrap">
                                    <Run Text="如果使用自定义文件夹，数据应该按每个数据集类型组织在自己的子文件夹中（例如，YourFolder/buildings/building.parquet）。插件会为下载的数据处理这个问题。"/>
                                    <Hyperlink NavigateUri="https://pro.arcgis.com/en/pro-app/latest/help/data/big-data-connections/big-data-connections.htm" RequestNavigate="Hyperlink_RequestNavigate">
                                        <Run Text="了解更多关于 MFC 结构的信息。"/>
                                    </Hyperlink>
                                </TextBlock>

                                <!-- MFC Output Location Group -->
                                <TextBlock Text="MFC 输出位置:" FontWeight="SemiBold" Margin="0,10,0,2"/>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                    <TextBox Text="{Binding MfcOutputPath, UpdateSourceTrigger=PropertyChanged}" Width="300" Margin="0,0,5,0"
                                             ToolTipService.ToolTip="保存 .mfc 连接文件的文件夹。"/>
                                    <Button Content="浏览..." Command="{Binding BrowseMfcLocationCommand}"/>
                                </StackPanel>

                                <!-- MFC Options Group -->
                                <TextBlock Text="MFC 选项:" FontWeight="SemiBold" Margin="0,10,0,2"/>
                                <CheckBox IsChecked="{Binding IsSharedMfc}" Content="创建为共享连接" Margin="0,0,0,5"
                                          ToolTipService.ToolTip="共享连接可以被多个项目使用。独立连接特定于当前项目。"/>
                                <!-- <CheckBox IsChecked="{Binding UseSpatialIndex}" Content="Use Spatial Index (if applicable)" Margin="0,0,0,10"
                                          ToolTipService.ToolTip="Enable spatial indexing for faster queries. Generally recommended."/> -->

                                <!-- Action Button -->
                                <Button Content="创建多文件要素连接" Command="{Binding CreateMfcCommand}" HorizontalAlignment="Left" Margin="0,15,0,0" Style="{DynamicResource Esri_Button}"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
        <!-- Loading Overlay -->
        <Grid Grid.Row="1" Background="{DynamicResource Esri_DockPaneClientAreaBackgroundBrush}" Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <ProgressBar Style="{DynamicResource Esri_ProgressBar}" IsIndeterminate="True" Width="200" Height="10" Margin="0,0,0,8"/>
                <TextBlock Text="正在初始化 Overture Maps 数据加载器..." Style="{DynamicResource Esri_TextBlockRegular}" Foreground="{DynamicResource Esri_TextStyleEmphasisBrush}" HorizontalAlignment="Center"/>
                <TextBlock Text="请稍候，我们正在设置..." Style="{DynamicResource Esri_TextBlockSmall}" Foreground="{DynamicResource Esri_TextStyleSubduedBrush}" Opacity="0.7" Margin="0,4,0,0" HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>