<Window x:Class="XIAOFUTools.Tools.Authorization.AuthorizationPromptDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="需要授权" 
        Height="320" 
        Width="480"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False"
        WindowStyle="SingleBorderWindow">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题和图标 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
            <Image Source="pack://application:,,,/ArcGIS.Desktop.Resources;component/Images/GenericWarning32.png" 
                   Width="32" Height="32" Margin="0,0,10,0"/>
            <TextBlock Text="{Binding Title}" 
                       FontSize="16" 
                       FontWeight="Bold" 
                       VerticalAlignment="Center"/>
        </StackPanel>
        
        <!-- 主要内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 工具名称和授权要求 -->
                <TextBlock Text="{Binding ToolMessage}" 
                           FontSize="12" 
                           Margin="0,0,0,10"
                           TextWrapping="Wrap"/>
                
                <!-- 授权状态信息 -->
                <Border BorderBrush="#CCCCCC" 
                        BorderThickness="1" 
                        CornerRadius="3" 
                        Padding="10" 
                        Background="#F9F9F9"
                        Margin="0,0,0,10">
                    <StackPanel>
                        <TextBlock Text="授权状态信息:" 
                                   FontWeight="Bold" 
                                   FontSize="11" 
                                   Margin="0,0,0,5"/>
                        
                        <TextBlock Text="{Binding StatusMessage}" 
                                   FontSize="11" 
                                   Margin="0,0,0,5"
                                   TextWrapping="Wrap"/>
                        
                        <TextBlock Text="{Binding MachineCodeMessage}" 
                                   FontSize="11" 
                                   Margin="0,0,0,0"
                                   TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>
                
                <!-- 操作指引 -->
                <TextBlock Text="您可以选择以下操作：" 
                           FontSize="12" 
                           FontWeight="Bold" 
                           Margin="0,0,0,8"/>
                
                <TextBlock Text="• 点击'打开授权界面'直接进行授权设置"
                           FontSize="11"
                           Margin="10,0,0,3"/>

                <TextBlock Text="• 联系作者获取授权码：QQ 1922759464"
                           FontSize="11"
                           Margin="10,0,0,3"/>

                <TextBlock Text="• 或通过菜单：用户 → 配置 → 授权"
                           FontSize="11"
                           Margin="10,0,0,0"/>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,15,0,0">
            
            <Button Name="OpenAuthButton"
                    Content="打开授权界面"
                    Width="100"
                    Height="28"
                    Margin="0,0,10,0"
                    Click="OpenAuthButton_Click"
                    IsDefault="True">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Background" Value="#0078D4"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="BorderBrush" Value="#0078D4"/>
                        <Setter Property="BorderThickness" Value="1"/>
                        <Setter Property="Padding" Value="8,4"/>
                        <Setter Property="FontSize" Value="11"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#106EBE"/>
                                <Setter Property="BorderBrush" Value="#106EBE"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                                <Setter Property="BorderBrush" Value="#005A9E"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
            
            <Button Name="CancelButton" 
                    Content="取消" 
                    Width="60" 
                    Height="28" 
                    Click="CancelButton_Click"
                    IsCancel="True">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Background" Value="#F3F2F1"/>
                        <Setter Property="Foreground" Value="#323130"/>
                        <Setter Property="BorderBrush" Value="#8A8886"/>
                        <Setter Property="BorderThickness" Value="1"/>
                        <Setter Property="Padding" Value="8,4"/>
                        <Setter Property="FontSize" Value="11"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#EDEBE9"/>
                                <Setter Property="BorderBrush" Value="#8A8886"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#E1DFDD"/>
                                <Setter Property="BorderBrush" Value="#8A8886"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </StackPanel>
    </Grid>
</Window>
