using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XIAOFUTools.Tools.AIAssistant.Models;
using XIAOFUTools.Tools.AIAssistant.Tools.Base;
using XIAOFUTools.Tools.AIAssistant.Tools;

namespace XIAOFUTools.Tools.AIAssistant.Services
{
    /// <summary>
    /// OpenAI函数管理器
    /// 管理所有可用的OpenAI函数
    /// </summary>
    public class OpenAIFunctionManager
    {
        private static OpenAIFunctionManager _instance;
        private static readonly object _lock = new object();
        
        private readonly Dictionary<string, IOpenAIFunction> _functions;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static OpenAIFunctionManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new OpenAIFunctionManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private OpenAIFunctionManager()
        {
            _functions = new Dictionary<string, IOpenAIFunction>();
            RegisterDefaultFunctions();
        }

        /// <summary>
        /// 注册默认函数
        /// </summary>
        private void RegisterDefaultFunctions()
        {
            try
            {
                // 注册地图信息函数
                RegisterFunction(new MapInfoFunction());
                
                // 注册图层管理函数
                RegisterFunction(new LayerManagementFunction());
                
                // 注册要素选择函数
                RegisterFunction(new FeatureSelectionFunction());
                
                // 注册诊断函数
                RegisterFunction(new DiagnosticFunction());

                System.Diagnostics.Debug.WriteLine($"OpenAI函数管理器初始化完成，注册了 {_functions.Count} 个函数");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"注册默认函数时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 注册函数
        /// </summary>
        /// <param name="function">要注册的函数</param>
        public void RegisterFunction(IOpenAIFunction function)
        {
            if (function == null)
                throw new ArgumentNullException(nameof(function));

            var definition = function.GetFunctionDefinition();
            if (string.IsNullOrWhiteSpace(definition.Name))
                throw new ArgumentException("函数名称不能为空");

            _functions[definition.Name] = function;
            System.Diagnostics.Debug.WriteLine($"注册OpenAI函数: {definition.Name}");
        }

        /// <summary>
        /// 注销函数
        /// </summary>
        /// <param name="functionName">函数名称</param>
        public void UnregisterFunction(string functionName)
        {
            if (_functions.ContainsKey(functionName))
            {
                _functions.Remove(functionName);
                System.Diagnostics.Debug.WriteLine($"注销OpenAI函数: {functionName}");
            }
        }

        /// <summary>
        /// 获取所有可用函数定义
        /// </summary>
        /// <returns>函数定义列表</returns>
        public List<OpenAIFunction> GetAvailableFunctions()
        {
            return _functions.Values
                .Select(f => f.GetFunctionDefinition())
                .ToList();
        }

        /// <summary>
        /// 获取指定函数
        /// </summary>
        /// <param name="functionName">函数名称</param>
        /// <returns>函数实例</returns>
        public IOpenAIFunction GetFunction(string functionName)
        {
            return _functions.TryGetValue(functionName, out var function) ? function : null;
        }

        /// <summary>
        /// 执行函数调用
        /// </summary>
        /// <param name="functionCall">函数调用请求</param>
        /// <returns>函数执行结果</returns>
        public async Task<FunctionResult> ExecuteFunctionAsync(FunctionCall functionCall)
        {
            if (functionCall == null)
            {
                return FunctionResult.CreateFailure(
                    "unknown", 
                    "unknown", 
                    "函数调用请求为空"
                );
            }

            var function = GetFunction(functionCall.Name);
            if (function == null)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    $"未找到函数: {functionCall.Name}"
                );
            }

            return await function.ExecuteAsync(functionCall);
        }

        /// <summary>
        /// 批量执行函数调用
        /// </summary>
        /// <param name="functionCalls">函数调用列表</param>
        /// <returns>函数执行结果列表</returns>
        public async Task<List<FunctionResult>> ExecuteFunctionsAsync(List<FunctionCall> functionCalls)
        {
            if (functionCalls == null || !functionCalls.Any())
                return new List<FunctionResult>();

            var results = new List<FunctionResult>();
            
            foreach (var call in functionCalls)
            {
                var result = await ExecuteFunctionAsync(call);
                results.Add(result);
            }

            return results;
        }

        /// <summary>
        /// 检查函数是否存在
        /// </summary>
        /// <param name="functionName">函数名称</param>
        /// <returns>是否存在</returns>
        public bool HasFunction(string functionName)
        {
            return _functions.ContainsKey(functionName);
        }

        /// <summary>
        /// 获取函数统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public object GetStatistics()
        {
            return new
            {
                total_functions = _functions.Count,
                function_names = _functions.Keys.ToArray(),
                registered_at = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };
        }

        /// <summary>
        /// 清除所有函数
        /// </summary>
        public void ClearFunctions()
        {
            _functions.Clear();
            System.Diagnostics.Debug.WriteLine("清除所有OpenAI函数");
        }

        /// <summary>
        /// 重新加载默认函数
        /// </summary>
        public void ReloadDefaultFunctions()
        {
            ClearFunctions();
            RegisterDefaultFunctions();
            System.Diagnostics.Debug.WriteLine("重新加载默认OpenAI函数");
        }
    }
}
