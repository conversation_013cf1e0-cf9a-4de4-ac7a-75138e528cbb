<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 简洁背景渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6c5ce7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a29bfe;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="32" height="32" rx="8" fill="url(#bgGradient)"/>

  <!-- 图层堆叠 -->
  <rect x="4" y="18" width="24" height="4" rx="2" fill="#00b894" opacity="0.9"/>
  <rect x="4" y="14" width="24" height="4" rx="2" fill="#74b9ff" opacity="0.9"/>
  <rect x="4" y="10" width="24" height="4" rx="2" fill="#fd79a8" opacity="0.9"/>

  <!-- 模板图标 -->
  <rect x="8" y="4" width="16" height="4" rx="2" fill="#ffffff" opacity="0.9"/>
  <line x1="10" y1="5" x2="22" y2="5" stroke="#6c5ce7" stroke-width="1" opacity="0.6"/>
  <line x1="10" y1="6" x2="22" y2="6" stroke="#6c5ce7" stroke-width="1" opacity="0.6"/>
  <line x1="10" y1="7" x2="22" y2="7" stroke="#6c5ce7" stroke-width="1" opacity="0.6"/>

  <!-- 连接线 -->
  <line x1="16" y1="8" x2="16" y2="10" stroke="#ffffff" stroke-width="2" stroke-dasharray="2,2" opacity="0.7"/>

  <!-- 添加按钮 -->
  <circle cx="24" cy="24" r="3" fill="#00b894"/>
  <path d="M22 24 L26 24 M24 22 L24 26" stroke="#ffffff" stroke-width="2" stroke-linecap="round"/>

  <!-- 标题 -->
  <text x="16" y="16" fill="#ffffff" font-family="PingFang SC, Microsoft YaHei, SF Pro, Arial, sans-serif" font-size="5" font-weight="600" text-anchor="middle">预设图层</text>
</svg>