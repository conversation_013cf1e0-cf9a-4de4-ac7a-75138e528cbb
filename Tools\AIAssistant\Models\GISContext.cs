using System.Collections.Generic;

namespace XIAOFUTools.Tools.AIAssistant.Models
{
    /// <summary>
    /// GIS上下文模型
    /// </summary>
    public class GISContext
    {
        /// <summary>
        /// 当前地图名称
        /// </summary>
        public string MapName { get; set; }

        /// <summary>
        /// 当前地图范围
        /// </summary>
        public MapExtent CurrentExtent { get; set; }

        /// <summary>
        /// 当前比例尺
        /// </summary>
        public double Scale { get; set; }

        /// <summary>
        /// 活动图层列表
        /// </summary>
        public List<LayerInfo> ActiveLayers { get; set; }

        /// <summary>
        /// 选中要素数量
        /// </summary>
        public int SelectedFeatureCount { get; set; }

        /// <summary>
        /// 当前坐标系
        /// </summary>
        public string SpatialReference { get; set; }

        /// <summary>
        /// 选中图层名称
        /// </summary>
        public string SelectedLayerName { get; set; }

        /// <summary>
        /// 项目路径
        /// </summary>
        public string ProjectPath { get; set; }

        /// <summary>
        /// 是否有未保存的编辑
        /// </summary>
        public bool HasUnsavedEdits { get; set; }

        public GISContext()
        {
            ActiveLayers = new List<LayerInfo>();
        }
    }

    /// <summary>
    /// 地图范围
    /// </summary>
    public class MapExtent
    {
        public double XMin { get; set; }
        public double YMin { get; set; }
        public double XMax { get; set; }
        public double YMax { get; set; }
    }

    /// <summary>
    /// 图层信息
    /// </summary>
    public class LayerInfo
    {
        /// <summary>
        /// 图层名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 图层类型
        /// </summary>
        public string LayerType { get; set; }

        /// <summary>
        /// 几何类型
        /// </summary>
        public string GeometryType { get; set; }

        /// <summary>
        /// 要素数量
        /// </summary>
        public int FeatureCount { get; set; }

        /// <summary>
        /// 是否可见
        /// </summary>
        public bool IsVisible { get; set; }

        /// <summary>
        /// 是否可编辑
        /// </summary>
        public bool IsEditable { get; set; }

        /// <summary>
        /// 字段列表
        /// </summary>
        public List<string> Fields { get; set; }

        /// <summary>
        /// 数据源路径
        /// </summary>
        public string DataSource { get; set; }

        /// <summary>
        /// 图层描述
        /// </summary>
        public string Description { get; set; }

        public LayerInfo()
        {
            Fields = new List<string>();
        }
    }
}
