<UserControl x:Class="XIAOFUTools.Tools.AreaCalculator.AreaCalculatorDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.AreaCalculator"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="400"
             d:DataContext="{Binding Path=AreaCalculatorDockPaneViewModel}"
             Loaded="UserControl_Loaded">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 布尔值转可见性转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- 帮助按钮样式 -->
            <Style x:Key="HelpButtonStyle" TargetType="Button" BasedOn="{StaticResource TextButtonStyle}">
                <Setter Property="Width" Value="22"/>
                <Setter Property="Height" Value="22"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="Margin" Value="0"/>
                <Setter Property="HorizontalAlignment" Value="Left"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>
    
    <Grid Margin="12">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 面图层选择 -->
        <TextBlock Grid.Row="0" Grid.Column="0" Text="面图层:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <Grid Grid.Row="0" Grid.Column="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <ComboBox Grid.Column="0"
                    Style="{StaticResource ComboBoxStyle}"
                    ItemsSource="{Binding PolygonLayers}"
                    SelectedItem="{Binding SelectedPolygonLayer}"
                    DisplayMemberPath="Name"/>
            <Button Grid.Column="1" Width="22" Height="22" Margin="5,0,0,0"
                    Style="{StaticResource DefaultButtonStyle}"
                    Command="{Binding RefreshLayersCommand}"
                    ToolTip="刷新图层列表"
                    VerticalAlignment="Center">
                <TextBlock Text="⟲" FontSize="14" FontWeight="Bold"
                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Button>
        </Grid>

        <!-- 字段选择 -->
        <TextBlock Grid.Row="1" Grid.Column="0" Text="字段:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <ComboBox Grid.Row="1" Grid.Column="1" Margin="0,0,0,10"
                Style="{StaticResource ComboBoxStyle}"
                ItemsSource="{Binding FieldInfos}"
                SelectedItem="{Binding SelectedFieldInfo}"
                DisplayMemberPath="DisplayText"
                IsEnabled="{Binding HasSelectedLayer}"/>

        <!-- 单位选择 -->
        <TextBlock Grid.Row="2" Grid.Column="0" Text="单位:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <ComboBox Grid.Row="2" Grid.Column="1" Margin="0,0,0,10"
                Style="{StaticResource ComboBoxStyle}"
                ItemsSource="{Binding AreaUnits}"
                SelectedItem="{Binding SelectedAreaUnit}"/>

        <!-- 保留小数位数 -->
        <TextBlock Grid.Row="3" Grid.Column="0" Text="保留小数位数:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <TextBox Grid.Row="3" Grid.Column="1" Margin="0,0,0,10"
               Style="{StaticResource TextBoxStyle}"
               Text="{Binding DecimalPlaces}"/>

        <!-- 面积类型 -->
        <TextBlock Grid.Row="4" Grid.Column="0" Text="面积类型:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <ComboBox Grid.Row="4" Grid.Column="1" Margin="0,0,0,10"
                Style="{StaticResource ComboBoxStyle}"
                ItemsSource="{Binding AreaTypes}"
                SelectedItem="{Binding SelectedAreaType}"/>

        <!-- 椭球注意提示 -->
        <TextBlock Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,10"
                 Text="注意：椭球面积计算基于地理坐标系统，适用于大范围区域的精确面积计算。"
                 TextWrapping="Wrap"
                 Foreground="{StaticResource TextSecondaryBrush}"
                 FontSize="10"
                 Visibility="{Binding IsEllipsoidSelected, Converter={StaticResource BooleanToVisibilityConverter}}"/>

        <!-- 进度条 -->
        <ProgressBar Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,10"
                   Style="{StaticResource ProgressBarStyle}"
                   Value="{Binding Progress}"
                   Minimum="0" Maximum="100"
                   IsIndeterminate="{Binding IsProgressIndeterminate}"
                   Height="6"/>

        <!-- 日志窗口 -->
        <Border Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="2"
               BorderBrush="#CDCDCD" BorderThickness="1"
               Margin="0,0,0,10">
            <TextBox
                  Style="{StaticResource LogTextBoxStyle}"
                  Text="{Binding LogContent, Mode=OneWay}"
                  BorderThickness="0"
                  VerticalAlignment="Stretch"
                  HorizontalAlignment="Stretch"/>
        </Border>

        <!-- 状态消息和按钮区域 -->
        <Grid Grid.Row="8" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 执行状态 -->
            <TextBlock Grid.Row="0" Text="{Binding StatusMessage}" 
                    TextWrapping="Wrap" VerticalAlignment="Center" Margin="0,0,0,10"/>

            <!-- 按钮区域 -->
            <Border Grid.Row="1" BorderBrush="{StaticResource DividerBrush}"
                   BorderThickness="0,1,0,0"
                   Margin="0,3,0,0"
                   Padding="0,8,0,0">
                <Grid>
                    <Button Content="?" Width="22" Height="22"
                            Style="{StaticResource HelpButtonStyle}"
                            Command="{Binding ShowHelpCommand}"
                            ToolTip="查看工具使用说明"
                            HorizontalAlignment="Left"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <!-- 只在处理时显示停止按钮 -->
                        <Button Content="停止" Width="80" Command="{Binding CancelCommand}" Margin="0,0,10,0"
                                Style="{StaticResource CancelButtonStyle}"
                                Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}"
                                IsEnabled="{Binding IsProcessing}"/>
                        <Button Content="开始" Width="80" Command="{Binding RunCommand}"
                                Style="{StaticResource ExecuteButtonStyle}"
                                IsEnabled="{Binding CanProcess}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
