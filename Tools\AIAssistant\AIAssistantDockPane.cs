using ArcGIS.Desktop.Framework;
using ArcGIS.Desktop.Framework.Contracts;

namespace XIAOFUTools.Tools.AIAssistant
{
    /// <summary>
    /// AI助手停靠窗格
    /// </summary>
    internal class AIAssistantDockPane : DockPane
    {
        private const string _dockPaneID = "XIAOFUTools_AIAssistantDockPane";

        protected AIAssistantDockPane() { }

        /// <summary>
        /// 创建停靠窗格内容
        /// </summary>
        protected override System.Windows.Controls.Control OnCreateContent()
        {
            return new AIAssistantDockPaneView();
        }

        /// <summary>
        /// 显示停靠窗格
        /// </summary>
        internal static void Show()
        {
            DockPane pane = FrameworkApplication.DockPaneManager.Find(_dockPaneID);
            pane?.Activate();
        }
    }
}
