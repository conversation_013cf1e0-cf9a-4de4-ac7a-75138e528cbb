using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Newtonsoft.Json;
using XIAOFUTools.Tools.AIAssistant.Models;
using XIAOFUTools.Tools.AIAssistant.Services;

namespace XIAOFUTools.Tools.AIAssistant.Tools.Base
{
    /// <summary>
    /// OpenAI函数基类
    /// 实现IOpenAIFunction接口并提供通用功能
    /// </summary>
    public abstract class BaseOpenAIFunction : IOpenAIFunction
    {
        /// <summary>
        /// 函数名称
        /// </summary>
        public abstract string Name { get; }

        /// <summary>
        /// 函数描述
        /// </summary>
        public abstract string Description { get; }

        /// <summary>
        /// 是否需要ArcGIS Pro上下文
        /// </summary>
        protected virtual bool RequiresContext => true;

        /// <summary>
        /// 获取OpenAI函数定义
        /// </summary>
        /// <returns>OpenAI函数定义</returns>
        public virtual OpenAIFunction GetFunctionDefinition()
        {
            return OpenAIFunction.Create(Name, Description, GetParametersDefinition());
        }

        /// <summary>
        /// 获取函数参数定义（由子类实现）
        /// </summary>
        /// <returns>参数定义</returns>
        protected abstract FunctionParameters GetParametersDefinition();

        /// <summary>
        /// 执行函数调用
        /// </summary>
        /// <param name="functionCall">函数调用请求</param>
        /// <returns>函数执行结果</returns>
        public async Task<FunctionResult> ExecuteAsync(FunctionCall functionCall)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                Log($"开始执行函数: {functionCall.Name}，参数: {functionCall.Arguments}");

                // 验证函数调用
                if (!ValidateCall(functionCall))
                {
                    return FunctionResult.CreateFailure(
                        functionCall.Id, 
                        functionCall.Name, 
                        "函数调用验证失败"
                    );
                }

                // 获取上下文信息（如果需要）
                GISContext context = null;
                if (RequiresContext)
                {
                    var gisService = new GISAgentService();
                    context = await gisService.GetCurrentGISContextAsync();
                }

                // 执行具体函数逻辑
                var result = await ExecuteInternalAsync(functionCall, context);
                
                stopwatch.Stop();
                result.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
                
                Log($"函数执行完成: {functionCall.Name}，耗时: {stopwatch.ElapsedMilliseconds}ms");
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Log($"函数执行失败: {functionCall.Name}，错误: {ex.Message}", LogLevel.Error);
                
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    ex.Message,
                    stopwatch.ElapsedMilliseconds
                );
            }
        }

        /// <summary>
        /// 具体函数的执行逻辑（由子类实现）
        /// </summary>
        /// <param name="functionCall">函数调用请求</param>
        /// <param name="context">GIS上下文信息</param>
        /// <returns>函数执行结果</returns>
        protected abstract Task<FunctionResult> ExecuteInternalAsync(FunctionCall functionCall, GISContext context);

        /// <summary>
        /// 验证函数调用参数
        /// </summary>
        /// <param name="functionCall">函数调用请求</param>
        /// <returns>验证结果</returns>
        public virtual bool ValidateCall(FunctionCall functionCall)
        {
            if (functionCall == null)
            {
                Log("函数调用为空", LogLevel.Error);
                return false;
            }

            if (string.IsNullOrWhiteSpace(functionCall.Name))
            {
                Log("函数名称为空", LogLevel.Error);
                return false;
            }

            if (functionCall.Name != Name)
            {
                Log($"函数名称不匹配: 期望 {Name}，实际 {functionCall.Name}", LogLevel.Error);
                return false;
            }

            // 验证参数JSON格式
            if (!string.IsNullOrWhiteSpace(functionCall.Arguments))
            {
                try
                {
                    JsonConvert.DeserializeObject(functionCall.Arguments);
                }
                catch (JsonException ex)
                {
                    Log($"参数JSON格式无效: {ex.Message}", LogLevel.Error);
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 解析函数参数为指定类型
        /// </summary>
        /// <typeparam name="T">参数类型</typeparam>
        /// <param name="functionCall">函数调用</param>
        /// <returns>解析后的参数对象</returns>
        protected T ParseArguments<T>(FunctionCall functionCall) where T : class, new()
        {
            if (string.IsNullOrWhiteSpace(functionCall.Arguments))
                return new T();

            try
            {
                return JsonConvert.DeserializeObject<T>(functionCall.Arguments) ?? new T();
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"无法解析函数参数: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 记录函数执行日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="level">日志级别</param>
        protected void Log(string message, LogLevel level = LogLevel.Info)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            var logMessage = $"[{timestamp}] [{level}] [OpenAI-{Name}] {message}";
            
            // 输出到调试控制台
            Debug.WriteLine(logMessage);
        }

        /// <summary>
        /// 日志级别枚举
        /// </summary>
        protected enum LogLevel
        {
            Debug,
            Info,
            Warning,
            Error
        }
    }
}
