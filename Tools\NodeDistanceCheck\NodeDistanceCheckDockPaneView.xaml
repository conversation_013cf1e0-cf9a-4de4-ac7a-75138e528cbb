<UserControl x:Class="XIAOFUTools.Tools.NodeDistanceCheck.NodeDistanceCheckDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             mc:Ignorable="d"
             d:DesignHeight="550" d:DesignWidth="450"
             d:DataContext="{Binding Path=NodeDistanceCheckDockPaneViewModel}"
             Loaded="UserControl_Loaded">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 布尔值转可见性转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- 帮助按钮样式 -->
            <Style x:Key="HelpButtonStyle" TargetType="Button" BasedOn="{StaticResource TextButtonStyle}">
                <Setter Property="Width" Value="22"/>
                <Setter Property="Height" Value="22"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="Margin" Value="0"/>
                <Setter Property="HorizontalAlignment" Value="Left"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>
    
    <Grid Margin="12">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 面要素图层选择 -->
        <TextBlock Grid.Row="0" Grid.Column="0" Text="面要素图层:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <Grid Grid.Row="0" Grid.Column="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <ComboBox Grid.Column="0"
                    Style="{StaticResource ComboBoxStyle}"
                    ItemsSource="{Binding PolygonLayers}"
                    SelectedItem="{Binding SelectedPolygonLayer}"
                    DisplayMemberPath="Name"/>
            <Button Grid.Column="1" Width="22" Height="22" Margin="5,0,0,0"
                    Style="{StaticResource DefaultButtonStyle}"
                    Command="{Binding RefreshLayersCommand}"
                    ToolTip="刷新图层列表"
                    VerticalAlignment="Center">
                <TextBlock Text="⟲" FontSize="14" FontWeight="Bold"
                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Button>
        </Grid>

        <!-- 节点检查选项 -->
        <TextBlock Grid.Row="1" Grid.Column="0" Text="节点检查选项:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <ComboBox Grid.Row="1" Grid.Column="1" Margin="0,0,0,10"
                Style="{StaticResource ComboBoxStyle}"
                ItemsSource="{Binding CheckOptions}"
                SelectedItem="{Binding SelectedCheckOption}"/>

        <!-- 节点检查距离 -->
        <TextBlock Grid.Row="2" Grid.Column="0" Text="节点检查距离:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <Grid Grid.Row="2" Grid.Column="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <TextBox Grid.Column="0" Text="{Binding CheckDistance}" VerticalAlignment="Center" Height="22"
                    Style="{StaticResource TextBoxStyle}"/>
            <TextBlock Grid.Column="1" Text="m" VerticalAlignment="Center" Margin="5,0,0,0"/>
        </Grid>

        <!-- 输出线要素图层 -->
        <TextBlock Grid.Row="3" Grid.Column="0" Text="输出线要素图层:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <Grid Grid.Row="3" Grid.Column="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <TextBox Grid.Column="0" Text="{Binding OutputPath}" VerticalAlignment="Center" Height="22"
                    Style="{StaticResource TextBoxStyle}"/>
            <Button Grid.Column="1" Content="浏览..." Height="22" Margin="5,0,0,0" Command="{Binding BrowseOutputCommand}"
                    Style="{StaticResource DefaultButtonStyle}" VerticalAlignment="Center"/>
        </Grid>

        <!-- 保留原始字段 -->
        <TextBlock Grid.Row="4" Grid.Column="0" Text="保留原始字段:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <Grid Grid.Row="4" Grid.Column="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" VerticalAlignment="Center" Height="22" Margin="5,0,0,0"
                      Text="{Binding SelectedFieldsDisplayText}"
                      Foreground="{StaticResource TextSecondaryBrush}"/>
            <Button Grid.Column="1" Content="选择字段..." Height="22" Margin="5,0,0,0" Command="{Binding SelectFieldsCommand}"
                    Style="{StaticResource DefaultButtonStyle}" VerticalAlignment="Center"
                    IsEnabled="{Binding HasSelectedLayer}"/>
        </Grid>



        <!-- 进度条 -->
        <ProgressBar Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,2"
                   Style="{StaticResource ProgressBarStyle}"
                   Value="{Binding Progress}"
                   Minimum="0" Maximum="100"
                   IsIndeterminate="{Binding IsProgressIndeterminate}"
                   Height="6"/>

        <!-- 日志窗口 -->
        <Border Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="2"
               BorderBrush="#CDCDCD" BorderThickness="1"
               Margin="0,0,0,10">
            <TextBox
                  Style="{StaticResource LogTextBoxStyle}"
                  Text="{Binding LogContent, Mode=OneWay}"
                  BorderThickness="0"
                  VerticalAlignment="Stretch"
                  HorizontalAlignment="Stretch"/>
        </Border>

        <!-- 状态消息和按钮区域 -->
        <Grid Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 执行状态 -->
            <TextBlock Grid.Row="0" Text="{Binding StatusMessage}" 
                    TextWrapping="Wrap" VerticalAlignment="Center" Margin="0,0,0,10"/>

            <!-- 按钮区域 -->
            <Border Grid.Row="1" BorderBrush="{StaticResource DividerBrush}"
                   BorderThickness="0,1,0,0"
                   Margin="0,3,0,0"
                   Padding="0,8,0,0">
                <Grid>
                    <Button Content="?" Width="22" Height="22"
                            Style="{StaticResource HelpButtonStyle}"
                            Command="{Binding ShowHelpCommand}"
                            ToolTip="查看工具使用说明"
                            HorizontalAlignment="Left"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <!-- 只在处理时显示停止按钮 -->
                        <Button Content="停止" Width="80" Command="{Binding CancelCommand}" Margin="0,0,10,0"
                                Style="{StaticResource CancelButtonStyle}"
                                Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}"
                                IsEnabled="{Binding IsProcessing}"/>
                        <Button Content="开始" Width="80" Command="{Binding RunCommand}"
                                Style="{StaticResource ExecuteButtonStyle}"
                                IsEnabled="{Binding CanProcess}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>