using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Text;
using ArcGIS.Core.CIM;
using ArcGIS.Core.Data;
using ArcGIS.Core.Geometry;
using ArcGIS.Desktop.Catalog;
using ArcGIS.Desktop.Core;
using ArcGIS.Desktop.Editing;
using ArcGIS.Desktop.Extensions;
using ArcGIS.Desktop.Framework;
using ArcGIS.Desktop.Framework.Contracts;
using ArcGIS.Desktop.Framework.Dialogs;
using ArcGIS.Desktop.Framework.Threading.Tasks;
using ArcGIS.Desktop.Layouts;
using ArcGIS.Desktop.Mapping;
using ArcGIS.Desktop.Core.Geoprocessing;

namespace XIAOFUTools.Tools.BoundaryPointGenerator
{
    /// <summary>
    /// 生成四至坐标点DockPane视图模型
    /// </summary>
    internal class BoundaryPointGeneratorDockPaneViewModel : PropertyChangedBase
    {
        #region 属性

        // 取消操作标志
        private bool _cancelRequested = false;
        public bool CancelRequested
        {
            get => _cancelRequested;
            set
            {
                SetProperty(ref _cancelRequested, value);
            }
        }
        
        // 是否正在处理
        private bool _isProcessing = false;
        public bool IsProcessing
        {
            get => _isProcessing;
            set
            {
                SetProperty(ref _isProcessing, value);
                NotifyPropertyChanged(() => CanProcess);
            }
        }
        
        // 是否可以处理
        public bool CanProcess => !IsProcessing;

        // 面图层列表
        private ObservableCollection<FeatureLayer> _polygonLayers;
        public ObservableCollection<FeatureLayer> PolygonLayers
        {
            get => _polygonLayers;
            set
            {
                SetProperty(ref _polygonLayers, value);
            }
        }

        // 选中的面图层
        private FeatureLayer _selectedPolygonLayer;
        public FeatureLayer SelectedPolygonLayer
        {
            get => _selectedPolygonLayer;
            set
            {
                SetProperty(ref _selectedPolygonLayer, value);
                NotifyPropertyChanged(() => HasSelectedLayer);
                // 当选择图层改变时，自动更新输出路径
                UpdateOutputPath();
            }
        }

        // 是否有选中图层
        public bool HasSelectedLayer => SelectedPolygonLayer != null;

        // 输出路径
        private string _outputPath;
        public string OutputPath
        {
            get => _outputPath;
            set
            {
                SetProperty(ref _outputPath, value);
            }
        }

        // 状态信息
        private string _statusMessage;
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                SetProperty(ref _statusMessage, value);
            }
        }

        // 日志内容
        private string _logContent;
        public string LogContent
        {
            get => _logContent;
            set
            {
                SetProperty(ref _logContent, value);
            }
        }

        // 日志构建器
        private StringBuilder _logBuilder = new StringBuilder();

        // 进度
        private int _progress;
        public int Progress
        {
            get => _progress;
            set
            {
                SetProperty(ref _progress, value);
            }
        }

        // 是否为不确定进度
        private bool _isProgressIndeterminate;
        public bool IsProgressIndeterminate
        {
            get => _isProgressIndeterminate;
            set
            {
                SetProperty(ref _isProgressIndeterminate, value);
            }
        }

        // 选中的保留字段
        private List<string> _selectedFields = new List<string>();
        public List<string> SelectedFields
        {
            get => _selectedFields;
            set
            {
                _selectedFields = value ?? new List<string>();
                SetProperty(ref _selectedFields, value);
                NotifyPropertyChanged(() => SelectedFieldsDisplayText);
            }
        }

        // 选中字段的显示文本
        public string SelectedFieldsDisplayText
        {
            get
            {
                if (SelectedFields == null || SelectedFields.Count == 0)
                {
                    return "未选择字段";
                }
                return $"已选择 {SelectedFields.Count} 个字段";
            }
        }

        #endregion

        #region 命令

        // 浏览输出路径命令
        private ICommand _browseOutputCommand;
        public ICommand BrowseOutputCommand
        {
            get
            {
                return _browseOutputCommand ?? (_browseOutputCommand = new RelayCommand(() =>
                {
                    try
                    {
                        // 使用ArcGIS Pro的SaveItemDialog选择输出位置
                        var saveItemDialog = new SaveItemDialog
                        {
                            Title = "选择输出位置",
                            OverwritePrompt = true,
                            DefaultExt = "shp",
                            Filter = ItemFilters.FeatureClasses_All
                        };

                        // 设置初始位置
                        var initialLocation = GetProjectGDBPath();
                        if (!string.IsNullOrEmpty(initialLocation))
                        {
                            saveItemDialog.InitialLocation = initialLocation;
                        }

                        // 显示对话框并获取结果
                        bool? dialogResult = saveItemDialog.ShowDialog();

                        if (dialogResult == true)
                        {
                            // 获取选中的路径
                            OutputPath = saveItemDialog.FilePath;
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"选择输出位置出错: {ex.Message}", "错误");
                    }
                }));
            }
        }

        // 取消命令（只用于停止处理）
        private ICommand _cancelCommand;
        public ICommand CancelCommand
        {
            get
            {
                return _cancelCommand ?? (_cancelCommand = new RelayCommand(() =>
                {
                    if (IsProcessing)
                    {
                        // 如果正在处理，则设置取消标志
                        CancelRequested = true;
                        StatusMessage = "正在取消操作...";
                        LogWarning("用户请求取消操作");
                    }
                }, () => IsProcessing));
            }
        }

        // 运行命令
        private ICommand _runCommand;
        public ICommand RunCommand
        {
            get
            {
                return _runCommand ?? (_runCommand = new RelayCommand(Execute, () => CanExecute()));
            }
        }
        
        // 帮助命令
        private ICommand _showHelpCommand;
        public ICommand ShowHelpCommand
        {
            get
            {
                return _showHelpCommand ?? (_showHelpCommand = new RelayCommand(() => ShowHelp()));
            }
        }

        // 选择字段命令
        private ICommand _selectFieldsCommand;
        public ICommand SelectFieldsCommand
        {
            get
            {
                return _selectFieldsCommand ?? (_selectFieldsCommand = new RelayCommand(() => SelectFields(), () => HasSelectedLayer));
            }
        }

        // 刷新图层命令
        private ICommand _refreshLayersCommand;
        public ICommand RefreshLayersCommand
        {
            get
            {
                return _refreshLayersCommand ?? (_refreshLayersCommand = new RelayCommand(() => RefreshLayers()));
            }
        }

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public BoundaryPointGeneratorDockPaneViewModel()
        {
            // 初始化属性
            PolygonLayers = new ObservableCollection<FeatureLayer>();

            // 设置默认输出路径为工程数据库
            UpdateOutputPath();

            StatusMessage = "请选择面图层。";
            LogContent = "";
            Progress = 0;
            IsProgressIndeterminate = false;

            // 加载面图层
            LoadPolygonLayers();
        }

        /// <summary>
        /// 刷新图层列表（供DockPane调用）
        /// </summary>
        public void RefreshLayers()
        {
            LoadPolygonLayers();
        }
        
        /// <summary>
        /// 获取当前项目地理数据库路径
        /// </summary>
        private string GetProjectGDBPath()
        {
            try
            {
                var project = Project.Current;
                if (project != null)
                {
                    return project.DefaultGeodatabasePath;
                }
                return Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取项目地理数据库路径失败: {ex.Message}");
                return Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            }
        }

        /// <summary>
        /// 获取地理处理工具的字段类型
        /// </summary>
        private string GetGeoprocessingFieldType(FieldType fieldType)
        {
            return fieldType switch
            {
                FieldType.String => "TEXT",
                FieldType.Integer => "LONG",
                FieldType.SmallInteger => "SHORT",
                FieldType.Double => "DOUBLE",
                FieldType.Single => "FLOAT",
                FieldType.Date => "DATE",
                FieldType.GUID => "GUID",
                FieldType.GlobalID => "GUID",
                _ => "TEXT"
            };
        }

        /// <summary>
        /// 更新输出路径，格式为：A+源图层名称_SZZB
        /// </summary>
        private void UpdateOutputPath()
        {
            string projectGDB = GetProjectGDBPath();
            string outputName;

            if (SelectedPolygonLayer != null)
            {
                // 格式：A+源图层名称_SZZB
                outputName = $"A{SelectedPolygonLayer.Name}_SZZB";
            }
            else
            {
                // 默认名称
                outputName = "A四至坐标点_SZZB";
            }

            if (!string.IsNullOrEmpty(projectGDB))
            {
                OutputPath = Path.Combine(projectGDB, outputName);
            }
            else
            {
                OutputPath = outputName;
            }
        }

        /// <summary>
        /// 加载面图层
        /// </summary>
        private void LoadPolygonLayers()
        {
            QueuedTask.Run(() =>
            {
                try
                {
                    // 获取所有面图层的临时列表
                    var tempLayers = new List<FeatureLayer>();
                    var map = MapView.Active?.Map;

                    if (map != null)
                    {
                        var layers = map.GetLayersAsFlattenedList().OfType<FeatureLayer>().ToList();

                        // 只添加面图层
                        foreach (var layer in layers)
                        {
                            try
                            {
                                using (var table = layer.GetTable())
                                {
                                    if (table != null)
                                    {
                                        var definition = table.GetDefinition() as FeatureClassDefinition;
                                        if (definition?.GetShapeType() == GeometryType.Polygon)
                                        {
                                            tempLayers.Add(layer);
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                // 忽略无法访问的图层
                                LogError($"无法访问图层 {layer.Name}: {ex.Message}");
                            }
                        }
                    }

                    // 在UI线程更新图层列表
                    if (System.Windows.Application.Current?.Dispatcher != null)
                    {
                        System.Windows.Application.Current.Dispatcher.Invoke(() =>
                        {
                            // 清空图层列表
                            PolygonLayers?.Clear();

                            // 添加图层
                            if (PolygonLayers != null)
                            {
                                foreach (var layer in tempLayers)
                                {
                                    PolygonLayers.Add(layer);
                                }

                                // 如果有图层，默认选择第一个
                                if (PolygonLayers.Count > 0)
                                {
                                    SelectedPolygonLayer = PolygonLayers[0];
                                }
                            }
                        });
                    }
                }
                catch (Exception ex)
                {
                    // 确保在UI线程显示错误信息
                    if (System.Windows.Application.Current?.Dispatcher != null)
                    {
                        System.Windows.Application.Current.Dispatcher.Invoke(() =>
                        {
                            StatusMessage = $"加载图层出错: {ex.Message}";
                        });
                    }
                }
            });
        }

        /// <summary>
        /// 确定命令是否可以执行
        /// </summary>
        private bool CanExecute()
        {
            return SelectedPolygonLayer != null && !string.IsNullOrEmpty(OutputPath) && !IsProcessing;
        }

        /// <summary>
        /// 执行生成四至坐标点操作
        /// </summary>
        private async void Execute()
        {
            // 防止重复执行
            if (IsProcessing)
            {
                LogWarning("工具正在运行中，请等待完成后再次执行");
                return;
            }

            // 重置取消标志
            CancelRequested = false;
            // 设置处理状态
            IsProcessing = true;

            StatusMessage = "正在处理...";
            ClearLog();
            // 重置进度条
            Progress = 0;
            IsProgressIndeterminate = true;

            try
            {
                LogInfo("开始生成四至坐标点...");

                await QueuedTask.Run(async () =>
                {
                    try
                    {
                        // 检查取消请求
                        if (CancelRequested)
                        {
                            LogWarning("操作已取消");
                            return;
                        }

                        // 创建输出要素类
                        var outputFeatureClassPath = await CreateOutputFeatureClass();
                        if (outputFeatureClassPath == null)
                        {
                            LogError("创建输出要素类失败");
                            return;
                        }

                        LogInfo($"成功创建输出要素类: {outputFeatureClassPath}");

                        // 处理面要素，生成四至坐标点
                        await ProcessPolygonFeatures(outputFeatureClassPath);

                        if (!CancelRequested)
                        {
                            LogInfo("四至坐标点生成完成！");

                            // 在UI线程更新状态
                            if (System.Windows.Application.Current?.Dispatcher != null)
                            {
                                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                                {
                                    StatusMessage = "处理完成！";
                                    Progress = 100;
                                    IsProgressIndeterminate = false;
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogError($"处理过程中发生错误: {ex.Message}");
                        if (System.Windows.Application.Current?.Dispatcher != null)
                        {
                            System.Windows.Application.Current.Dispatcher.Invoke(() =>
                            {
                                StatusMessage = $"处理失败: {ex.Message}";
                            });
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                LogError($"执行失败: {ex.Message}");
                StatusMessage = $"执行失败: {ex.Message}";
            }
            finally
            {
                // 重置处理状态
                IsProcessing = false;
                IsProgressIndeterminate = false;
            }
        }

        /// <summary>
        /// 创建输出要素类
        /// </summary>
        private async Task<string> CreateOutputFeatureClass()
        {
            try
            {
                // 使用地理处理工具创建要素类
                string outputPath = OutputPath;
                if (string.IsNullOrEmpty(outputPath))
                {
                    LogError("输出路径为空");
                    return null;
                }

                // 获取输入图层的空间参考
                SpatialReference spatialReference = null;
                if (SelectedPolygonLayer != null)
                {
                    try
                    {
                        using (var table = SelectedPolygonLayer.GetTable())
                        {
                            if (table != null)
                            {
                                var definition = table.GetDefinition() as FeatureClassDefinition;
                                spatialReference = definition?.GetSpatialReference();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogError($"获取空间参考失败: {ex.Message}");
                        throw;
                    }
                }

                // 确保输出目录存在
                string outputDirectory = Path.GetDirectoryName(outputPath);
                if (string.IsNullOrEmpty(outputDirectory))
                {
                    LogError("无法确定输出目录");
                    return null;
                }

                string featureClassName = Path.GetFileNameWithoutExtension(outputPath);

                // 如果要素类已存在，先删除
                try
                {
                    var deleteParams = Geoprocessing.MakeValueArray(Path.Combine(outputDirectory, featureClassName));
                    await Geoprocessing.ExecuteToolAsync("Delete_management", deleteParams);
                    LogInfo($"删除已存在的要素类: {featureClassName}");
                }
                catch
                {
                    // 如果删除失败（可能是因为不存在），继续执行
                }

                // 使用CreateFeatureclass地理处理工具
                var parameters = Geoprocessing.MakeValueArray(
                    outputDirectory, // 输出工作空间
                    featureClassName, // 要素类名称
                    "POINT", // 几何类型
                    null, // 模板
                    "DISABLED", // 是否有M值
                    "DISABLED", // 是否有Z值
                    spatialReference // 空间参考
                );

                var result = await Geoprocessing.ExecuteToolAsync("CreateFeatureclass_management", parameters);

                if (result.IsFailed)
                {
                    LogError($"创建要素类失败: {string.Join(", ", result.Messages.Select(m => m.Text))}");
                    return null;
                }

                // 添加字段
                string featureClassPath = result.ReturnValue;

                // 添加源要素ID字段
                var addFieldParams0 = Geoprocessing.MakeValueArray(
                    featureClassPath,
                    "源要素ID",
                    "LONG"
                );
                await Geoprocessing.ExecuteToolAsync("AddField_management", addFieldParams0);

                // 添加方向字段
                var addFieldParams1 = Geoprocessing.MakeValueArray(
                    featureClassPath,
                    "方向",
                    "TEXT",
                    null, null, 10
                );
                await Geoprocessing.ExecuteToolAsync("AddField_management", addFieldParams1);

                // 添加X坐标字段
                var addFieldParams2 = Geoprocessing.MakeValueArray(
                    featureClassPath,
                    "X坐标_米",
                    "DOUBLE"
                );
                await Geoprocessing.ExecuteToolAsync("AddField_management", addFieldParams2);

                // 添加Y坐标字段
                var addFieldParams3 = Geoprocessing.MakeValueArray(
                    featureClassPath,
                    "Y坐标_米",
                    "DOUBLE"
                );
                await Geoprocessing.ExecuteToolAsync("AddField_management", addFieldParams3);

                // 添加选中的保留字段
                if (SelectedFields != null && SelectedFields.Count > 0)
                {
                    try
                    {
                        using (var table = SelectedPolygonLayer.GetTable())
                        {
                            if (table != null)
                            {
                                var definition = table.GetDefinition();
                                var sourceFields = definition.GetFields();

                                foreach (var fieldName in SelectedFields)
                                {
                                    var sourceField = sourceFields.FirstOrDefault(f => f.Name == fieldName);
                                    if (sourceField != null)
                                    {
                                        var fieldType = GetGeoprocessingFieldType(sourceField.FieldType);
                                        var addFieldParams = Geoprocessing.MakeValueArray(
                                            featureClassPath,
                                            sourceField.Name,
                                            fieldType,
                                            null, null, sourceField.Length > 0 ? sourceField.Length : (object)null
                                        );
                                        await Geoprocessing.ExecuteToolAsync("AddField_management", addFieldParams);
                                        LogInfo($"添加保留字段: {sourceField.Name} ({fieldType})");
                                    }
                                }
                            }
                            else
                            {
                                LogError("无法获取图层表格");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogError($"添加保留字段失败: {ex.Message}");
                        throw;
                    }
                }

                return featureClassPath;
            }
            catch (Exception ex)
            {
                LogError($"创建输出要素类时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 处理面要素，生成四至坐标点
        /// </summary>
        private async Task ProcessPolygonFeatures(string outputFeatureClassPath)
        {
            try
            {
                using (var inputTable = SelectedPolygonLayer.GetTable())
                {
                    if (inputTable == null)
                    {
                        LogError("无法获取输入图层的表格");
                        return;
                    }
                    // 收集所有要创建的点
                    var pointsToCreate = new List<(MapPoint point, Dictionary<string, object> attributes)>();

                    int totalFeatures = 0;
                    int processedFeatures = 0;

                    // 先计算总数
                    using (var countCursor = inputTable.Search())
                    {
                        while (countCursor.MoveNext())
                        {
                            totalFeatures++;
                        }
                    }

                    LogInfo($"共找到 {totalFeatures} 个面要素");

                    // 处理每个要素
                    using (var cursor = inputTable.Search())
                    {
                        while (cursor.MoveNext())
                        {
                            if (CancelRequested)
                            {
                                LogWarning("操作已取消");
                                break;
                            }

                            var feature = cursor.Current as Feature;
                            if (feature != null)
                            {
                                // 获取面几何和要素ID
                                var polygon = feature.GetShape() as Polygon;
                                var featureOID = feature.GetObjectID();

                                LogInfo($"正在处理面要素 {featureOID}");

                                if (polygon != null)
                                {
                                    LogInfo($"面要素 {featureOID} 的几何信息: PartCount={polygon.PartCount}, Area={polygon.Area:F2}");

                                    // 生成四至坐标点
                                    var boundaryPoints = GenerateBoundaryPoints(polygon);

                                    LogInfo($"面要素 {featureOID} 生成了 {boundaryPoints.Count} 个四至点");

                                    // 收集点要素数据
                                    foreach (var point in boundaryPoints)
                                    {
                                        var attributes = new Dictionary<string, object>
                                        {
                                            ["源要素ID"] = featureOID,
                                            ["方向"] = point.Direction,
                                            ["X坐标_米"] = point.Point.X,
                                            ["Y坐标_米"] = point.Point.Y
                                        };

                                        // 添加保留字段的值
                                        if (SelectedFields != null && SelectedFields.Count > 0)
                                        {
                                            foreach (var fieldName in SelectedFields)
                                            {
                                                try
                                                {
                                                    var fieldValue = feature[fieldName];
                                                    attributes[fieldName] = fieldValue;
                                                }
                                                catch (Exception ex)
                                                {
                                                    LogWarning($"获取字段 {fieldName} 的值失败: {ex.Message}");
                                                    attributes[fieldName] = null;
                                                }
                                            }
                                        }

                                        pointsToCreate.Add((point.Point, attributes));
                                        LogInfo($"  - {point.Direction}点: ({point.Point.X:F2}, {point.Point.Y:F2})");
                                    }
                                }
                                else
                                {
                                    LogWarning($"面要素 {featureOID} 的几何为空");
                                }
                            }
                            else
                            {
                                LogWarning($"第 {processedFeatures + 1} 个要素为空");
                            }

                            processedFeatures++;

                            // 更新进度
                            int progressValue = (int)((double)processedFeatures / totalFeatures * 100);
                            if (System.Windows.Application.Current?.Dispatcher != null)
                            {
                                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                                {
                                    Progress = progressValue;
                                    IsProgressIndeterminate = false;
                                });
                            }

                            LogInfo($"已处理 {processedFeatures}/{totalFeatures} 个要素");
                        }
                    }

                    // 使用地理处理工具插入点
                    if (!CancelRequested && pointsToCreate.Count > 0)
                    {
                        LogInfo($"正在插入 {pointsToCreate.Count} 个点要素...");
                        await InsertPointFeatures(outputFeatureClassPath, pointsToCreate);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"处理面要素时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 插入点要素
        /// </summary>
        private async Task InsertPointFeatures(string outputFeatureClassPath, List<(MapPoint point, Dictionary<string, object> attributes)> pointsToCreate)
        {
            try
            {
                await QueuedTask.Run(() =>
                {
                    // 打开输出要素类
                    var workspace = Path.GetDirectoryName(outputFeatureClassPath);
                    var featureClassName = Path.GetFileNameWithoutExtension(outputFeatureClassPath);

                    if (string.IsNullOrEmpty(workspace) || string.IsNullOrEmpty(featureClassName))
                    {
                        LogError("无效的输出要素类路径");
                        return;
                    }

                    using (var geodatabase = new Geodatabase(new FileGeodatabaseConnectionPath(new Uri(workspace))))
                    using (var featureClass = geodatabase.OpenDataset<FeatureClass>(featureClassName))
                    {
                        int insertedCount = 0;
                        
                        foreach (var pointData in pointsToCreate)
                        {
                            if (CancelRequested) break;

                            try
                            {
                                using (var rowBuffer = featureClass.CreateRowBuffer())
                                {
                                    // 设置几何
                                    rowBuffer[featureClass.GetDefinition().GetShapeField()] = pointData.point;

                                    // 设置属性
                                    foreach (var attr in pointData.attributes)
                                    {
                                        try
                                        {
                                            rowBuffer[attr.Key] = attr.Value;
                                        }
                                        catch (Exception ex)
                                        {
                                            LogWarning($"设置字段 {attr.Key} 的值失败: {ex.Message}");
                                        }
                                    }

                                    using (var feature = featureClass.CreateRow(rowBuffer))
                                    {
                                        feature.Store();
                                        insertedCount++;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                LogError($"插入点要素失败: {ex.Message}");
                            }
                        }

                        LogInfo($"成功插入 {insertedCount} 个点要素");
                    }
                });
            }
            catch (Exception ex)
            {
                LogError($"插入点要素时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成面要素的四至坐标点（基于面折点的最东西南北点）
        /// </summary>
        private List<BoundaryPoint> GenerateBoundaryPoints(Polygon polygon)
        {
            var points = new List<BoundaryPoint>();

            try
            {
                // 获取面的所有折点
                var allPoints = new List<MapPoint>();

                // 遍历所有环（外环和内环）
                for (int partIndex = 0; partIndex < polygon.PartCount; partIndex++)
                {
                    var part = polygon.Parts[partIndex];

                    // 遍历每个线段的端点
                    foreach (var segment in part)
                    {
                        allPoints.Add(segment.StartPoint);
                        allPoints.Add(segment.EndPoint);
                    }
                }

                if (allPoints.Count == 0)
                {
                    LogWarning("面要素没有找到折点");
                    return points;
                }

                // 去除重复点（使用坐标比较）
                var uniquePoints = new List<MapPoint>();
                foreach (var point in allPoints)
                {
                    bool isDuplicate = false;
                    foreach (var existingPoint in uniquePoints)
                    {
                        if (Math.Abs(point.X - existingPoint.X) < 0.0001 &&
                            Math.Abs(point.Y - existingPoint.Y) < 0.0001)
                        {
                            isDuplicate = true;
                            break;
                        }
                    }
                    if (!isDuplicate)
                    {
                        uniquePoints.Add(point);
                    }
                }

                if (uniquePoints.Count == 0)
                {
                    LogWarning("去重后没有有效的折点");
                    return points;
                }

                // 找到最东、最西、最南、最北的折点
                MapPoint eastPoint = uniquePoints[0];   // 最大X坐标
                MapPoint westPoint = uniquePoints[0];   // 最小X坐标
                MapPoint southPoint = uniquePoints[0];  // 最小Y坐标
                MapPoint northPoint = uniquePoints[0];  // 最大Y坐标

                foreach (var point in uniquePoints)
                {
                    // 最东点（最大X坐标）
                    if (point.X > eastPoint.X)
                    {
                        eastPoint = point;
                    }

                    // 最西点（最小X坐标）
                    if (point.X < westPoint.X)
                    {
                        westPoint = point;
                    }

                    // 最南点（最小Y坐标）
                    if (point.Y < southPoint.Y)
                    {
                        southPoint = point;
                    }

                    // 最北点（最大Y坐标）
                    if (point.Y > northPoint.Y)
                    {
                        northPoint = point;
                    }
                }

                // 添加四至坐标点
                points.Add(new BoundaryPoint { Point = eastPoint, Direction = "东" });
                points.Add(new BoundaryPoint { Point = westPoint, Direction = "西" });
                points.Add(new BoundaryPoint { Point = southPoint, Direction = "南" });
                points.Add(new BoundaryPoint { Point = northPoint, Direction = "北" });
            }
            catch (Exception ex)
            {
                LogError($"生成四至坐标点时发生错误: {ex.Message}");
            }

            return points;
        }

        #region 日志方法

        /// <summary>
        /// 清除日志
        /// </summary>
        private void ClearLog()
        {
            _logBuilder.Clear();
            LogContent = "";
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        private void LogInfo(string message)
        {
            var logMessage = $"[{DateTime.Now:HH:mm:ss}] {message}";
            _logBuilder.AppendLine(logMessage);

            if (System.Windows.Application.Current?.Dispatcher != null)
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    LogContent = _logBuilder.ToString();
                });
            }
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        private void LogWarning(string message)
        {
            var logMessage = $"[{DateTime.Now:HH:mm:ss}] 警告: {message}";
            _logBuilder.AppendLine(logMessage);

            if (System.Windows.Application.Current?.Dispatcher != null)
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    LogContent = _logBuilder.ToString();
                });
            }
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        private void LogError(string message)
        {
            var logMessage = $"[{DateTime.Now:HH:mm:ss}] 错误: {message}";
            _logBuilder.AppendLine(logMessage);

            if (System.Windows.Application.Current?.Dispatcher != null)
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    LogContent = _logBuilder.ToString();
                });
            }
        }

        #endregion

        /// <summary>
        /// 选择保留字段
        /// </summary>
        private void SelectFields()
        {
            if (SelectedPolygonLayer == null) return;

            try
            {
                QueuedTask.Run(() =>
                {
                    try
                    {
                        using (var table = SelectedPolygonLayer.GetTable())
                        {
                            if (table != null)
                            {
                                var definition = table.GetDefinition();
                                var fields = definition.GetFields().ToList();

                                // 在UI线程显示对话框
                                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                                {
                                    var dialog = new FieldSelectionDialog(fields, SelectedFields);
                                    if (dialog.ShowDialog() == true)
                                    {
                                        SelectedFields = dialog.SelectedFieldNames;
                                        LogInfo($"已选择 {SelectedFields.Count} 个保留字段");
                                    }
                                });
                            }
                            else
                            {
                                LogError("无法获取图层表格");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogError($"获取字段列表失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                LogError($"获取字段列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示帮助信息
        /// </summary>
        private void ShowHelp()
        {
            var helpContent = "生成四至坐标点工具使用说明\n\n" +
                "功能描述：\n" +
                "根据输入的面要素生成四至坐标点（最东、最西、最南、最北点）。\n\n" +
                "参数说明：\n" +
                "• 面图层：选择要处理的面要素图层\n" +
                "• 输出四至点图层：指定输出点要素的位置和名称\n" +
                "• 保留原始字段：选择要从源图层复制到输出图层的字段\n\n" +
                "操作步骤：\n" +
                "1. 选择要处理的面图层\n" +
                "2. 设置输出四至点图层路径（默认输出到工程数据库）\n" +
                "3. 可选：点击\"选择字段...\"选择要保留的原始字段\n" +
                "4. 点击\"开始\"按钮执行处理\n" +
                "5. 处理过程中可点击\"停止\"按钮取消操作\n\n" +
                "输出结果：\n" +
                "生成的点要素包含以下字段：\n" +
                "• 源要素ID：源面要素的ObjectID\n" +
                "• 方向：标识点的方位（东、西、南、北）\n" +
                "• X坐标_米：点的X坐标值\n" +
                "• Y坐标_米：点的Y坐标值\n" +
                "• 选中的原始字段：从源图层复制的字段值\n\n" +
                "注意事项：\n" +
                "• 输出到文件夹时将创建Shapefile格式\n" +
                "• 输出到地理数据库时将创建要素类\n" +
                "• 四至点基于面要素本身折点的最东西南北坐标计算\n" +
                "• 每个面要素将生成4个点（东、西、南、北各一个）\n" +
                "• 保留字段的值会复制到每个生成的四至点";

            MessageBox.Show(helpContent, "帮助", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
    }

    /// <summary>
    /// 四至坐标点数据结构
    /// </summary>
    internal class BoundaryPoint
    {
        public MapPoint Point { get; set; }
        public string Direction { get; set; }
    }
}
