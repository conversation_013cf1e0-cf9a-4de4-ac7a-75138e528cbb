<UserControl x:Class="XIAOFUTools.Tools.BatchGeometryRepair.BatchGeometryRepairDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.BatchGeometryRepair"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="400"
             d:DataContext="{Binding Path=BatchGeometryRepairViewModel}"
             Loaded="UserControl_Loaded">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <!-- 图层列表操作按钮 -->
            <RowDefinition Height="Auto"/>
            <!-- 图层列表 -->
            <RowDefinition Height="*"/>
            <!-- 进度条 -->
            <RowDefinition Height="Auto"/>
            <!-- 日志区域 -->
            <RowDefinition Height="120"/>
            <!-- 按钮区域 -->
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 图层列表操作按钮 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
            <Button Content="全选" Command="{Binding SelectAllCommand}"
                   Style="{StaticResource DefaultButtonStyle}"
                   Width="60" Height="22" Margin="0,0,10,0"/>
            <Button Content="反选" Command="{Binding SelectNoneCommand}"
                   Style="{StaticResource DefaultButtonStyle}"
                   Width="60" Height="22" Margin="0,0,10,0"/>
            <Button Content="⟲" Width="22" Height="22" Margin="0,0,0,0"
                   Style="{StaticResource DefaultButtonStyle}"
                   Command="{Binding RefreshLayersCommand}"
                   ToolTip="刷新图层列表"
                   FontSize="14" FontWeight="Bold"/>
        </StackPanel>

        <!-- 图层列表 -->
        <Border Grid.Row="1" BorderBrush="#CDCDCD" BorderThickness="1" Margin="0,0,0,10">
            <DataGrid ItemsSource="{Binding LayerList}"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     CanUserReorderColumns="False"
                     CanUserResizeRows="False"
                     CanUserSortColumns="True"
                     GridLinesVisibility="All"
                     HeadersVisibility="Column"
                     SelectionMode="Extended"
                     BorderThickness="0">
                    <DataGrid.Columns>
                        <!-- 选择列 -->
                        <DataGridTemplateColumn Header="选择" Width="50">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                             HorizontalAlignment="Center"
                                             Checked="CheckBox_Checked"
                                             Unchecked="CheckBox_Unchecked"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- 图层名称列 -->
                        <DataGridTextColumn Header="图层名称" Binding="{Binding LayerName}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="TextAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <!-- 类型列 -->
                        <DataGridTextColumn Header="类型" Binding="{Binding LayerType}" Width="80" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="TextAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <!-- 坐标系列 -->
                        <DataGridTextColumn Header="坐标系" Binding="{Binding CoordinateSystem}" Width="120" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="TextAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>

        <!-- 进度条 -->
        <ProgressBar Grid.Row="2" Margin="0,0,0,2"
                   Style="{StaticResource ProgressBarStyle}"
                   Value="{Binding Progress}"
                   Minimum="0" Maximum="100"
                   IsIndeterminate="{Binding IsProgressIndeterminate}"
                   Height="6"/>

        <!-- 日志区域 -->
        <Border Grid.Row="3" BorderBrush="#CDCDCD" BorderThickness="1" Margin="0,0,0,10" Height="120">
            <TextBox Style="{StaticResource LogTextBoxStyle}"
                    Text="{Binding LogContent, Mode=OneWay}"
                    BorderThickness="0"
                    VerticalAlignment="Stretch"
                    HorizontalAlignment="Stretch"/>
        </Border>

        <!-- 状态消息和按钮区域 -->
        <Grid Grid.Row="4" Margin="0,0,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 执行状态 -->
            <TextBlock Grid.Row="0" Text="{Binding StatusMessage}"
                    TextWrapping="Wrap" VerticalAlignment="Center" Margin="0,0,0,10"/>

            <!-- 按钮区域 -->
            <Border Grid.Row="1" BorderBrush="{StaticResource DividerBrush}"
                   BorderThickness="0,1,0,0"
                   Margin="0,5,0,0"
                   Padding="0,10,0,0">
                <Grid>
                    <Button Content="?" Width="22" Height="22"
                            Style="{StaticResource HelpButtonStyle}"
                            Command="{Binding ShowHelpCommand}"
                            ToolTip="查看工具使用说明"
                            HorizontalAlignment="Left"/>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <!-- 只在处理时显示停止按钮 -->
                        <Button Content="停止" Width="80" Command="{Binding CancelCommand}" Margin="0,0,10,0"
                                Style="{StaticResource CancelButtonStyle}"
                                Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}"
                                IsEnabled="{Binding IsProcessing}"/>
                        <Button Content="开始" Width="80" Command="{Binding RunCommand}"
                                Style="{StaticResource ExecuteButtonStyle}"
                                IsEnabled="{Binding CanProcess}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
