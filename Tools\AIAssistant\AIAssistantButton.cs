using System;
using ArcGIS.Desktop.Framework.Contracts;
using XIAOFUTools.Tools.Authorization;

namespace XIAOFUTools.Tools.AIAssistant
{
    /// <summary>
    /// AI助手按钮
    /// </summary>
    internal class AIAssistantButton : Button
    {
        protected override void OnClick()
        {
            try
            {
                // 检查授权
                if (!AuthorizationChecker.CheckAuthorizationWithPrompt("AI助手工具"))
                {
                    return;
                }

                // 打开AI助手停靠窗格
                AIAssistantDockPane.Show();
            }
            catch (Exception ex)
            {
                ArcGIS.Desktop.Framework.Dialogs.MessageBox.Show($"打开AI助手窗格时出错: {ex.Message}", "错误");
            }
        }
    }
}
