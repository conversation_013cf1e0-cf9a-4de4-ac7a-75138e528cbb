using System;
using System.Text;
using XIAOFUTools.Tools.AIAssistant.Models;

namespace XIAOFUTools.Tools.AIAssistant.Prompts
{
    /// <summary>
    /// 提示词管理器
    /// 负责根据不同场景生成最适合的提示词组合
    /// </summary>
    public class PromptManager
    {
        private static PromptManager _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// 单例实例
        /// </summary>
        public static PromptManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new PromptManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private PromptManager() { }

        /// <summary>
        /// 生成Augment风格的智能系统提示词
        /// </summary>
        /// <param name="mode">模式：chat 或 agent</param>
        /// <param name="gisContext">GIS上下文信息</param>
        /// <param name="userMessage">用户消息</param>
        /// <returns>完整的智能系统提示词</returns>
        public string GenerateSystemPrompt(
            string mode,
            GISContext gisContext = null,
            string userMessage = null)
        {
            // 格式化GIS上下文
            var contextString = gisContext != null ? FormatGISContext(gisContext) : null;

            // 使用Augment风格的上下文感知提示词
            var intelligentPrompt = AugmentStylePrompts.GetContextAwarePrompt(mode, contextString, userMessage);

            var promptBuilder = new StringBuilder();
            promptBuilder.AppendLine(intelligentPrompt);

            // 为Agent模式添加额外的智能指导
            if (mode?.ToLower() == "agent")
            {
                promptBuilder.AppendLine();
                promptBuilder.AppendLine(AugmentStylePrompts.TaskAnalysisPrompt);
                promptBuilder.AppendLine();
                promptBuilder.AppendLine(AugmentStylePrompts.ErrorRecoveryPrompt);
                promptBuilder.AppendLine();
                promptBuilder.AppendLine(AugmentStylePrompts.ProactiveSuggestionsPrompt);
            }

            return promptBuilder.ToString();
        }

        /// <summary>
        /// 格式化GIS上下文信息
        /// </summary>
        /// <param name="context">GIS上下文</param>
        /// <returns>格式化的上下文字符串</returns>
        private string FormatGISContext(GISContext context)
        {
            var contextBuilder = new StringBuilder();

            // 基本地图信息
            contextBuilder.AppendLine($"**当前地图**: {context.MapName ?? "未知"}");
            contextBuilder.AppendLine($"**坐标系**: {context.SpatialReference ?? "未知"}");
            contextBuilder.AppendLine($"**比例尺**: 1:{context.Scale:N0}");

            // 地图范围
            if (context.CurrentExtent != null)
            {
                contextBuilder.AppendLine($"**地图范围**: X({context.CurrentExtent.XMin:F2}, {context.CurrentExtent.XMax:F2}), Y({context.CurrentExtent.YMin:F2}, {context.CurrentExtent.YMax:F2})");
            }

            // 图层信息
            if (context.ActiveLayers != null && context.ActiveLayers.Count > 0)
            {
                contextBuilder.AppendLine($"**活动图层数量**: {context.ActiveLayers.Count}");
                contextBuilder.AppendLine("**图层列表**:");
                
                for (int i = 0; i < Math.Min(context.ActiveLayers.Count, 10); i++) // 最多显示10个图层
                {
                    var layer = context.ActiveLayers[i];
                    var visibility = layer.IsVisible ? "👁️" : "🚫";
                    var editable = layer.IsEditable ? "✏️" : "🔒";
                    contextBuilder.AppendLine($"  {i + 1}. {visibility}{editable} **{layer.Name}** ({layer.LayerType})");
                    
                    if (layer.FeatureCount > 0)
                    {
                        contextBuilder.AppendLine($"     - 要素数量: {layer.FeatureCount:N0}");
                    }
                }

                if (context.ActiveLayers.Count > 10)
                {
                    contextBuilder.AppendLine($"  ... 还有 {context.ActiveLayers.Count - 10} 个图层");
                }
            }

            // 选择信息
            if (context.SelectedFeatureCount > 0)
            {
                contextBuilder.AppendLine($"**选中要素**: {context.SelectedFeatureCount:N0} 个");
                if (!string.IsNullOrEmpty(context.SelectedLayerName))
                {
                    contextBuilder.AppendLine($"**选择图层**: {context.SelectedLayerName}");
                }
            }

            // 项目信息
            if (!string.IsNullOrEmpty(context.ProjectPath))
            {
                contextBuilder.AppendLine($"**项目路径**: {context.ProjectPath}");
            }

            if (context.HasUnsavedEdits)
            {
                contextBuilder.AppendLine("⚠️ **注意**: 当前项目有未保存的编辑");
            }

            return contextBuilder.ToString();
        }



        /// <summary>
        /// 生成错误恢复提示词
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="context">当前上下文</param>
        /// <returns>错误恢复提示词</returns>
        public string GenerateErrorRecoveryPrompt(string errorMessage, string context = null)
        {
            var promptBuilder = new StringBuilder();
            
            promptBuilder.AppendLine("## ⚠️ 错误恢复模式");
            promptBuilder.AppendLine($"**遇到错误**: {errorMessage}");
            
            if (!string.IsNullOrEmpty(context))
            {
                promptBuilder.AppendLine($"**当前上下文**: {context}");
            }
            
            promptBuilder.AppendLine();
            promptBuilder.AppendLine("请分析错误原因，尝试以下恢复策略：");
            promptBuilder.AppendLine("1. 检查是否为临时性错误，可以重试");
            promptBuilder.AppendLine("2. 寻找替代的执行方案");
            promptBuilder.AppendLine("3. 降级处理，提供部分功能");
            promptBuilder.AppendLine("4. 向用户说明情况并请求指导");
            promptBuilder.AppendLine();
            promptBuilder.AppendLine("请选择最合适的恢复策略并执行。");

            return promptBuilder.ToString();
        }

        /// <summary>
        /// 生成性能优化建议提示词
        /// </summary>
        /// <param name="operationType">操作类型</param>
        /// <param name="dataSize">数据规模</param>
        /// <returns>性能优化建议</returns>
        public string GeneratePerformanceOptimizationPrompt(string operationType, int dataSize = 0)
        {
            var promptBuilder = new StringBuilder();
            
            promptBuilder.AppendLine("## ⚡ 性能优化建议");
            promptBuilder.AppendLine($"**操作类型**: {operationType}");
            
            if (dataSize > 0)
            {
                promptBuilder.AppendLine($"**数据规模**: {dataSize:N0} 条记录");
                
                if (dataSize > 100000)
                {
                    promptBuilder.AppendLine("⚠️ **大数据集警告**: 当前数据集较大，建议：");
                    promptBuilder.AppendLine("- 考虑分批处理");
                    promptBuilder.AppendLine("- 使用空间索引加速");
                    promptBuilder.AppendLine("- 预先筛选必要数据");
                }
            }
            
            promptBuilder.AppendLine();
            promptBuilder.AppendLine("请在执行操作时考虑性能优化，选择最高效的处理方式。");

            return promptBuilder.ToString();
        }
    }
}
