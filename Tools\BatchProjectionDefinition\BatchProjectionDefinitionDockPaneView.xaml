<UserControl x:Class="XIAOFUTools.Tools.BatchProjectionDefinition.BatchProjectionDefinitionDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.BatchProjectionDefinition"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="400"
             d:DataContext="{Binding Path=BatchProjectionDefinitionViewModel}"
             Loaded="UserControl_Loaded">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 帮助按钮样式 -->
            <Style x:Key="HelpButtonStyle" TargetType="Button" BasedOn="{StaticResource TextButtonStyle}">
                <Setter Property="Width" Value="22"/>
                <Setter Property="Height" Value="22"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="Margin" Value="0"/>
                <Setter Property="HorizontalAlignment" Value="Left"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>
    
    <Grid Margin="12">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 坐标系选择区域 -->
        <Border Grid.Row="0" BorderBrush="#CDCDCD" BorderThickness="1" CornerRadius="4" Margin="0,0,0,10" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="目标坐标系:" FontWeight="Bold" Margin="0,0,0,5"/>
                
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="{Binding SelectedCoordinateSystemName}" 
                              VerticalAlignment="Center" TextWrapping="Wrap"/>
                    <Button Grid.Column="1" Content="选择坐标系" 
                           Command="{Binding SelectCoordinateSystemCommand}"
                           Style="{StaticResource DefaultButtonStyle}"
                           Width="80" Height="22" Margin="10,0,0,0"/>
                </Grid>
            </Grid>
        </Border>

        <!-- 图层列表操作按钮 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
            <Button Content="全选" Command="{Binding SelectAllCommand}"
                   Style="{StaticResource DefaultButtonStyle}"
                   Width="60" Height="22" Margin="0,0,10,0"/>
            <Button Content="反选" Command="{Binding SelectNoneCommand}"
                   Style="{StaticResource DefaultButtonStyle}"
                   Width="60" Height="22" Margin="0,0,10,0"/>
            <Button Content="⟲" Width="22" Height="22" Margin="0,0,0,0"
                   Style="{StaticResource DefaultButtonStyle}"
                   Command="{Binding RefreshLayersCommand}"
                   ToolTip="刷新图层列表"
                   FontSize="14" FontWeight="Bold"/>
        </StackPanel>

        <!-- 图层列表 -->
        <Border Grid.Row="2" BorderBrush="#CDCDCD" BorderThickness="1" Margin="0,0,0,10">
            <DataGrid ItemsSource="{Binding LayerList}"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     CanUserReorderColumns="False"
                     CanUserResizeRows="False"
                     CanUserSortColumns="True"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     SelectionMode="Extended"
                     BorderThickness="0">
                <DataGrid.Columns>
                    <!-- 选择列 -->
                    <DataGridTemplateColumn Header="选择" Width="50">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                         HorizontalAlignment="Center"
                                         Checked="CheckBox_Checked"
                                         Unchecked="CheckBox_Unchecked"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    
                    <!-- 图层名称列 -->
                    <DataGridTextColumn Header="图层名称" Binding="{Binding LayerName}" Width="*" IsReadOnly="True"/>
                    
                    <!-- 类型列 -->
                    <DataGridTextColumn Header="类型" Binding="{Binding LayerType}" Width="80" IsReadOnly="True"/>
                    
                    <!-- 当前坐标系列 -->
                    <DataGridTextColumn Header="当前坐标系" Binding="{Binding CurrentCoordinateSystem}" Width="120" IsReadOnly="True"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- 进度条 -->
        <ProgressBar Grid.Row="3" Margin="0,0,0,2"
                   Style="{StaticResource ProgressBarStyle}"
                   Value="{Binding Progress}"
                   Minimum="0" Maximum="100"
                   IsIndeterminate="{Binding IsProgressIndeterminate}"
                   Height="6"/>

        <!-- 日志窗口 -->
        <Border Grid.Row="4" BorderBrush="#CDCDCD" BorderThickness="1" Margin="0,0,0,10" Height="120">
            <TextBox Style="{StaticResource LogTextBoxStyle}"
                    Text="{Binding LogContent, Mode=OneWay}"
                    BorderThickness="0"
                    VerticalAlignment="Stretch"
                    HorizontalAlignment="Stretch"/>
        </Border>

        <!-- 状态消息和按钮区域 -->
        <Grid Grid.Row="5" Margin="0,0,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 执行状态 -->
            <TextBlock Grid.Row="0" Text="{Binding StatusMessage}" 
                    TextWrapping="Wrap" VerticalAlignment="Center" Margin="0,0,0,10"/>
            
            <!-- 按钮区域 -->
            <Border Grid.Row="1" BorderBrush="{StaticResource DividerBrush}" 
                   BorderThickness="0,1,0,0" 
                   Margin="0,5,0,0" 
                   Padding="0,10,0,0">
                <Grid>
                    <Button Content="?" Width="22" Height="22"
                            Style="{StaticResource HelpButtonStyle}"
                            Command="{Binding ShowHelpCommand}"
                            ToolTip="查看工具使用说明"
                            HorizontalAlignment="Left"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <!-- 只在处理时显示停止按钮 -->
                        <Button Content="停止" Width="80" Command="{Binding CancelCommand}" Margin="0,0,10,0"
                                Style="{StaticResource CancelButtonStyle}"
                                Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}"
                                IsEnabled="{Binding IsProcessing}"/>
                        <Button Content="开始" Width="80" Command="{Binding RunCommand}"
                                Style="{StaticResource ExecuteButtonStyle}"
                                IsEnabled="{Binding CanProcess}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
