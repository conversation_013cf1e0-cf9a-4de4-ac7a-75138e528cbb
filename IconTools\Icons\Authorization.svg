<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 核心功能蓝色渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>

    <!-- 盾牌渐变 -->
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:0.9" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shieldShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="1" stdDeviation="2" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="32" height="32" rx="8" fill="url(#bgGradient)"/>

  <!-- 盾牌主体 -->
  <path d="M16 4 C20 4 24 6 24 6 C24 6 24 14 24 18 C24 24 16 28 16 28 C16 28 8 24 8 18 C8 14 8 6 8 6 C8 6 12 4 16 4 Z" 
        fill="url(#shieldGradient)" filter="url(#shieldShadow)"/>

  <!-- 盾牌边框 -->
  <path d="M16 4 C20 4 24 6 24 6 C24 6 24 14 24 18 C24 24 16 28 16 28 C16 28 8 24 8 18 C8 14 8 6 8 6 C8 6 12 4 16 4 Z" 
        fill="none" stroke="#667eea" stroke-width="0.5" opacity="0.3"/>

  <!-- 钥匙图标 -->
  <g transform="translate(16,16)">
    <!-- 钥匙头部（圆形） -->
    <circle cx="-2" cy="-2" r="3" fill="#667eea" opacity="0.8"/>
    <circle cx="-2" cy="-2" r="1.5" fill="none" stroke="#ffffff" stroke-width="1"/>
    
    <!-- 钥匙杆 -->
    <rect x="1" y="-2.5" width="4" height="1" fill="#667eea" opacity="0.8"/>
    
    <!-- 钥匙齿 -->
    <rect x="4" y="-2.5" width="1" height="2" fill="#667eea" opacity="0.8"/>
    <rect x="3" y="-1.5" width="1" height="1" fill="#667eea" opacity="0.8"/>
  </g>

  <!-- 授权状态指示灯 -->
  <circle cx="22" cy="10" r="2" fill="#00b894" opacity="0.9"/>
  <circle cx="22" cy="10" r="1" fill="#ffffff" opacity="0.8"/>

  <!-- 装饰元素 -->
  <circle cx="16" cy="16" r="8" fill="none" stroke="#ffffff" stroke-width="0.5" opacity="0.2"/>
</svg>
