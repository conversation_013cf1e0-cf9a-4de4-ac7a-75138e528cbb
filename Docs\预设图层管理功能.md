# 预设图层管理功能

## 📋 功能概述

预设图层管理功能允许用户导入、管理和快速添加预设的图层文件（.lyr和.lyrx格式）到地图中。

## 🎯 主要功能

### 1. 预设图层Gallery
- **位置**: 工具箱选项卡、右键菜单
- **支持格式**: .lyr（ArcMap图层文件）、.lyrx（ArcGIS Pro图层文件）
- **存储位置**: `Data\影像图层` 文件夹（相对于程序集位置）

### 2. 设置界面管理
- **导入图层**: 支持批量导入.lyr和.lyrx文件
- **打开文件夹**: 一键打开预设图层存储文件夹
- **自动刷新**: 可设置是否自动刷新图层列表
- **图层位置**: 可设置新添加的图层是否自动移动到底部

## 🚀 使用方法

### 方式1: 通过设置界面管理
1. **打开设置** - 点击工具箱中的"设置"按钮
2. **找到预设图层管理** - 在设置界面中找到"预设图层管理"组
3. **导入图层** - 点击"导入图层"按钮选择.lyr或.lyrx文件
4. **管理文件夹** - 点击"打开文件夹"直接访问存储位置

### 方式2: 通过右键菜单快速添加
1. **地图框右键** - 在内容窗格的地图名称上右键 → "添加预设图层"
2. **地图视图右键** - 在地图视图空白区域右键 → "添加预设图层"
3. **选择要素右键** - 在选中要素上右键 → "添加预设图层"

### 方式3: 通过工具箱选项卡
1. **打开工具箱** - 切换到XIAOFU工具箱选项卡
2. **通用工具组** - 在通用工具组中找到"添加预设图层"
3. **选择图层** - 从下拉列表中选择需要的预设图层

## ⚙️ 设置选项

### 预设图层设置
- **自动刷新图层列表**: 启用后，预设图层列表会自动刷新以显示新添加的图层文件
- **添加图层后自动移动到底部**: 启用后，新添加的预设图层会自动移动到图层列表底部

### 文件夹位置
- **默认路径**: `程序集位置\Data\影像图层`
- **自动创建**: 如果文件夹不存在，系统会自动创建

## 📁 文件管理

### 支持的文件格式
- **.lyr**: ArcMap图层文件，包含符号系统和数据源信息
- **.lyrx**: ArcGIS Pro图层文件，包含符号系统和数据源信息

### 导入功能特点
- **批量导入**: 支持一次选择多个图层文件
- **重复检查**: 如果文件已存在，会询问是否覆盖
- **错误处理**: 导入失败的文件会显示错误统计
- **结果反馈**: 显示成功和失败的文件数量

### 文件夹操作
- **一键打开**: 直接在资源管理器中打开预设图层文件夹
- **自动创建**: 如果文件夹不存在，会自动创建
- **路径显示**: 在设置界面中显示完整的文件夹路径

## 🎨 右键菜单集成

### 已集成的菜单
1. **地图选择上下文菜单** - 在选中要素上右键时显示
2. **地图框右键菜单** - 在地图列表中的地图上右键时显示
3. **地图视图右键菜单** - 在地图视图空白区域右键时显示

### 菜单位置
- 预设图层Gallery通常位于其他工具按钮的上方
- 与批量添加数据等功能组合在一起，提供完整的数据管理解决方案

## 💡 使用技巧

### 最佳实践
1. **组织图层**: 将常用的图层文件按类别组织到子文件夹中
2. **命名规范**: 使用有意义的文件名，便于识别图层内容
3. **定期清理**: 删除不再使用的图层文件，保持文件夹整洁
4. **备份重要图层**: 对重要的预设图层文件进行备份

### 性能优化
1. **启用自动刷新**: 如果经常添加新图层，启用自动刷新功能
2. **图层位置设置**: 根据工作习惯设置图层添加位置
3. **批量操作**: 一次性导入多个图层文件，提高效率

## 🔧 故障排除

### 常见问题
1. **图层无法显示**: 检查图层文件的数据源路径是否正确
2. **导入失败**: 确认文件格式正确且未被其他程序占用
3. **文件夹无法打开**: 检查文件夹权限和路径是否正确

### 解决方案
1. **重新设置数据源**: 在ArcGIS中重新设置图层的数据源路径
2. **检查文件权限**: 确保对预设图层文件夹有读写权限
3. **重启应用**: 如果遇到问题，尝试重启ArcGIS Pro

---

*最后更新: 2024年*  
*版本: v1.0*
