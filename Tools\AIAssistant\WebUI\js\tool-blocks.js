// 工具块管理器 - Augment/Cursor风格
class ToolBlockManager {
    constructor() {
        this.container = document.getElementById('toolBlocksContainer');
        this.toolBlocks = new Map(); // 存储工具块实例
        this.currentToolId = 0;
        
        console.log('ToolBlockManager initialized');
    }

    /**
     * 创建新的工具块
     * @param {string} toolName - 工具名称
     * @param {Object} params - 工具参数
     * @returns {string} 工具块ID
     */
    createToolBlock(toolName, params = {}) {
        const toolId = `tool_${++this.currentToolId}`;
        const toolBlock = this.createToolBlockElement(toolId, toolName, params);
        
        this.container.appendChild(toolBlock);
        this.toolBlocks.set(toolId, {
            element: toolBlock,
            name: toolName,
            params: params,
            status: 'pending',
            startTime: Date.now()
        });

        // 自动滚动到工具块
        this.scrollToToolBlock(toolId);
        
        console.log(`Created tool block: ${toolId} for ${toolName}`);
        return toolId;
    }

    /**
     * 创建工具块DOM元素
     */
    createToolBlockElement(toolId, toolName, params) {
        const toolBlock = document.createElement('div');
        toolBlock.className = 'tool-block';
        toolBlock.id = toolId;
        
        const icon = this.getToolIcon(toolName);
        const displayName = this.getToolDisplayName(toolName);
        
        toolBlock.innerHTML = `
            <div class="tool-block-header" onclick="toolBlockManager.toggleToolBlock('${toolId}')">
                <div class="tool-block-title">
                    <div class="tool-block-icon">${icon}</div>
                    <span>${displayName}</span>
                </div>
                <div class="tool-block-status">
                    <div class="status-dot"></div>
                    <span class="status-text">准备中</span>
                    <span class="tool-block-toggle">▼</span>
                </div>
            </div>
            <div class="tool-block-content">
                <div class="tool-block-params">${this.formatParams(params)}</div>
                <div class="tool-block-result" style="display: none;"></div>
            </div>
        `;
        
        return toolBlock;
    }

    /**
     * 开始执行工具
     */
    startToolExecution(toolId) {
        const toolData = this.toolBlocks.get(toolId);
        if (!toolData) return;

        toolData.status = 'executing';
        toolData.element.className = 'tool-block executing';
        
        const statusText = toolData.element.querySelector('.status-text');
        statusText.textContent = '执行中';
        
        console.log(`Tool block ${toolId} started execution`);
    }

    /**
     * 完成工具执行
     */
    completeToolExecution(toolId, result, success = true) {
        const toolData = this.toolBlocks.get(toolId);
        if (!toolData) return;

        toolData.status = success ? 'completed' : 'failed';
        toolData.element.className = `tool-block ${toolData.status}`;
        toolData.result = result;
        toolData.endTime = Date.now();
        
        const statusText = toolData.element.querySelector('.status-text');
        const duration = Math.round((toolData.endTime - toolData.startTime) / 1000 * 10) / 10;
        statusText.textContent = success ? `完成 (${duration}s)` : `失败 (${duration}s)`;
        
        // 显示结果
        const resultElement = toolData.element.querySelector('.tool-block-result');
        resultElement.textContent = this.formatResult(result);
        resultElement.style.display = 'block';
        
        console.log(`Tool block ${toolId} ${success ? 'completed' : 'failed'}`);
    }

    /**
     * 切换工具块展开/收起状态
     */
    toggleToolBlock(toolId) {
        const toolData = this.toolBlocks.get(toolId);
        if (!toolData) return;

        const element = toolData.element;
        element.classList.toggle('expanded');
        
        console.log(`Tool block ${toolId} toggled`);
    }

    /**
     * 滚动到指定工具块
     */
    scrollToToolBlock(toolId) {
        const toolData = this.toolBlocks.get(toolId);
        if (!toolData) return;

        setTimeout(() => {
            toolData.element.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'nearest' 
            });
        }, 100);
    }

    /**
     * 清空所有工具块
     */
    clearAllToolBlocks() {
        this.container.innerHTML = '';
        this.toolBlocks.clear();
        this.currentToolId = 0;
        
        console.log('All tool blocks cleared');
    }

    /**
     * 获取工具图标
     */
    getToolIcon(toolName) {
        const icons = {
            'batch_add_data': '📁',
            'area_calculator': '📐',
            'field_copy': '📋',
            'batch_clip': '✂️',
            'geometry_repair': '🔧',
            'coordinate_transform': '🌐',
            'export_data': '💾',
            'import_data': '📥',
            'spatial_analysis': '🗺️',
            'default': '🛠️'
        };
        
        return icons[toolName] || icons['default'];
    }

    /**
     * 获取工具显示名称
     */
    getToolDisplayName(toolName) {
        const names = {
            'batch_add_data': '批量添加数据',
            'area_calculator': '面积计算',
            'field_copy': '字段复制',
            'batch_clip': '批量裁剪',
            'geometry_repair': '几何修复',
            'coordinate_transform': '坐标转换',
            'export_data': '数据导出',
            'import_data': '数据导入',
            'spatial_analysis': '空间分析'
        };
        
        return names[toolName] || toolName;
    }

    /**
     * 格式化参数显示
     */
    formatParams(params) {
        if (!params || Object.keys(params).length === 0) {
            return '无参数';
        }
        
        return JSON.stringify(params, null, 2);
    }

    /**
     * 格式化结果显示
     */
    formatResult(result) {
        if (typeof result === 'string') {
            return result;
        }
        
        if (typeof result === 'object') {
            return JSON.stringify(result, null, 2);
        }
        
        return String(result);
    }

    /**
     * 获取所有工具块状态
     */
    getToolBlocksStatus() {
        const status = {
            total: this.toolBlocks.size,
            pending: 0,
            executing: 0,
            completed: 0,
            failed: 0
        };

        this.toolBlocks.forEach(toolData => {
            status[toolData.status]++;
        });

        return status;
    }

    /**
     * 模拟工具执行（用于测试）
     */
    simulateToolExecution(toolName, params = {}) {
        const toolId = this.createToolBlock(toolName, params);
        
        // 延迟开始执行
        setTimeout(() => {
            this.startToolExecution(toolId);
            
            // 模拟执行时间
            const executionTime = Math.random() * 3000 + 1000; // 1-4秒
            setTimeout(() => {
                const success = Math.random() > 0.2; // 80%成功率
                const result = success ? 
                    `${this.getToolDisplayName(toolName)}执行成功！处理了${Math.floor(Math.random() * 100)}条记录。` :
                    `${this.getToolDisplayName(toolName)}执行失败：模拟错误信息`;
                
                this.completeToolExecution(toolId, result, success);
            }, executionTime);
        }, 500);
        
        return toolId;
    }
}

// 全局工具块管理器实例
let toolBlockManager;

// 初始化工具块管理器
document.addEventListener('DOMContentLoaded', () => {
    toolBlockManager = new ToolBlockManager();
    console.log('Tool block manager initialized');
});

// 测试函数（可在控制台调用）
function testToolBlocks() {
    console.log('Testing tool blocks...');
    
    toolBlockManager.simulateToolExecution('batch_add_data', {
        inputFolder: 'C:/GIS/Data/Input',
        outputWorkspace: 'C:/GIS/Data/Output.gdb',
        fileTypes: ['shp', 'gdb']
    });
    
    setTimeout(() => {
        toolBlockManager.simulateToolExecution('area_calculator', {
            featureClass: 'Parcels',
            areaField: 'AREA_SQKM',
            unit: 'SQUARE_KILOMETERS'
        });
    }, 1000);
    
    setTimeout(() => {
        toolBlockManager.simulateToolExecution('coordinate_transform', {
            inputSRS: 'EPSG:4326',
            outputSRS: 'EPSG:3857',
            transformMethod: 'WGS84_TO_WEB_MERCATOR'
        });
    }, 2000);
}

// 挂载到全局作用域
window.toolBlockManager = toolBlockManager;
window.testToolBlocks = testToolBlocks;
