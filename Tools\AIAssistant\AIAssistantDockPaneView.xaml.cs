using System;
using System.IO;
using System.Windows.Controls;
using Microsoft.Web.WebView2.Core;
using XIAOFUTools.Tools.AIAssistant.Services;

namespace XIAOFUTools.Tools.AIAssistant
{
    /// <summary>
    /// AI助手停靠窗格视图
    /// </summary>
    public partial class AIAssistantDockPaneView : UserControl
    {
        private AIAssistantDockPaneViewModel _viewModel;
        private WebViewBridge _webViewBridge;

        public AIAssistantDockPaneView()
        {
            InitializeComponent();
            _viewModel = new AIAssistantDockPaneViewModel();
            DataContext = _viewModel;
        }

        private async void UserControl_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                // 初始化WebView2
                await InitializeWebView();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AI助手初始化失败: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task InitializeWebView()
        {
            // 确保WebView2运行时已安装
            await webView.EnsureCoreWebView2Async();

            // 设置WebView2选项
            webView.CoreWebView2.Settings.IsGeneralAutofillEnabled = false;
            webView.CoreWebView2.Settings.IsPasswordAutosaveEnabled = false;
            webView.CoreWebView2.Settings.AreDevToolsEnabled = true; // 开发时启用，发布时可关闭

            // 创建通信桥梁
            _webViewBridge = new WebViewBridge(webView, _viewModel);

            // 导航到本地HTML文件
            string htmlPath = GetWebUIPath();
            if (File.Exists(htmlPath))
            {
                webView.CoreWebView2.Navigate($"file:///{htmlPath.Replace('\\', '/')}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("未找到WebUI文件: " + htmlPath);
            }
        }

        private string GetWebUIPath()
        {
            // 获取插件安装目录下的WebUI文件夹路径
            string assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
            string assemblyDir = Path.GetDirectoryName(assemblyLocation);
            return Path.Combine(assemblyDir, "Tools", "AIAssistant", "WebUI", "index.html");
        }

        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                System.Diagnostics.Debug.WriteLine("AI助手WebUI加载成功");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("AI助手WebUI页面加载失败");
            }
        }

        private void WebView_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            // 处理来自JavaScript的消息
            _webViewBridge?.HandleWebMessage(e.TryGetWebMessageAsString());
        }
    }
}
