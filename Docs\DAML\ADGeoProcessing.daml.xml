<?xml version="1.0" encoding="utf-8"?>
<ArcGIS defaultAssembly="ArcGIS.Desktop.GeoProcessing.dll" defaultNamespace="ArcGIS.Desktop.GeoProcessing" xmlns="http://schemas.esri.com/DADF/Registry" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://schemas.esri.com/DADF/Registry \ArcGIS\ArcGISDesktop\UIFramework\ArcGIS.Desktop.Framework.xsd">

  <dependencies>
    <dependency name="ADCore.daml" />
    <dependency name="ADMapping.daml" />
    <dependency name="ArcGISLayout.daml" />
    <dependency name="ADDataSourcesRaster.daml" />
  </dependencies>

  <dialogs>
    <dialog id="GPEnvironments" className="EnvironmentsViewModel">
      <content className="EnvironmentsView" />
    </dialog>
    <dialog id="GP_Gallery_Customize" className="GalleryCustomizeViewModel">
      <content className="GalleryCustomizeView" />
    </dialog>
  </dialogs>

  <accelerators>
  </accelerators>

  <shortcutTables>
    <!--<insertShortcutTable id="esri_modelbuilder_General" category="ModelBuilder" caption="General" targetID="esri_geoprocessing_modelBuilderPane">
      <shortcut refID="esri_modelbuilder_CycleDialogs" flags="Ctrl+Shift" key="F6" />
      <shortcut refID="esri_modelbuilder_ClearSelection" key="Escape" />
      <shortcut refID="esri_modelbuilder_CycleActiveItems" flags="Shift" key="Tab" />
    </insertShortcutTable>-->

    <insertShortcutTable id="esri_modelbuilder_Model" category="ModelBuilder" caption="Diagram" description="Use the ModelBuilder view shortcuts to navigate the model diagram and manage model elements."
                         targetID="esri_geoprocessing_modelBuilderPane" condition="esri_geoprocessing_anyMBPane" isPrimary="true" requiresModifier="true">
      <shortcut refID="esri_geoprocessing_modelBuilderButton" flags="Ctrl" key="N" />
      <shortcut refID="esri_modelbuilder_saveButton" flags="Ctrl" key="S" />
      <shortcut refID="esri_modelbuilder_saveAsButton" flags="Ctrl+Shift" key="S" />
      <shortcut refID="esri_modelbuilder_exportScriptButton" flags="Shift" key="F" />
      <shortcut refID="esri_modelbuilder_SentModelToPythonWindowButton" flags="Shift" key="W" />
      <shortcut refID="esri_modelBuilder_editToolProperties" flags="Shift" key="P" />

      <shortcut refID="esri_modelbuilder_exportImageButton" flags="Shift" key="G"/>
      <shortcut refID="esri_modelbuilder_OpenToolInGPPane" flags="Shift" key="T" />
      <shortcut refID="esri_modelbuilder_environmentsButton" flags="Shift" key="E" />
      <shortcut refID="esri_modelbuilder_progressorOpen" flags="Shift" key="O" />

      <shortcut refID="esri_modelbuilder_autoLayoutDiagramButton" flags="Ctrl+Shift" key="A" />
      <shortcut refID="esri_modelbuilder_fitToWindowButton" flags="Ctrl+Shift" key="F" />
      <shortcut refID="esri_modelbuilder_ZoomStepUp" flags="Ctrl" key="OemPlus"/>
      <shortcut refID="esri_modelbuilder_ZoomStepDown" flags="Ctrl" key="OemMinus" />

      <shortcut refID="esri_modelbuilder_selectAllButton" flags="Ctrl" key="A" />
      <shortcut refID="esri_modelbuilder_selectAllGroupButton" flags="Ctrl+Alt" key="A" />
      <shortcut refID="esri_modelbuilder_ExtentLeft" flags="Shift" key="Left" onKeyUp="true"/>
      <shortcut refID="esri_modelbuilder_ExtentRight" flags="Shift" key="Right" onKeyUp="true"/>
      <shortcut refID="esri_modelbuilder_ExtentDown" flags="Shift" key="Down" onKeyUp="true"/>
      <shortcut refID="esri_modelbuilder_ExtentUp" flags="Shift" key="Up" onKeyUp="true"/>

      <shortcut refID="esri_modelbuilder_runModelButton" flags="Ctrl+Shift" key="R" />
      <shortcut refID="esri_modelbuilder_validateButton" flags="Ctrl+Shift" key="V" />

      <shortcut refID="esri_modelbuilder_createVariableButton" flags="Ctrl" key="E" />
      <shortcut refID="esri_modelbuilder_createLabelButton" flags="Ctrl" key="L" />

      <shortcut refID="esri_modelbuilder_groupButton" flags="Ctrl" key="G" />
      <shortcut refID="esri_modelbuilder_AutoLayoutGroup" flags="Ctrl+Shift" key="L" />
      <shortcut refID="esri_modelbuilder_dissolveGroup" flags="Ctrl+Shift" key="G" />
      <shortcut refID="esri_modelbuilder_expandSelectedGroup" flags="Ctrl" key="Right" onKeyUp="true"/>
      <shortcut refID="esri_modelbuilder_collapseSelectedGroup" flags="Ctrl" key="Left" onKeyUp="true"/>
      <shortcut refID="esri_modelbuilder_groupCollapseAll" flags="Ctrl+Shift" key="Left" onKeyUp="true"/>
      <shortcut refID="esri_modelbuilder_groupExpandAll" flags="Ctrl+Shift" key="Right" onKeyUp="true"/>

      <shortcut refID="esri_modelbuilder_openElementButton" flags="Ctrl" key="O" />
      <shortcut refID="esri_modelbuilder_renameElementButton" flags="Ctrl" key="R" />
      <shortcut refID="esri_modelbuilder_parameterOnButton" flags="Ctrl" key="P"/>
      <shortcut refID="esri_modelbuilder_parameterOffButton" flags="Ctrl+Shift" key="P"/>
      <shortcut refID="esri_modelBuilder_addToDisplayOffButton" flags="Ctrl" key="D" />
      <!--Off status-->
      <shortcut refID="esri_modelBuilder_addToDisplayOnButton" flags="Ctrl+Shift" key="D" />
      <shortcut refID="esri_modelBuilder_DeleteIntermediateDataOnButton" flags="Ctrl+Shift" key="I" />
      <shortcut refID="esri_modelBuilder_DeleteIntermediateDataOffButton" flags="Ctrl" key="I" />

      <shortcut refID="esri_modelbuilder_cutElementButton" flags="Ctrl" key="X" isReadOnly="true"/>
      <shortcut refID="esri_modelbuilder_copyElementButton" flags="Ctrl" key="C" isReadOnly="true"/>
      <shortcut refID="esri_modelbuilder_pasteElementButton" flags="Ctrl" key="V" isReadOnly="true"/>
      <shortcut refID="esri_modelbuilder_AddSubGroup" flags="Ctrl" key="J"/>
      <!--<shortcut refID="esri_modelbuilder_renameGroup" flags="Ctrl" key="R"/>-->
      <shortcut refID="esri_modelbuilder_findAndReplace" flags="Ctrl" key="F" isReadOnly="true"/>
      <shortcut refID="esri_modelbuilder_reportViewButton" flags="Shift" key="R" isReadOnly="true"/>
    </insertShortcutTable>

    <insertShortcutTable id="esri_modelbuilder_View" category="ModelBuilder" caption="Report" description="Use the ModelBuilder Report view shortcuts to navigate the model report and interact with report elements."
                         targetID="esri_geoprocessing_MBReportPane" isPrimary="true" requiresModifier="true">
      <shortcut refID="esri_modelbuilder_findAndReplace" flags="Ctrl" key="F" isReadOnly="true"/>
    </insertShortcutTable>

  </shortcutTables>

  <products>
    <insertProduct id="esri_product_spatialanalyst" caption="Spatial Analyst" description="Provides advanced spatial modeling and analysis tools for raster data. It is designed to solve complex problems such as modeling terrains, finding suitable locations, discovering spatial patterns, modeling distance and cost, performing hydrologic analysis, and statistical analysis for cell based data." code="10" />
    <insertProduct id="esri_product_datainterop" caption="Data Interoperability" description="Is an integrated spatial ETL (extract, transform, and load) toolset that runs within the geoprocessing framework using Safe Software's FME technology. It enables you to integrate data from multiple sources and formats, use that data with geoprocessing tools, and publish it." code="45"/>
  </products>

  <conditions>
    <insertCondition id="esri_geoprocessing_EditingScriptToolCondition">
      <state id="esri_geoprocessing_EditingScriptTool" />
    </insertCondition>
    <insertCondition id="esri_projectItem_DefaultToolboxSelectedCondition">
      <not>
        <state id="esri_projectItem_DefaultToolboxSelected" />
      </not>
      <and>
        <state id="esri_mapping_openProjectCondition"/>
      </and>
    </insertCondition>
    <insertCondition id="esri_projectItem_SelectedToolboxMakeDefaultCondition">
      <not>
        <state id="esri_projectItem_DefaultToolboxSelected" />
      </not>
      <and>
        <state id="esri_mapping_openProjectCondition"/>
      </and>
    </insertCondition>
    <insertCondition id="esri_geoprocessing_toolbox_broken">
      <state id="esri_geoprocessing_toolbox_InvalidCondition"/>
    </insertCondition>
    <insertCondition id="esri_geoprocessing_toolbox_valid">
      <not>
        <state id="esri_geoprocessing_toolbox_InvalidCondition"/>
      </not>
    </insertCondition>

    <insertCondition id="esri_geoprocessing_DataInterop_installed">
      <state id="esri_geoprocessing_DI_installed" />
    </insertCondition>
    <insertCondition id="esri_geoprocessing_ToolSuggestions_installed">
      <and>
        <state id="esri_geoprocessing_TS_installed" />
        <state id="esri_geoprocessing_modelBuilderPane"/>
      </and>
    </insertCondition>
    <insertCondition id="esri_geoprocessing_PortalCondition">
      <state id="esri_geoprocessing_ShowPortal" />
    </insertCondition>
    <insertCondition id="esri_geoprocessing_mb_usegridrouter">
      <state id="esri_geoprocessing_mb_use_gridrouter" />
    </insertCondition>
    <insertCondition id="esri_geoprocessing_anyMBPane">
      <or>
        <state id="esri_geoprocessing_modelBuilderPane"/>
        <state id="esri_geoprocessing_MBReportPane"/>
      </or>
    </insertCondition>
    <insertCondition id="esri_geoprocessing_anyGPPane">
      <or>
        <state id="esri_geoprocessing_modelBuilderPane"/>
        <state id="esri_geoprocessing_MBReportPane"/>
        <state id="esri_geoprocessing_proNotebookPane"/>
      </or>
    </insertCondition>
    <insertCondition id="esri_geoprocessing_container">
      <state id="esri_geoprocessing_container"/>
    </insertCondition>
    <insertCondition  id="esri_geoprocessing_mb_autosaveEnabledState" >
      <or>
        <state id="esri_geoprocessing_anyMBPane"/>
        <state id="esri_geoprocessing_mb_HasValidBuildState"/>
      </or>
    </insertCondition>
    <insertCondition id="esri_geoprocessing_EnviromentsToolPropsCondition">
      <state id="esri_geoprocessing_ExtToolProps" />
    </insertCondition>
  </conditions>

  <categories>
    <updateCategory refID="esri_core_projectContainers">
      <insertComponent id="esri_geoprocessing_container" className="ArcGIS.Desktop.Internal.GeoProcessing.GeoprocessingContainer" insert="after" placeWith="MapContainer">
        <content type="GP" displayName="Toolboxes" contextMenu="esri_geoprocessing_insertMenu"/>
      </insertComponent>
      <insertComponent id="esri_geoprocessing_history" className="ArcGIS.Desktop.Internal.GeoProcessing.HistoryContainer" insert="after" placeWith="esri_geodatabase_projectContainer">
        <content type="GPHistory" displayName="Geoprocessing History" contextMenu="esri_geoprocessing_historyContainerMenu"/>
      </insertComponent>
    </updateCategory>

    <updateCategory refID="esri_project_historyControls">
      <insertComponent id="esri_geoprocessing_history_ctrl" className="GPHistoryViewModel" insert="before" placeWith="esri_raster_functionsHistory">
        <content title="Geoprocessing" iconImage="GeoprocessingHistory24"
                 />
      </insertComponent>
    </updateCategory>


    <!--GP tools on customization dialog-->
    <updateCategory refID="esri_customizedCommandsCategory">
      <insertComponent id="esri_customized_GPCommands" className="ArcGIS.Desktop.GeoProcessing.GeoprocessingToolsComponent">
        <content displayName="Geoprocessing Tools" />
      </insertComponent>
    </updateCategory>
    <!--Catalog GP items-->
    <updateCategory refID="esri_itemInfoType">
      <insertComponent id="esri_itemInfoType_GeoprocessingProjectItem" className="ArcGIS.Desktop.GeoProcessing.GeoprocessingProjectItem" containerType="GP">
        <content>
          <supportedTypeIDs>
            <type id="toolbox_pyt" contextMenuID="esri_geoprocessing_FilePythonToolboxMenu"/>
            <type id="toolbox" contextMenuID="esri_geoprocessing_FileToolboxMenu"/>
            <type id="toolset" contextMenuID="esri_geoprocessing_ToolsetMenu"/>
            <type id="tool" contextMenuID="esri_geoprocessing_ToolMenu"/>
            <type id="tool_component" contextMenuID="esri_geoprocessing_PythonToolMenu"/>
            <type id="tool_model" contextMenuID="esri_geoprocessing_PythonToolMenu"/>
            <type id="tool_script" contextMenuID="esri_geoprocessing_PythonToolMenu"/>
            <type id="tool_specialized" contextMenuID="esri_geoprocessing_CustomToolMenu"/>
            <type id="tool_service" contextMenuID="esri_geoprocessing_PythonToolMenu"/>
            <type id="tool_pythonscript" contextMenuID=""/>
            <type id="service_geoprocessing" contextMenuID="esri_geoprocessing_ServerToolboxMenu"/>
            <type id="service_geoprocessing_task" contextMenuID="esri_geoprocessing_ServiceTaskMenu"/>
            <type id="toolbox_fgdb" contextMenuID="esri_geoprocessing_ToolboxMenu"/>
            <type id="toolbox_egdb" contextMenuID="esri_geoprocessing_PythonToolboxMenu"/>
            <type id="cim_toolbox" contextMenuID="esri_geoprocessing_ToolboxProjectItemMenu"/>
            <type id="cim_toolbox_pyt" contextMenuID="esri_geoprocessing_PythonToolboxProjectMenu"/>
          </supportedTypeIDs>
        </content>
      </insertComponent>
    </updateCategory>

    <insertCategory id="esri_gpGuidToControl">
      <component id="CLSID_MdStringCtrl">
        <content guid="{DA8EA072-AD53-11D5-A685-0008C711C8C1}" classType="ArcGIS.Desktop.Internal.GeoProcessing.Controls.gpString" />
      </component>
    </insertCategory>

    <insertCategory id="esri_gpToolConsumeCredit">
      <!--
         no overlay coin. tool dialog will make query after each validate to specified assembly class in staticClassType=XXX
      -->
      <component id="_geocoding">
        <content staticClassType="ArcGIS.Desktop.Internal.GeoProcessing.geocoding_QueryCredits">
          <tool name="geocoding.BatchGeocode"/>
          <tool name="geocoding.GeocodeLocationsFromTable"/>
          <tool name="geocoding.GeocodeAddresses"/>
          <tool name="geocoding.RematchAddresses"/>
          <tool name="geocoding.ReverseGeocode"/>
          <tool name="geocoding.CreateCompositeAddressLocator"/>
          <tool name="ca.UpdateFeaturesWithIncidentRecords"/>
        </content>
      </component>
      <component id="_na_estimator">
        <content staticClassType="ArcGIS.Desktop.Internal.GeoProcessing.na_QueryCredits+SolveCreditsEstimator">
          <tool name="na.Solve"/>
        </content>
      </component>
      <component id="_transit_estimator">
        <content staticClassType="ArcGIS.Desktop.Internal.GeoProcessing.na_QueryCredits+TransitCreditsEstimator">
          <tool name="transit.CalculateTransitServiceFrequency"/>
        </content>
      </component>
      <component id="_spatialstatsSWM_estimator">
        <content staticClassType="ArcGIS.Desktop.Internal.GeoProcessing.na_QueryCredits+SpatialStatsSWMEstimator">
          <tool name="stats.GenerateNetworkSWM"/>
        </content>
      </component>
      <component id="_transit_consumer">
        <content staticClassType="ArcGIS.Desktop.Internal.GeoProcessing.na_QueryCredits+TransitCreditsUsageIndicator">
          <tool name="transit.GenerateShapesFeaturesFromGTFS"/>
        </content>
      </component>
    </insertCategory>

    <insertCategory id="esri_gpToolDialogAddin">
      <customControl id="esri_gpToolDialogCustomValidate" className="ArcGIS.Desktop.Internal.GeoProcessing.custom_validate_addin">
        <content className="ArcGIS.Desktop.Internal.GeoProcessing.custom_validate_addin_vm">
          <!-- for tools:-->
          <tool name="management.AddJoin"/>
          <tool name="management.JoinField"/>
        </content>
      </customControl>
    </insertCategory>

    <insertCategory id="gpDataTypeFilters">
      <component id="DEDatasetType">
        <content filter="esri_browseDialogFilters_gp_datasets" />
      </component>
      <component id="DEAddressLocator">
        <content filter="esri_browseDialogFilters_locators_allTypes" />
      </component>
      <component id="DEBimFileWorkspace">
        <content filter="esri_browseDialogFilters_bimfile_workspace" />
      </component>
      <component id="DECadastralFabric">
        <content filter="esri_browseDialogFilters_geodatabaseItems_cadastralFabric" />
      </component>
      <component id="DECadDrawingDataset">
        <content filter="esri_browseDialogFilters_cad" />
      </component>
      <component id="DECatalogRoot">
        <content filter="esri_browseDialogFilters_basicTypes" />
      </component>
      <component id="DEDbaseTable">
        <content filter="esri_browseDialogFilters_tables_dbase" />
      </component>
      <component id="DEDiskConnection">
        <content filter="esri_browseDialogFilters_basicTypes" />
      </component>
      <component id="DEUtilityNetwork">
        <content filter="esri_browseDialogFilters_geodatabaseItems_facilityNetwork"/>
      </component>
      <component id="DETraceNetwork">
        <content filter="esri_browseDialogFilters_geodatabaseItems_traceNetwork"/>
      </component>
      <component id="DEFeatureClass">
        <content filter="esri_browseDialogFilters_featureClasses_all" />
      </component>
      <component id="DEFeatureDataset">
        <content filter="esri_browseDialogFilters_featureDatasets_all" />
      </component>
      <component id="DEFolder">
        <content filter="esri_browseDialogFilters_folders" />
      </component>
      <component id="DEFile">
        <content filter="esri_browseDialogFilters_browseFiles" />
      </component>
      <component id="DEGeoDataServer">
        <content filter="esri_browseDialogFilters_services_geodata" />
      </component>
      <component id="DEGeoDatasetType">
        <content filter="esri_browseDialogFilters_gp_geoDatasets" />
      </component>
      <component id="DEGeometricNetwork">
        <content filter="esri_browseDialogFilters_geodatabaseItems_geometricNetwork" />
      </component>
      <component id="DEGlobeServer">
        <content filter="esri_browseDialogFilters_services_globe" />
      </component>
      <component id="DEGPServer">
        <content filter="esri_browseDialogFilters_services_geoprocessing" />
      </component>
      <component id="DEImageServer">
        <content filter="esri_browseDialogFilters_services_image" />
      </component>
      <component id="DELasDataset">
        <content filter="esri_browseDialogFilters_las" />
      </component>
      <component id="DELayer">
        <content filter="esri_browseDialogFilters_gp_layerfiles" />
      </component>
      <component id="DELink">
        <content filter="esri_browseDialogFilters_basicTypes" />
      </component>
      <component id="DEMapDocument">
        <content filter="esri_browseDialogFilters_maps_mxd" />
      </component>
      <component id="DEMapServer">
        <content filter="esri_browseDialogFilters_services_map" />
      </component>
      <component id="DEMosaicDataset">
        <content filter="esri_browseDialogFilters_mosaic_selectable" />
      </component>
      <component id="DENetworkDataset">
        <content filter="esri_browseDialogFilters_geodatabaseItems_networkDataset" />
      </component>
      <component id="GPNetworkDataSource">
        <content filter="esri_browseDialogFilters_geodatabaseItems_networkDataset" />
      </component>
      <component id="DEParcelDataset">
        <content filter="esri_browseDialogFilters_geodatabaseItems_parcelDataset" />
      </component>
      <component id="DEPrjFile">
        <content filter="esri_browseDialogFilters_prjFile" />
      </component>
      <component id="DERasterBand">
        <content filter="esri_browseDialogFilters_rasterBands" />
      </component>
      <component id="DERasterCatalog">
        <content filter="esri_browseDialogFilters_geodatabaseItems_rasterCatalog" />
      </component>
      <component id="DERasterDataset">
        <content filter="esri_browseDialogFilters_rasters" />
      </component>
      <component id="DERelationshipClass">
        <content filter="esri_browseDialogFilters_geodatabaseItems_relationship" />
      </component>
      <!--<component id="DERemoteDatabaseFolder">
        <content filter="" />
      </component>-->
      <!--<component id="DERepresentationClass">
        <content filter="" />
      </component>-->
      <!--<component id="GPDiagramLayer">
        <content filter="esri_browseDialogFilters_geodatabaseItems_networkdiagram" />
      </component>-->
      <component id="DENetworkDiagram">
        <content filter="esri_browseDialogFilters_geodatabaseItems_networkdiagram" />
      </component>
      <component id="DEDiagramDataset">
        <content filter="esri_browseDialogFilters_geodatabaseItems_diagramDataset" />
      </component>
      <component id="DEServerConnection">
        <content filter="esri_browseDialogFilters_agsFiles" />
      </component>
      <component id="DEShapeFile">
        <content filter="esri_browseDialogFilters_shapefiles" />
      </component>
      <!--<component id="DESpatialReferencesFolder">
        <content filter="" />
      </component>-->
      <component id="DETable">
        <content filter="esri_browseDialogFilters_gp_tableView" />
      </component>
      <component id="DETerrain">
        <content filter="esri_browseDialogFilters_geodatabaseItems_terrain" />
      </component>
      <component id="DETextFile">
        <content filter="esri_browseDialogFilters_textFiles" />
      </component>
      <component id="DETin">
        <content filter="esri_browseDialogFilters_tinDatasets" />
      </component>
      <component id="DEToolbox">
        <content filter="esri_browseDialogFilters_toolboxes" />
      </component>
      <component id="DETool">
        <content filter="esri_browseDialogFilters_tools" />
      </component>
      <component id="DETopology">
        <content filter="esri_browseDialogFilters_geodatabaseItems_topology" />
      </component>
      <component id="DEType">
        <content filter="esri_browseDialogFilters_gp_all_online" />
      </component>
      <component id="DEWCSCoverage">
        <content filter="esri_browseDialogFilters_services_wcs" />
      </component>
      <component id="DEWMSMap">
        <content filter="esri_browseDialogFilters_services_wms" />
      </component>
      <component id="DEWMTS">
        <content filter="esri_browseDialogFilters_services_wmts" />
      </component>
      <component id="DEWorkspace">
        <content>
          <filter id="esri_browseDialogFilters_folders" />
          <filter id="esri_browseDialogFilters_geodatabases_file" />
          <filter id="esri_browseDialogFilters_geodatabases_sqlite" />
          <filter id="esri_browseDialogFilters_geodatabases_mobile" />
          <filter id="esri_browseDialogFilters_geodatabases_egdb_file_project" />
        </content>
      </component>
      <!--<component id="GPAnalysisCellSize">
        <content filter="" />
      </component>-->
      <component id="GPCadastralFabricLayer">
        <content filter="esri_browseDialogFilters_geodatabaseItems_cadastralFabric" />
      </component>
      <!--<component id="GPFacilityNetworkLayer">
        <content filter="esri_browseDialogFilters_geodatabaseItems_facilityNetwork"/>
      </component>-->
      <component id="GPFeatureLayer">
        <content>
          <filter id="esri_browseDialogFilters_featureClasses_all" displayName="Feature Classes and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPFeatureRecordSetLayer">
        <content>
          <filter id="esri_browseDialogFilters_featureClasses_all" displayName="Feature Classes and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPAnnotationLayer">
        <content>
          <filter id="esri_browseDialogFilters_featureClasses_annotation" displayName="Annotation Classes and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPCatalogLayer">
        <content>
          <filter id="esri_browseDialogFilters_catalogdataset" displayName="Catalog Layer and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GP3DObjectFeatureLayer">
        <content>
          <filter id="esri_browseDialogFilters_featureClasses_3dobject" displayName="3D Object Feature Layers">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPGraphicsLayer">
        <content filter="esri_browseDialogFilters_gp_layerfiles" />
      </component>
      <component id="GPDimensionLayer">
        <content>
          <filter id="esri_browseDialogFilters_featureClasses_dimension" displayName="Dimension Classes and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPGALayer">
        <content filter="esri_browseDialogFilters_gp_layerfiles" />
      </component>
      <component id="GPLasDatasetLayer">
        <content>
          <filter id="esri_browseDialogFilters_las" displayName="Las Datasets and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPLayer">
        <content filter="esri_browseDialogFilters_gp_layerfiles" />
      </component>
      <component id="GPLayout">
        <content filter="esri_browseDialogFilters_layouts_pagx" />
      </component>
      <component id="GPMap">
        <content filter="esri_browseDialogFilters_maps_all" />
      </component>
      <component id="GPMosaicLayer">
        <content>
          <filter id="esri_browseDialogFilters_mosaic_selectable" displayName="Mosaic Datasets and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPTrajectoryLayer">
        <content>
          <filter id="esri_browseDialogFilters_trajectory_selectable" displayName="Trajectory Datasets and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPNALayer">
        <content filter="esri_browseDialogFilters_gp_layerfiles" />
      </component>
      <component id="GPNetworkDatasetLayer">
        <content>
          <filter id="esri_browseDialogFilters_geodatabaseItems_networkDataset" displayName="Network Datasets and Layer Files" >
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPParcelLayer">
        <content filter="esri_browseDialogFilters_geodatabaseItems_parcelDataset" />
      </component>
      <component id="GPRasterLayer">
        <content>
          <filter id="esri_browseDialogFilters_rasters_GPrasterLayer" displayName="Rasters and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPRecordSet">
        <content filter="esri_browseDialogFilters_gp_tableView" />
      </component>
      <component id="GPSACellSize">
        <content filter="esri_browseDialogFilters_rasters_GPSAGeoData" />
      </component>
      <component id="GPSAGeoData">
        <content filter="esri_browseDialogFilters_rasters_GPSAGeoData" />
      </component>
      <component id="GPSAGeoDataWithSqlite">
        <content filter="esri_browseDialogFilters_rasters_GPSAGeoDataWithSqlite" />
      </component>
      <component id="GPTableView">
        <content>
          <filter id="esri_browseDialogFilters_gp_tableView" displayName="Tables and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPTerrainLayer">
        <content filter="esri_browseDialogFilters_geodatabaseItems_terrain" />
      </component>
      <component id="GPTinLayer">
        <content>
          <filter id="esri_browseDialogFilters_tinDatasets" displayName="Tin Datasets and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
      <component id="GPTopologyLayer">
        <content filter="esri_browseDialogFilters_geodatabaseItems_topology" />
      </component>
      <component id="GPType">
        <content filter="esri_browseDialogFilters_gp_all" />
      </component>
      <component id="GPSceneServiceLayer">
        <content filter="esri_browseDialogFilters_sceneServiceLayer" />
      </component>
      <component id="GPUtilityNetworkLayer">
        <content filter="esri_browseDialogFilters_geodatabaseItems_facilityNetwork" />
      </component>
      <component id="GPTraceNetworkLayer">
        <content filter="esri_browseDialogFilters_geodatabaseItems_traceNetwork" />
      </component>
      <component id="GPVectorLayer">
        <content filter="esri_browseDialogFilters_services_hosted_vectortiles_new" />
      </component>
      <component id="GPInternetTiledLayer">
        <content filter="esri_browseDialogFilters_services_hosted_tiles_new" />
      </component>
      <component id="GPOrientedImageryLayer">
        <content>
          <filter id="esri_browseDialogFilters_orientedImageryDataset" displayName="Oriented Imagery Layer and Layer Files">
            <type action="allow" id="file_lyrx" />
            <type action="allow" id="layer_general" />
          </filter>
        </content>
      </component>
    </insertCategory>

    <!--default project tools -->
    <insertCategory id="esri_gpDefaultProjectTools">
      <component id="_gpDefaultProjectTools">
        <content>
          <tool name="management.CalculateField"/>
          <tool name="analysis.PairwiseBuffer"/>
          <tool name="analysis.Near"/>
          <tool name="analysis.PairwiseDissolve"/>
          <tool name="analysis.SpatialJoin"/>
          <tool name="analysis.PairwiseIntersect"/>
        </content>
      </component>
    </insertCategory>
    <insertCategory id="esri_gpDefaultUserFavoriteTools">
      <component id="_gpDefaultUserFavoriteTools">
        <content>
        </content>
      </component>
    </insertCategory>
    <!-- default analysys gallery-->
    <insertCategory id="esri_gpDefaultGalleryTools">
      <component id="_gpDefaultGalleryTools">
        <content>
          <tool group="Default" name="analysis.PairwiseBuffer"/>
          <tool group="Default" name="analysis.SummarizeWithin"/>
          <tool group="Default" name="analysis.SpatialJoin"/>
          <tool group="Default" name="analysis.PairwiseClip"/>
          <tool group="Default" name="stats.OptimizedHotSpotAnalysis"/>
          <tool group="Summarize Data" name="analysis.SummarizeWithin"/>
          <tool group="Summarize Data" name="analysis.Statistics"/>
          <tool group="Summarize Data" name="gapro.JoinFeatures"/>
          <tool group="Summarize Data" name="sa.ZonalStatisticsAsTable"/>
          <tool group="Overlay Features" name="analysis.PairwiseClip"/>
          <tool group="Overlay Features" name="analysis.PairwiseIntersect"/>
          <tool group="Overlay Features" name="analysis.Union"/>
          <tool group="Overlay Features" name="analysis.SpatialJoin"/>
          <tool group="Determine Proximity" name="analysis.PairwiseBuffer"/>
          <tool group="Determine Proximity" name="analysis.Near"/>
          <tool group="Determine Proximity" name="analysis.GenerateNearTable"/>
          <tool group="Analyze Patterns" name="stats.OptimizedHotSpotAnalysis"/>
          <tool group="Analyze Patterns" name="stats.OptimizedOutlierAnalysis"/>
          <tool group="Analyze Patterns" name="stats.DensityBasedClustering"/>
          <tool group="Analyze Patterns" name="sa.KernelDensity"/>
          <tool group="Analyze Patterns" name="ga.EmpiricalBayesianKriging"/>
          <tool group="Analyze Terrain" name="sa.Slope"/>
          <tool group="Analyze Terrain" name="sa.Contour"/>
          <tool group="Analyze Terrain" name="sa.Viewshed2"/>
          <tool group="Regression, Prediction, and Classification" name="stats.Forest"/>
          <tool group="Regression, Prediction, and Classification" name="ia.ClassifyPixelsUsingDeepLearning"/>
          <tool group="Regression, Prediction, and Classification" name="ia.DetectObjectsUsingDeepLearning"/>
          <tool group="Enrich Data" name="analysis.Enrich"/>
          <tool group="Manage Data" name="analysis.PairwiseDissolve"/>
          <tool group="Manage Data" name="management.Append"/>
          <tool group="Manage Data" name="management.Merge"/>
          <tool group="Manage Data" name="management.Project"/>
          <tool group="Manage Data" name="management.AddField"/>
          <tool group="Manage Data" name="management.CalculateField"/>
          <tool group="Convert Data" name="management.FeatureToPoint"/>
          <tool group="Convert Data" name="management.PointsToLine"/>
          <tool group="Convert Data" name="management.FeatureToPolygon"/>
          <tool group="Convert Data" name="conversion.FeatureToRaster"/>
          <tool group="Convert Data" name="conversion.RasterToPoint"/>
          <tool group="Convert Data" name="conversion.RasterToPolygon"/>
        </content>
      </component>
    </insertCategory>

    <!-- Cad Gallery Items -->
    <insertCategory id="esri_gpDefaultCadGalleryTools">
      <component id="_gpDefaultCadGalleryTools">
        <content>
          <tool group="Data Loading" name="conversion.ExportFeatures"/>
          <tool group="Data Loading" name="management.CopyFeatures"/>
          <tool group="Data Loading" name="management.FeatureToPolygon"/>
          <tool group="Data Loading" name="analysis.SpatialJoin"/>
          <tool group="Data Loading" name="management.Append"/>
          <tool group="Data Loading" name="management.Merge"/>
        </content>
      </component>
    </insertCategory>
    <!-- Cad Gallery Items -->

    <!-- BIM Gallery Items -->
    <insertCategory id="esri_gpDefaultBimGalleryTools">
      <component id="_gpDefaultBimGalleryTools">
        <content>
          <tool group="Manage Data" name="management.CopyFeatures"/>
          <tool group="Manage Data" name="management.Merge"/>
          <tool group="Manage Data" name="management.Append"/>
          <tool group="Manage Data" name="conversion.ExportFeatures"/>
          <tool group="3D analyst tool" name="3d.EncloseMultiPatch"/>
          <tool group="3D analyst tool" name="3d.Intersect3D"/>
          <tool group="3D analyst tool" name="3d.Difference3D"/>
          <tool group="3D analyst tool" name="3d.Union3D"/>
        </content>
      </component>
    </insertCategory>
    <!-- BIM Gallery Items -->

    <!-- AI Assistants Extensions -->
    <updateCategory refID="esri_core_ai_assistant_extension_category">
      <insertComponent id="esri_geoprocessing_assistant_extension" className="ArcGIS.Desktop.Internal.GeoProcessing.GeoProcessingAssistantExtension">
        <content searchDescription="This skill class helps users create, retrieve, and open tools for data processing">
          <aiAssistantFunction name="SearchTool" searchDescription="Search through geoprocessing tools."/>
          <aiAssistantFunction name="PredictTool" searchDescription="Predict the next geoprocessing tools."/>
          <aiAssistantFunction name="OpenTool" searchDescription="Open a geoprocessing tool for the user to help them process their data."/>
          <aiAssistantFunction name="ToolIdToParameters" searchDescription="Get geoprocessing tool parameters."/>
          <aiAssistantFunction name="CreateToolBox" searchDescription="create a toolbox. A toolbox is used to store geoprocessing script and model tools for later use.  Scripts and model tools are automated processes."/>
        </content>
      </insertComponent>
      <insertComponent id="esri_modelbuilder_assistant_extension" className="ArcGIS.Desktop.Internal.GeoProcessing.ModelBuilderAssistantExtension">
        <content searchDescription="Create, retrieve and open model tools. A model can automating data processing">
          <aiAssistantFunction name="CreateNewModel" searchDescription="create a model within a toolbox.  Model tools are no code automated workflows used for storing tasks."/>
        </content>
      </insertComponent>
      <insertComponent id="esri_rasteranalysis_assistant_extension" className="ArcGIS.Desktop.Internal.GeoProcessing.RasterAnalysisAssistantExtension">
        <content searchDescription="This skill class focuses on hydrological tasks, such as creating conditioned DEMs and analyzing flow direction and accumulation, returning relevant tool Id for each task">
          <aiAssistantFunction name="ConditionDEM" searchDescription="serendipity serendipity"/>
          <!-- <aiAssistantFunction name="IdentifySinks" searchDescription="serendipity serendipity"/>
          <aiAssistantFunction name="FlowDirection" searchDescription="serendipity serendipity"/>
          <aiAssistantFunction name="FlowAccumulation" searchDescription="serendipity serendipity"/>
          <aiAssistantFunction name="DrainageBasins" searchDescription="serendipity serendipity"/>
          <aiAssistantFunction name="WatershedsDetermine" searchDescription="serendipity serendipity"/>
          <aiAssistantFunction name="StreamNetwork" searchDescription="serendipity serendipity"/>
          <aiAssistantFunction name="AssignStreamOrder" searchDescription="serendipity serendipity"/>
          <aiAssistantFunction name="AssignStreamLink" searchDescription="serendipity serendipity"/>
          <aiAssistantFunction name="CalculateFlowLength" searchDescription="serendipity serendipity"/>
          <aiAssistantFunction name="SnapPourPointsToStream" searchDescription="serendipity serendipity"/>
          <aiAssistantFunction name="StorageCapacity" searchDescription="serendipity serendipity"/>
          <aiAssistantFunction name="DownslopeDistance" searchDescription="serendipity serendipity"/> -->
        </content>
      </insertComponent>
    </updateCategory>
    <!-- AI Assistants Extensions -->

  </categories>

  <modules>
    <insertModule id="esri_geoprocessing_module" className="GeoprocessingModule" caption="Geoprocessing" description="Geoprocessing" autoLoad="false">
      <tabs>

        <tab id="esri_geoprocessing_GPContainer" caption="Manage" keytip="G" condition="esri_geoprocessing_container" tabGroupID="esri_geoprocessing_GPTabGroup">
          <group refID="esri_core_clipboardGroup"/>
          <group refID="esri_core_organize"/>
          <group refID="esri_geoprocessing_container_ProjectGroup"/>
          <group refID="esri_geoprocessing_container_CreateGroup"/>
        </tab>

        <tab id="esri_geoprocessing_ProNBTab" caption="Notebook" condition="esri_geoprocessing_proNotebookPane" insert="before" placeWith="esri_core_insertTab" keytip="B">
          <group refID="esri_python_savegroup"/>
        </tab>

        <tab id="esri_geoprocessing_MBDiagramTab1" caption="Diagram" condition="esri_geoprocessing_modelBuilderPane" keytip="MD" tabGroupID="esri_geoprocessing_MBTabGroup">
          <group refID="esri_modelbuilder_spacingGroup"/>
          <group refID="esri_modelbuilder_linkGroup" />
          <group refID="esri_modelbuilder_orientationGroup"/>
          <group refID="esri_modelbuilder_layoutGroup"/>
          <group refID="esri_modelbuilder_routingGroup"/>
          <group refID="esri_modelbuilder_labelTextFormatGroup"/>
          <!--<group refID="esri_modelbuilder_templateGroup"/>-->
        </tab>

        <tab id="esri_geoprocessing_MBHomeTab" caption="ModelBuilder" insert="before" placeWith="esri_core_insertTab" condition="esri_geoprocessing_anyMBPane" keytip="MB">
          <group refID="esri_modelbuilder_editGroup"/>
          <group refID="esri_modelbuilder_newModelGroup" />
          <group refID="esri_modelbuilder_viewGroup"/>
          <group refID="esri_modelbuilder_modeGroup"/>
          <group refID="esri_modelbuilder_runGroup"/>
          <group refID="esri_modelbuilder_insertGroup"/>
          <group refID="esri_modelbuilder_groupGroup"/>
          <group refID="esri_modelbuilder_messagesGroup"/>
        </tab>

        <!--<tab id="esri_geoprocessing_MBInsertTab" caption="Insert" condition="esri_geoprocessing_anyGPPane" keytip="N">
          <group refID="esri_core_projectData"/>
          <group refID="esri_core_favoritesData"/>
        </tab>-->

        <!--<tab id="esri_geoprocessing_MBAnalysisTab" caption="Analysis" condition="esri_geoprocessing_anyGPPane" keytip="A">
          <insertGroup refID="esri_geoprocessing_analysisTools"/>
          <insertGroup refID="esri_geoprocessing_ToolsGallery" />
          <insertGroup refID="esri_geoprocessing_portal" />
          <insertGroup refID="esri_geoprocessing_analysis2"/>
          <insertGroup refID="esri_datasourcesraster_rasterLayerTemplateGroup"/>
          <insertGroup refID="esri_geoprocessing_datainterop_group"/>
        </tab>-->
        <!--<tab id="esri_geoprocessing_MBViewTab" caption="View" condition="esri_geoprocessing_anyGPPane" keytip="V">
          <group refID="esri_core_dockWindows" />
        </tab>-->

      </tabs>
      
      <tabGroups>
        <tabGroup id="esri_geoprocessing_GPTabGroup" caption="Manage"/>
        <tabGroup caption="ModelBuilder" id="esri_geoprocessing_MBTabGroup" />
      </tabGroups>

      <groups>
        <group id="esri_geoprocessing_container_ProjectGroup" caption="Project">
          <button refID="esri_geoprocessing_Toolbox_MakeDefaultButton" size="large"/>
          <menu refID="esri_geoprocessing_insertMenu" size="large"/>
        </group>

        <group id="esri_geoprocessing_container_CreateGroup" caption="Create">
          <button refID="esri_geoprocessing_Add_ModelTool" size="large" />
          <button refID="esri_geoprocessing_Add_ScriptTool" size="large" />
          <button refID="esri_geoprocessing_Add_Toolset" size="large"/>
        </group>

        <group id="esri_modelbuilder_labelTextFormatGroup" caption="Format"  smallImage="TextSymbolGroup16">
          <toolbar refID="esri_modelbuilder_labelTextFontToolBar" />
        </group>
        <group id="esri_geoprocessing_analysisTools" caption="Geoprocessing" launcherButtonID="esri_geoprocessing_openGPOptionsButton" smallImage="GeoprocessingResultsFolder16" launcherKeytip="GO">
          <button refID="esri_geoprocessing_showToolHistory" size="large" />
          <button refID="esri_geoprocessing_modelBuilderButton" size="neverLarge" />
          <splitButton refID="esri_geoprocessing_PythonSplitButton" size="neverLarge" />
          <button refID="esri_geoprocessing_environmentsButton" size="neverLarge" />
          <customControl refID="esri_geoprocessingAnalysisButton_readytouse" size="neverMiddle"/>
          <splitButton refID="esri_geoprocessing_toolsButton" size="neverMiddle"/>
        </group>
        <group id="esri_geoprocessing_ToolsGallery" caption="Tools" smallImage="GeoprocessingResultsFolder16"  condition="esri_mapping_mapTypeLinkChartState_false">
          <gallery refID="esri_geoprocessingGallery" inline="true" />
        </group>
        <group id="esri_geoprocessing_portal" caption="Portal" smallImage="GeoprocessingTool16"  condition="esri_mapping_mapTypeLinkChartState_false">
          <customControl refID="esri_geoprocessingAnalysisButton_feature" size="neverMiddle"/>
          <customControl refID="esri_geoprocessingAnalysisButton_raster" size="neverMiddle"/>
        </group>
        <group id="esri_geoprocessing_analysis2" caption="Workflows" smallImage="GeoprocessingTool16">
          <button refID="esri_dataengineering_viewOutlineStatisticsButton" size="neverSmall"/>
          <!--<splitButton refID="esri_geoprocessing_suitabilityModelSplitButton" size="neverSmall"/>-->
          <button refID="esri_geoprocessing_suitabilityButton" size="neverSmall"/>
          <button refID="esri_defense_showVisibilityDockPane" size="neverSmall"/>
          <splitButton refID="esri_mapping_interactiveAnalysisSplitButton" size="neverSmall"/>
          <gallery refID="esri_mapping_addSimulationGallery" size="neverSmall"/>
          <!--buttonPalette refID="esri_mapping_simulationPalette"/>-->
          <gallery refID="esri_networkanalysis_transportation_AnalysisLayerFactoriesAndUpdateDefaultMapNetworkSource_Gallery" size="neverSmall"/>
          <button refID="esri_neighborhoodexplorer_mainButton" size="neverSmall"/>
          <button refID="esri_geostatistics_WizardButton" size="neverSmall"/>
          <customControl refID="esri_businessAnalystButton_baToolsDropdown" size="neverSmall"/>
          <toolPalette refID="esri_dataInterop_FMEToolPalette" size="neverSmall"/>
        </group>
        <group id="esri_modelbuilder_editGroup" caption="Clipboard" smallImage="EditPaste16" largeImage="EditPaste32">
          <button refID="esri_modelbuilder_pasteElementButton" size="large"/>
          <button refID="esri_modelbuilder_cutElementButton" size="middle" />
          <button refID="esri_modelbuilder_copyElementButton" size="middle" />
        </group>

        <group id="esri_modelbuilder_newModelGroup" caption="Model" launcherButtonID="esri_geoprocessing_openMBOptionsButton" largeImage="ModelBuilderNewModel32" smallImage="ModelBuilderNewModel16" >
          <button refID="esri_modelbuilder_NewModelButton" size="large" />
          <button refID="esri_modelbuilder_saveSplitButton" size="large" />
          <button refID="esri_modelbuilder_autoSaveButton" size="middle" />
          <button refID="esri_modelBuilder_editToolProperties" size="middle" />
          <button refID="esri_modelbuilder_environmentsButton" size="middle" />
          <button refID="esri_modelbuilder_exportSplitButton" size="middle" />
          <button refID="esri_modelbuilder_reportViewButton" size="middle" />
          <button refID="esri_modelbuilder_OpenToolInGPPane" size="middle" />
          <button refID="esri_modelbuilder_findAndReplace" size="large" />
        </group>
        <group id="esri_modelbuilder_viewGroup" caption="View" smallImage="ModelBuilderAutoLayout16" largeImage="ModelBuilderAutoLayout32">
          <button refID="esri_modelbuilder_autoLayoutDiagramButton" size="large" />
          <button refID="esri_modelbuilder_fitToWindowButton" size="large" />
          <button refID="esri_modelbuilder_prevExtentButton" size="small" />
          <button refID="esri_modelbuilder_nextExtentButton" size="small" />
        </group>
        <group id="esri_modelbuilder_runGroup" caption="Run" smallImage="GenericRun16" largeImage="GenericRun32">
          <button refID="esri_modelbuilder_validateButton" />
          <button refID="esri_modelbuilder_runModelButton" />
          <button refID="esri_modelbuilder_deleteIntermediateDataActualButton" />
        </group>
        <group id="esri_modelbuilder_modeGroup" caption="Mode" smallImage="ModelBuilderSelect16">
          <button refID="esri_modelbuilder_selectButton" size="middle" />
          <button refID="esri_modelbuilder_selectAllButton" size="middle" />
          <button refID="esri_modelbuilder_panButton" size="middle" />
        </group>
        <group id="esri_modelbuilder_insertGroup" caption="Insert" largeImage="GeoprocessingToolbox32" smallImage="GeoprocessingToolbox16">
          <button refID="esri_modelbuilder_createVariableButton" size="middle" />
          <button refID="esri_modelbuilder_createLabelButton" size="middle" />
          <button refID="esri_geoprocessing_toolsSplitButton" size="middle" />
          <button refID="esri_modelbuilder_toolSuggestionSplitButton" size="large" />
          <menu refID="esri_modelbuilder_iteratorPalette" />
          <menu refID="esri_modelbuilder_utilitiesPalette" />
          <menu refID="esri_modelbuilder_conditionalPalette" />
          <!--<menu refID="esri_modelbuilder_ListPalette" size="large" />-->
        </group>
        <group id="esri_modelbuilder_groupGroup" caption="Group" smallImage="ModelBuilderGroup16" largeImage="ModelBuilderGroup32">
          <button refID="esri_modelbuilder_groupButton" />
          <button refID="esri_modelbuilder_ungroupButton" />
        </group>

        <group id="esri_modelbuilder_messagesGroup" caption="Messages" smallImage="GeoprocessingResultsFolderOpenState16" largeImage="GeoprocessingResultsFolderOpenState32">
          <button refID="esri_modelbuilder_progressorOpen" size="large" />
        </group>

        <!--<group id="esri_geoprocessing_datainterop_group" caption="Interoperability" smallImage="FME_WB16" largeImage="FME_WB32">

          </group>-->
        <group id="esri_modelbuilder_spacingGroup" caption="Spacing" sizePriorities="medium" largeImage="ModelBuilderNewModel32" smallImage="ModelBuilderNewModel16" condition="esri_geoprocessing_modelBuilderPane">
          <labelControl refID="esri_modelbuilder_elementLabel"  size="small"/>
          <labelControl refID="esri_modelbuilder_levelLabel"  size="small"/>
          <labelControl refID="esri_modelbuilder_subGraphLabel"  size="small"/>
          <customControl refID="esri_modelbuilder_elementSlider" size="small"/>
          <customControl refID="esri_modelbuilder_levelSlider" size="small"/>
          <customControl refID="esri_modelbuilder_subGraphSlider"  size="small"/>
        </group>
        
        <group id="esri_modelbuilder_linkGroup" caption="Links" largeImage="ModelBuilderNewModel32" smallImage="ModelBuilderNewModel16" condition="esri_geoprocessing_modelBuilderPane">
          <toolPalette refID="esri_modelbuilder_linkShapeToolPalette" size="large"/>
          <toolbar refID="esri_modelbuilder_links"/>

        </group>

        <group id="esri_modelbuilder_orientationGroup" caption="Orientation" largeImage="ModelBuilderNewModel32" smallImage="ModelBuilderNewModel16" condition="esri_geoprocessing_modelBuilderPane" >
          <comboBox refID="esri_modelbuilder_LayoutType" size="large" />
          <button refID="esri_modelbuilder_FlipTextDirection" size="large"/>
        </group>

        <group id="esri_modelbuilder_layoutGroup" caption="Layout" largeImage="ModelBuilderNewModel32" smallImage="ModelBuilderNewModel16" condition="esri_geoprocessing_modelBuilderPane">
          <button refID="esri_modelbuilder_autoLayoutDiagramButton" separator="true"/>

          <comboBox refID="esri_modelbuilder_subGraphPlacement" size="large"/>
          <comboBox refID="esri_modelbuilder_groupGraphPlacement" size="large"/>
        </group>

        <group id="esri_modelbuilder_routingGroup" caption="Routing" largeImage="ModelBuilderNewModel32" smallImage="ModelBuilderNewModel16" condition="esri_geoprocessing_modelBuilderPane">
          <button refID="esri_modelbuilder_useGridRouter" size="small" />
          <button refID="esri_modelbuilder_showGridCheckbox" size="small" />
          <menu refID="esri_modelbuilder_gridSettingsPallette" size="large"/>
        </group>

        <group id="esri_python_savegroup" caption="Notebook" condition="esri_geoprocessing_proNotebookPane">
          <button refID="esri_pnotebook_New" size="large"/>
          <button refID="esri_pnotebook_Save" size="large"/>
          <button refID="esri_pnotebook_Saveas" size="large"/>
          <splitButton refID="esri_pnotebook_Export" size ="large"/>
          <button refID="esri_pnotebook_Interrupt" size="large"/>
        </group>

        <group id="esri_geoprocessing_cadToolsGroup" caption="Tools" extendedCaption="Geoprocessing tools for CAD files" smallImage="GeoprocessingTool16">
          <gallery refID="esri_cadToolsGallery" inline="true"/>
        </group>

        <!-- Bim Tool Gallery Group -->
        <group id="esri_geoprocessing_bimToolsGroup" caption="Tools" extendedCaption="Geoprocessing tools for BIM files" smallImage="GeoprocessingTool16" condition="esri_mapping_onlyBimCategoryLayerSelectedCondition">
          <gallery refID="esri_bimToolsGallery" inline="true"/>
        </group>
        
        <!-- Linear Referencing Group -->
        <group id="esri_geoprocessing_linearReferencingGroup" caption="Analysis" launcherButtonID="esri_geoprocessing_openGPOptionsButton" launcherKeytip="GO"
               smallImage="GeoprocessingTool16">
          <subgroup refID="esri_geoprocessing_linearReferencingSubgroup"/>
        </group>

        <!-- Create points from standalone table group -->
        <group id="esri_geoprocessing_makePointsGroup" caption="Create Points">
          <menu refID="esri_geoprocessing_StandaloneCreatePointsFromTableRibbonMenu"/>
        </group>
      </groups>

      <subgroups>
        <!-- Linear Referencing Subgroups -->
        
        <subgroup id="esri_geoprocessing_linearReferencingSubgroup">
          <button refID="esri_linearReferencing_toolsSplitButton"/>
        </subgroup>
      </subgroups>

      <toolbars>
        <toolbar id="esri_modelbuilder_labelTextFontToolBar">
          <group>
            <comboBox refID="esri_modelbuilder_labelTextFontFamilyComboBox" />
            <comboBox refID="esri_modelbuilder_labelTextFontSizeComboBox" size="small" />
          </group>
          <group>
            <customControl refID="esri_modelbuilder_labelTextColorGallery" size="small" />
            <button refID="esri_modelbuilder_labelTextBoldButton" size="small" />
            <button refID="esri_modelbuilder_labelTextItalicButton" size="small" />
            <button refID="esri_modelbuilder_labelTextUnderlineButton" size="small" />
            <button refID="esri_modelbuilder_labelTextILeftJustification" size="small" />
            <button refID="esri_modelbuilder_labelTextCenterJustification" size="small" />
            <button refID="esri_modelbuilder_labelTextRightJustification" size="small" />
            <button refID="esri_modelbuilder_labelTextIncreaseSizeButton" size="small" />
            <button refID="esri_modelbuilder_labelTextDecreaseSizeButton" size="small" />
          </group>
        </toolbar>
        
        <toolbar id="esri_modelbuilder_links">
          <group>
            <button refID="esri_modelbuilder_enforceLinkFlow" />
          </group>
          <group>
            <button refID="esri_modelbuilder_straightenLongLinks" />
          </group>
          <group>
            <button refID="esri_modelbuilder_orthogonalLinks" />
          </group>
        </toolbar>
      </toolbars>
      <controls>

        <checkBox id="esri_modelbuilder_enforceLinkFlow" className="MBLinkStatusEnforceLinkFlow" isChecked="{Binding Settings.EnforceLinkFlow}" keytip="LF" caption="Enforce Link Flow" loadOnClick="false"   condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Enforce Link Flow">
            Checked - The layout attempts to force all links to point to the same direction. If the arranged graph contains cycles, some of the links will still point backward.
            <disabledText></disabledText>
          </tooltip>
        </checkBox>

        <checkBox id="esri_modelbuilder_straightenLongLinks" className="MBLinkStatusStraightenLongLinks" keytip="LL" caption="Straighten Long Links" loadOnClick="false"  condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Straighten Long Links">
            Checked - The layout strives to straighten links that cross more than one layer of the graph. Because of the additional horizontal alignment constraints, the arranged graph usually occupies a larger area than when this setting is unchecked or False.
            <disabledText></disabledText>
          </tooltip>
        </checkBox>

        <checkBox id="esri_modelbuilder_orthogonalLinks" className="MBLinkStatusOrthogonalLinks"  keytip="OL" caption="Orthogonal Links" loadOnClick="false" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Orthogonal Links">
            Checked - The connectors will follow orthogonal routes.
            <disabledText></disabledText>
          </tooltip>
        </checkBox>
        <checkBox id="esri_modelbuilder_showGridCheckbox" className="MBShowGrid" keytip="SG" caption="Show Grid" loadOnClick="false" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Show Grid">
            Checked - The Layout shows reference grid.
            <disabledText></disabledText>
          </tooltip>
        </checkBox>
        <checkBox id="esri_modelbuilder_useGridRouter" className="MBUseGridRouter" keytip="GR" caption="Use Grid Router" loadOnClick="false"  condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Use Grid Router">
            Checked - The layout uses a grid router to arrange model elements. This may slow layout performance depending on the number of nodes, connectors and the size of the model.
            Unchecked - The layout uses the default Quick Router. This is good for most of the cases and layout performance is faster. This is the recommended setting.
            <disabledText></disabledText>
          </tooltip>
        </checkBox>

        <customControl id="esri_modelbuilder_gridRouterSettings" caption="Grid Size" keytip="GS" className="ArcGIS.Desktop.Internal.GeoProcessing.ModelBuilder.Ribbon.GridRouterSettingsViewModel" extendedCaption="Set the value is used to set the size of the routing grid." loadOnClick="false" disableIfBusy="false"  condition="esri_geoprocessing_modelBuilderPane">
          <content className="ArcGIS.Desktop.Internal.GeoProcessing.Controls.GridRouterSettingsControl"/>
          <tooltip heading="Grid Size">
            The value is used to set the size of the routing grid. A smaller grid size will generate better results, but might slow layout performance depending on the number of nodes, connectors and the size of the model.<disabledText></disabledText>
          </tooltip>
        </customControl>


        <labelControl id="esri_modelbuilder_elementLabel" caption="Element" hasTwoLines="false" smallImage="ModelBuilderLinkBezier16" largeImage="ModelBuilderLinkBezier32" />
        <customControl id="esri_modelbuilder_elementSlider" caption="Element" keytip="ES" extendedCaption="Set distance between tools, variables, connectors and groups."
           className="ArcGIS.Desktop.Internal.GeoProcessing.ModelBuilder.Ribbon.ElementSlider" loadOnClick="false" disableIfBusy="false" >

          <content className="ArcGIS.Desktop.Internal.GeoProcessing.Controls.CustomSlider" loadOnClick="false"/>
          <tooltip heading="Element Distance">
            The distance between tools, variables, connectors and groups.<disabledText></disabledText>
          </tooltip>
        </customControl>

        <labelControl id="esri_modelbuilder_levelLabel" caption="Level" hasTwoLines="false" />
        <customControl id="esri_modelbuilder_levelSlider" caption="Level" keytip="LS" extendedCaption="Set distance between stacked tools, variables and groups in the same graph."
           className="ArcGIS.Desktop.Internal.GeoProcessing.ModelBuilder.Ribbon.LevelSlider" loadOnClick="false" disableIfBusy="false">
          <content className="ArcGIS.Desktop.Internal.GeoProcessing.Controls.CustomSlider" loadOnClick="false"/>
          <tooltip heading="Level Distance">
            The distance between stacked tools, variables and groups in the same graph.<disabledText></disabledText>
          </tooltip>
        </customControl>

        <labelControl id="esri_modelbuilder_subGraphLabel" caption="Sub-Graph" hasTwoLines="false" />
        <customControl id="esri_modelbuilder_subGraphSlider" caption="Element" keytip="SS" extendedCaption="Set distance between unconnected graphs."
          className="ArcGIS.Desktop.Internal.GeoProcessing.ModelBuilder.Ribbon.SubGraphSlider" loadOnClick="false" disableIfBusy="false" >
          <content className="ArcGIS.Desktop.Internal.GeoProcessing.Controls.CustomSlider" loadOnClick="false"/>
          <tooltip heading="Sub-Graph Distance">
            The distance between separate or unconnected graphs.<disabledText></disabledText>
          </tooltip>
        </customControl>

        <button id="esri_modelbuilder_bezierLinkTypeButton" hidden="true" caption="Bezier" keytip="B" extendedCaption="Link Type" className="MBLinkShapeStatusBezier" loadOnClick="false" helpContextID="" smallImage="ModelBuilderLinkBezier16" largeImage="ModelBuilderLinkBezier32" condition="esri_geoprocessing_modelBuilderPane" >
          <tooltip heading="Bezier" >
            The shape of the connector used while drawing, or after Auto Layout is applied. The connector segments will be Bezier curves.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_cascadeLinkTypeButton" hidden="true" caption="Cascade" keytip="c" extendedCaption="Link Type" className="MBLinkShapeStatusCascade" loadOnClick="false" helpContextID="" smallImage="ModelBuilderLinkCascade16" largeImage="ModelBuilderLinkCascade32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Cascade" >
            The connector segments will be horizontal and vertical lines, each one orthagonal to its adjacent segments.
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_polylineLinkTypeButton" hidden="true" caption="Polyline" keytip="P" extendedCaption="Link Type" className="MBLinkShapeStatusPolyline" loadOnClick="false" helpContextID="" smallImage="ModelBuilderLinkPolyline16" largeImage="ModelBuilderLinkPolyline32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Polyline" >
            The connector segments will be straight lines.
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_splineLinkTypeButton" hidden="true" caption="Spline" keytip="S" extendedCaption="Link Type" className="MBLinkShapeStatusSpline" loadOnClick="false" helpContextID="" smallImage="ModelBuilderLinkSpline16" largeImage="ModelBuilderLinkSpline32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Spline" >
            The connector segments will be curves forming an interpolating spline that passes through all control points.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <comboBox id="esri_modelbuilder_LayoutType" keytip="DF" className="ArcGIS.Desktop.GeoProcessing.MBLayout" condition="esri_geoprocessing_modelBuilderPane" noEdit="false" resizable="true" itemWidth="160" rows="20" loadOnClick="false">
          <tooltip heading="Layout type">
            Layout Orientation<disabledText></disabledText>
          </tooltip>
        </comboBox>

        <checkBox id="esri_modelbuilder_FlipTextDirection" hidden="true" caption="Flip Flow Direction" extendedCaption="Flip Flow Direction" keytip="FD" className="MBOrientationFlipTextDirection" loadOnClick="false" helpContextID="" smallImage="ModelBuilderFlipText16" largeImage="ModelBuilderFlipText32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Flip Flow Direction" >
            Checked - The layout's flow and text direction will be set in a Right to Left reading pattern.
            <disabledText></disabledText>
          </tooltip>
        </checkBox>

        <labelControl id="esri_modelbuilder_subGraphPlacementLabel" caption="Sub-Graph" hasTwoLines="false" />
        <comboBox id="esri_modelbuilder_subGraphPlacement" caption="Sub-Graph" keytip="SL" className="ArcGIS.Desktop.GeoProcessing.MBSubGraph" condition="esri_geoprocessing_modelBuilderPane" noEdit="false" resizable="true" itemWidth="160" rows="20" loadOnClick="false">
          <tooltip heading="Sub-Graph">
            The layout of independent graphs in a model.<disabledText></disabledText>
          </tooltip>
        </comboBox>
        <labelControl id="esri_modelbuilder_groupGraphPlacementLabel" caption="Group-Graph" hasTwoLines="false" />
        <labelControl id="esri_modelbuilder_LayoutTypeLabel" caption="Layout" hasTwoLines="false" />

        <comboBox id="esri_modelbuilder_groupGraphPlacement" caption="Group-Graph" keytip="GL" className="ArcGIS.Desktop.GeoProcessing.MBSubGraphInGroups" condition="esri_geoprocessing_modelBuilderPane" noEdit="false" resizable="true" itemWidth="160" rows="20" loadOnClick="false">
          <tooltip heading="Group-Graph (in group)" >
            The layout of independent graphs within a group in a model.<disabledText></disabledText>
          </tooltip>
        </comboBox>
        <customControl id="esri_modelbuilder_labelTextColorGallery" keytip="TC" caption="" extendedCaption="Text color of the layout elements." className="Commands+TextColorPickerViewModel" loadOnClick="false">
          <content assembly="Extensions\Mapping\ArcGIS.Desktop.Mapping.dll" className="ArcGIS.Desktop.Internal.Mapping.Symbology.ColorPicker" />
          <tooltip heading="Text Color">
            Change the text color of the selected layout elements.<disabledText></disabledText>
          </tooltip>
        </customControl>
        <comboBox id="esri_modelbuilder_labelTextFontFamilyComboBox" keytip="FT" className="Commands+FontFamilyChoices" condition="esri_geoprocessing_modelBuilderPane" noEdit="false" resizable="true" itemWidth="160" rows="20" loadOnClick="false">
          <tooltip heading="Text Symbol Font">
            Choose the font name for the current label.<disabledText></disabledText>
          </tooltip>
        </comboBox>
        <comboBox id="esri_modelbuilder_labelTextFontSizeComboBox" caption="Font Size" keytip="FS" extendedCaption="Choose font size for an element in the layout." className="Commands+FontFamilySizes" condition="esri_geoprocessing_modelBuilderPane" textSearchEnabled="false" noEdit="false" resizable="false" itemWidth="40" rows="20" helpContextID="" loadOnClick="false">
          <tooltip heading="Label Font Size">
            Choose a font size for the selected elements.<disabledText></disabledText>
          </tooltip>
        </comboBox>
        <button id="esri_modelbuilder_labelTextIncreaseSizeButton" caption="Increase Font Size" keytip="IL" extendedCaption="Increase font size of the selected layout element." className="esri_geoprocessing_module:Commands.FontSizeIncrementButton" smallImage="TextSymbolIncreaseSize16" loadOnClick="false" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Increase Font Size">
            Increase the font size of the selected elements.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_labelTextDecreaseSizeButton" caption="Decrease Font Size" keytip="DL" extendedCaption="Decrease font size of the selected layout element." className="esri_geoprocessing_module:Commands.FontSizeDecrementButton" smallImage="TextSymbolDecreaseSize16" loadOnClick="false" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Decrease Font Size">
            Decrease the font size of the selected elements.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_labelTextItalicButton" caption="Italics" keytip="I" extendedCaption="Apply italic formatting to a layout element." className="FontItalicButton" smallImage="TextSymbolItalic16" loadOnClick="false" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Italics">
            Apply italic formatting to the selected elements.<disabledText>Available if the current font has a italic style.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_labelTextBoldButton" caption="Bold" keytip="B" extendedCaption="Apply bold formatting to a layout element." className="FontBoldButton" smallImage="TextSymbolBold16" loadOnClick="false" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Bold">
            Apply bold formatting to the selected elements.<disabledText>Available if the current font has a bold style.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_labelTextUnderlineButton" caption="Underline" keytip="U" extendedCaption="Apply underline formatting to a layout element." className="FontUnderlineButton" smallImage="TextSymbolUnderline16" loadOnClick="false" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Underline">
            Apply underline formatting to the selected elements.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_labelTextILeftJustification" caption="Left" keytip="LJ" extendedCaption="Apply Left Justification formatting to a layout element." className="FontLeftJustifyButton" smallImage="TextSymbolTextAlignLeft16" loadOnClick="false" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Left Justification">
            Apply Left Justification formatting to the selected elements.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_labelTextCenterJustification" caption="Center" keytip="CJ" extendedCaption="Apply Center Justification formatting to a layout element." className="FontCenterJustifyButton" smallImage="TextSymbolTextAlignHorizontalCenter16" loadOnClick="false" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Center Justification">
            Apply Center Justification formatting to the selected elements.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_labelTextRightJustification" caption="Right" keytip="RJ" extendedCaption="Apply Right Justification formatting to a layout element." className="FontRightJustifyButton" smallImage="TextSymbolTextAlignRight16" loadOnClick="false" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Right Justification">
            Apply Right Justification formatting to the selected elements.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_addToolboxButton" className="esri_geoprocessing_module:Commands.AddToolbox" caption="Add Toolbox" largeImage="GeoprocessingToolboxAdd32" smallImage="GeoprocessingToolBoxAdd16" helpContextID="" keytip="AT">
          <tooltip heading="">
            Add an existing toolbox to the project. The project saves a reference to the toolbox.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_newToolboxButton" className="esri_geoprocessing_module:Commands.NewToolbox" caption="New Legacy Toolbox (.tbx)" largeImage="GeoprocessingToolboxNew32" smallImage="GeoprocessingToolboxNew16" helpContextID="" keytip="TB" isCustomCommand="true">
          <args>
            <browse_filter>esri_browseDialogFilters_toolboxes</browse_filter>
            <browse_title>New Legacy Toolbox</browse_title>
            <default_extension>tbx</default_extension>
          </args>
          <tooltip heading="">
            Create a new legacy toolbox. The project saves a reference to the toolbox.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_newToolboxButton_atbx" className="esri_geoprocessing_module:Commands.NewToolbox" caption="New Toolbox (.atbx)" largeImage="GeoprocessingToolboxNew32" smallImage="GeoprocessingToolboxNew16" helpContextID="" keytip="TA" isCustomCommand="true">
          <args>
            <browse_filter>esri_browseDialogFilters_toolboxes_atbx</browse_filter>
            <browse_title>New Toolbox</browse_title>
            <default_extension>atbx</default_extension>
          </args>
          <tooltip heading="">
            Create a new toolbox. The project saves a reference to the toolbox.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_newPythonToolboxButton" className="esri_geoprocessing_module:Commands.NewToolbox" caption="New Python Toolbox" largeImage="GeoprocessingToolboxPythonNew32" smallImage="GeoprocessingToolboxPythonNew16" helpContextID="" keytip="P" isCustomCommand="true">
          <args>
            <browse_filter>esri_browseDialogFilters_toolboxes_pyt</browse_filter>
            <browse_title>New Python Toolbox</browse_title>
            <default_extension>pyt</default_extension>
          </args>
          <tooltip heading="">
            Create a new Python toolbox. The project saves a reference to the toolbox.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_modelBuilderButton" className="esri_geoprocessing_module:Commands.NewModel"
                caption="ModelBuilder" keytip="NM"
                largeImage="GeoprocessingModel32"
                smallImage="GeoprocessingModel16"
                helpContextID="120003888">
          <tooltip heading="ModelBuilder">
            Create a new blank geoprocessing model and open it in ModelBuilder.

A model is a geoprocessing workflow represented as a diagram that strings together a sequence of tools, using the output of one tool as the input to another tool. You create, edit, and manage models in ModelBuilder.<disabledText>Not available yet.</disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_pythonButton" caption="Python Window" keytip="PY" className="esri_geoprocessing_module:Commands.ShowPythonWindow" largeImage="GeoprocessingPythonWindowShow32" smallImage="GeoprocessingPythonWindowShow16" helpContextID="" disableIfBusy="false">
          <tooltip heading="Python Window">
            Show the Python window.

The Python window is an interpreter in which you can execute geoprocessing tools, ArcPy functions, and any other Python functionality.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_toolsButton" className="esri_geoprocessing_module:Commands.ShowTools" caption="Tools" extendedCaption="Open Geoprocessing pane" keytip="TS"
          largeImage="GeoprocessingToolbox32"
          smallImage="GeoprocessingToolbox16"
          helpContextID="" disableIfBusy="false">
          <tooltip heading="Geoprocessing">
            Show the Geoprocessing pane.

You can search for a specific tool, see a list of favorite and recently run tools, and explore all tools and toolboxes that are included in ArcGIS Pro.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_toolsViewTabButton" className="esri_geoprocessing_module:Commands.ShowTools" caption="Geoprocessing" extendedCaption="Open Geoprocessing pane" keytip="TS"
          largeImage="GeoprocessingToolbox32"
          smallImage="GeoprocessingToolbox16"
          helpContextID="" disableIfBusy="false">
          <tooltip heading="Geoprocessing">
            Show the Geoprocessing pane.

You can search for a specific tool, see a list of favorite and recently run tools, and explore all tools and toolboxes that are included in ArcGIS Pro.<disabledText></disabledText>
          </tooltip>
        </button>

        <customControl id="esri_geoprocessing_toolsSearch" keytip="TS" caption="Tools"
                       className="ArcGIS.Desktop.GeoProcessing.ToolSearcherViewModel"
                       disableIfBusy="false" loadOnClick="true"
                       assembly="Extensions\Geoprocessing\ArcGIS.Desktop.GeoProcessing.dll" helpContextID="">
          <content className="ArcGIS.Desktop.GeoProcessing.ToolSearcherView"/>
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </customControl>
        <customControl id="esri_modelbuilder_toolsSuggestion" keytip="ST" caption="Suggest Tool"
                       className="ArcGIS.Desktop.GeoProcessing.ToolSearcherViewModel"
                       disableIfBusy="false" loadOnClick="true"
                       assembly="Extensions\Geoprocessing\ArcGIS.Desktop.GeoProcessing.dll" helpContextID="" condition="esri_geoprocessing_ToolSuggestions_installed">
          <content className="ArcGIS.Desktop.GeoProcessing.ToolSuggestView"/>
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </customControl>
        <customControl id="esri_modelbuilder_toolsRibbonSuggestion" keytip="ST" caption="Suggest Tool"
                       className="ArcGIS.Desktop.GeoProcessing.ToolSearcherViewModel"
                       disableIfBusy="false" loadOnClick="true"
                       assembly="Extensions\Geoprocessing\ArcGIS.Desktop.GeoProcessing.dll" helpContextID="" condition="esri_geoprocessing_ToolSuggestions_installed">
          <content className="ArcGIS.Desktop.GeoProcessing.ToolSuggestRibbonView"/>
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </customControl>

        <button id="esri_geoprocessing_toolsButtonFromGallery" hidden="true" className="esri_geoprocessing_module:Commands.ShowTools" caption="More Tools..." keytip="MT" extendedCaption="Open Geoprocessing pane" helpContextID="">
          <tooltip heading="Geoprocessing">
            Show the Geoprocessing pane.

You can search for a specific tool, see a list of favorite and recently run tools, and explore all tools and toolboxes that are included in ArcGIS.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_CustomizeGalleryButton" hidden="true" className="esri_geoprocessing_module:Commands.CustomizeGallery" caption="Customize..." keytip="CU" extendedCaption="Analysis gallery" helpContextID="">
          <tooltip heading="Customize Analysis Gallery">
            Add groups, reorder, or remove the tools in the Analysis gallery.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_environmentsButton" className="esri_geoprocessing_module:Commands.ShowEnvironments" caption="Environments" extendedCaption="Geoprocessing" keytip="E" smallImage="GeoprocessingEnvironmentSettings16" largeImage="GeoprocessingEnvironmentSettings32" helpContextID="120003890">
          <tooltip heading="Geoprocessing Environments">
            Specify the environment settings that geoprocessing tools should use.

These settings apply to all geoprocessing tools that honor the environment settings. Environments set here are saved with the project.

Common environments include: extent, output coordinate system, cell size, and raster analysis mask.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_EditSchedule" hidden="true" caption="Edit Schedule" extendedCaption="Schedule Properties" className="esri_geoprocessing_module:Commands.OnEditSchedule" smallImage="GenericPencil16" helpContextID="">
          <tooltip heading="">
            Edit existing schedule<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_runNow" hidden="true" caption="Run Now" extendedCaption="Run scheduled task" className="esri_geoprocessing_module:Commands.OnRunScheduleNow" smallImage="SchedulerRunNow16" helpContextID="">
          <tooltip heading="">
            Run the scheduled task now<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_PauseSchedule" hidden="true" caption="Pause Schedule" extendedCaption="Pause scheduled task" className="esri_geoprocessing_module:Commands.OnPauseSchedule" smallImage="Pause16" helpContextID="">
          <tooltip heading="">
            Pause/Disable the current scheduled task<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_UnPauseSchedule" hidden="true" caption="Resume Schedule" extendedCaption="Resume scheduled task" className="esri_geoprocessing_module:Commands.OnUnPauseSchedule" smallImage="Play16" helpContextID="">
          <tooltip heading="">
            Continue/Enable running the scheduled task<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_DeleteSchedule" hidden="true" caption="Delete Schedule" extendedCaption="Delete schedule" className="esri_geoprocessing_module:Commands.OnDeleteSchedule" smallImage="GenericDeleteRed16" helpContextID="">
          <tooltip heading="">
            Deletes the scheduled task including the logs<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_ClearLogs" hidden="true" caption="Clear Run Log" extendedCaption="Clear scheduled task logs" className="esri_geoprocessing_module:Commands.OnClearScheduleLogs" smallImage="SchedulerEraseNow16" helpContextID="">
          <tooltip heading="">
            Clear all the logs of the scheduled task<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_openToolHistory" hidden="true" caption="Open" extendedCaption="Geoprocessing tool from History" className="esri_geoprocessing_module:Commands.OnOpenToolHistory" helpContextID="">
          <tooltip heading="">
            Open the tool with the same parameters and settings that were previously used.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_showResult" disableIfBusy="false" hidden="true" caption="View Details" extendedCaption="Geoprocessing Result Dialog" className="esri_geoprocessing_module:Commands.OnOpenResultDialog" helpContextID="">
          <tooltip heading="">
            Open a window to display the details of a geoprocessing history item.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_runToolFromHistory" hidden="true" caption="Run" extendedCaption="Geoprocessing tool from History" className="esri_geoprocessing_module:Commands.OnRunToolFromHistory" helpContextID="">
          <tooltip heading="">
            Re-run the tool with the same parameters and settings that were previously used.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_copy_snippetHistory" hidden="true" caption="Copy Python Command" className="esri_geoprocessing_module:Commands.OnCopySnippetToolHistory" helpContextID="">
          <tooltip heading="">
            Copy Python code that executes the tool to the clipboard.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_sendToPythonWindow" hidden="true" caption="Send To Python window" className="esri_geoprocessing_module:Commands.OnSendSnippetPythonWindow" helpContextID=""
                smallImage="GeoprocessingPythonWindowShow16">
          <tooltip heading="">
            Send Python code that executes the tool to the Python window.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_sendToNotebook" hidden="false" caption="Send To Notebook" className="esri_geoprocessing_module:Commands.OnSendSnippetNotebook" helpContextID=""
           smallImage="PythonNotebook16">
          <tooltip heading="">
            Send Python code that executes the tool to an active Notebook.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_makePythonScript" hidden="true" caption="Save As Python Script" className="esri_geoprocessing_module:Commands.OnMakePythonScript" helpContextID=""
            smallImage="GeoprocessingScript16">
          <tooltip heading="">
            Save As Python Script.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_removeToolHistory" hidden="true" caption="Remove" extendedCaption="Geoprocessing tool from History" className="esri_geoprocessing_module:Commands.OnRemoveToolHistory" smallImage="GenericDeleteRed16" helpContextID="">
          <tooltip heading="">
            Remove from the project Geoprocessing History.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_removeErrorsFromToolHistory" hidden="true" caption="Remove Items With Errors" extendedCaption="Geoprocessing History" className="esri_geoprocessing_module:Commands.OnRemoveErrorsFromToolHistory" smallImage="GenericDeleteRed16" helpContextID="">
          <tooltip heading="">
            Remove entries from the project Geoprocessing History which did not complete successfully.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_clearToolHistory" hidden="true" caption="Clear History" extendedCaption="Geoprocessing History" className="esri_geoprocessing_module:Commands.OnClearToolHistory" helpContextID="">
          <tooltip heading="">
            Clear the project Geoprocessing History.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_showToolHistory" caption="History" extendedCaption="Open Geoprocessing History" keytip="H" className="esri_geoprocessing_module:Commands.ShowToolHistory" largeImage="GeoprocessingHistory32" smallImage="GeoprocessingHistory16" helpContextID="120002446">
          <tooltip heading="Geoprocessing History">
            The geoprocessing tools you run are logged, and the list of tools is saved with your project Geoprocessing History. You can view detailed information about each tool that was run and reopen them with the same settings you previously used.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_openTool" hidden="true" caption="Open" extendedCaption="Geoprocessing tool" className="esri_geoprocessing_module:Commands.OnOpenTool" helpContextID="">
          <tooltip heading="">
            Open the tool.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_deleteTool" hidden="true" loadOnClick="false"
                caption="Delete" extendedCaption="Geoprocessing tool"
                className="esri_geoprocessing_module:Commands.OnDeleteToolboxComponent"
                helpContextID="" smallImage="GenericDiscard16">
          <tooltip heading="">
            Delete the tool from the toolbox. The tool is permanently deleted.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_deleteToolset" hidden="true" caption="Delete" extendedCaption="Geoprocessing toolset"
                className="esri_geoprocessing_module:Commands.OnDeleteToolboxComponent" loadOnClick="false"
                helpContextID="" smallImage="GenericDiscard16">
          <tooltip heading="">
            Delete the toolset from the toolbox. The toolset and all of the tools it contains are permanently deleted.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_deleteToolbox" hidden="true" caption="Delete" extendedCaption="Geoprocessing toolbox"
                className="esri_geoprocessing_module:Commands.OnDeleteToolboxComponent" loadOnClick="false"
                helpContextID="" condition="esri_projectItem_DefaultToolboxSelectedCondition"
                smallImage="GenericDiscard16">
          <tooltip heading="">
            Delete the toolbox. The toolbox is permanently deleted.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_openGPOptionsButton" caption="Geoprocessing Options" className="esri_geoprocessing_module:Commands.ShowGPOptions">
          <tooltip heading="Geoprocessing Options">
            Set geoprocessing options.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_openMBOptionsButton" caption="Modelbuilder Options" className="esri_geoprocessing_module:Commands.ShowMBOptions">
          <tooltip heading="Modelbuilder Options">
            Set modelbuilder options.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_addToFavorities" hidden="true" caption="Add To Project Favorites" extendedCaption="Add geoprocessing tool to Favorites in Geoprocessing pane" className="esri_geoprocessing_module:Commands.OnAddToFavorities" helpContextID="">
          <tooltip heading="">
            Add this tool to the Favorites section in the Geoprocessing pane.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_addToUserFavorities" hidden="true" caption="Add To My Favorites" extendedCaption="Add geoprocessing tool to User Favorites in Geoprocessing pane" className="esri_geoprocessing_module:Commands.OnAddToUserFavorities" helpContextID="">
          <tooltip heading="">
            Add this tool to the User Favorites section in the Geoprocessing pane.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_addToHistoryFavorities" hidden="true" caption="Add To History Favorites"
                extendedCaption="Add tool to Favorites in Geoprocessing history pane"
                className="esri_geoprocessing_module:Commands.OnAddToHistoryFavorities" helpContextID=""
                smallImage="FavoriteStar16">
          <tooltip heading="">
            Add this tool to the Favorites section in Geoprocessing History.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_removefromHistoryFavorities" hidden="true" caption="Remove From History Favorites"
                extendedCaption="Remove tool from favorites in Geoprocessing history pane"
                className="esri_geoprocessing_module:Commands.OnRemoveFromHistoryFavorities" helpContextID=""
                smallImage="FavoriteStarRemove16">
          <tooltip heading="">
            Remove this tool from the Favorites section in Geoprocessing History. <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_addToGallery" hidden="true" caption="Add To Analysis Gallery" className="esri_geoprocessing_module:Commands.OnAddToGallery" helpContextID="">
          <tooltip heading="">
            Add this tool to the Analysis gallery.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_addToModelBuilder" hidden="true" loadOnClick="false" caption="Add To Model" className="esri_geoprocessing_module:Commands.OnAddToModelBuilder" helpContextID="">
          <tooltip heading="">
            Add this tool to the Current Model<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_removeFromFavorities" hidden="true" caption="Remove" extendedCaption="Remove tool from Geoprocessing pane Favorites" className="esri_geoprocessing_module:Commands.OnRemoveFromFavorities" smallImage="GenericDeleteRed16" helpContextID="">
          <tooltip heading="">
            Remove the tool from Favorites.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_removeFromUserFavorities" hidden="true" caption="Remove" extendedCaption="Remove tool from Geoprocessing pane User Favorites" className="esri_geoprocessing_module:Commands.OnRemoveFromUserFavorities" smallImage="GenericDeleteRed16" helpContextID="">
          <tooltip heading="">
            Remove the tool from User Favorites.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_removeFromGallery" hidden="true" caption="Remove" extendedCaption="Remove tool from Analysis gallery" className="esri_geoprocessing_module:Commands.OnRemoveFromGallery" smallImage="GenericDeleteRed16" helpContextID="">
          <tooltip heading="">
            Remove the tool from the Analysis gallery.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_HideMainProgressor" hidden="true" caption="Clear Progress" extendedCaption="Close Progress" className="esri_geoprocessing_module:Commands.OnHideMainProgressor" smallImage="" helpContextID="">
          <tooltip heading="">
            Clear Progress from the Geoprocessing pane.<disabledText></disabledText>
          </tooltip>
        </button>
        <button className="esri_geoprocessing_module:Commands.OnLocateTool" id="esri_geoprocessing_locateTool" hidden="true" caption="Locate" extendedCaption="Locate Geoprocessing Tool">
          <tooltip heading="">
            Go to the toolbox location of the tool.<disabledText>The tool cannot be located in the Geoprocessing pane.</disabledText>
          </tooltip>
        </button>
        <button className="esri_geoprocessing_module:Commands.OnEditTool" id="esri_geoprocessing_editTool" caption="Edit" extendedCaption="Geoprocessing script or model tool">
          <tooltip heading="">
            Edit the source of the tool.<disabledText>This tool cannot be edited. If you wish to edit a Python Toolbox tool you must right-click and Edit the Python Toolbox.</disabledText>
          </tooltip>
        </button>

        <button className="esri_geoprocessing_module:Commands.OnBatchTool" id="esri_geoprocessing_BatchTool" loadOnClick="false" caption="Batch" extendedCaption="Batch processing">
          <tooltip heading="">
            Run the tool multiple times using multiple datasets as input or using different parameter settings.<disabledText>This tool does not support Batch.</disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_editToolboxProperties" hidden="true"
                caption="Properties" extendedCaption="Geoprocessing toolbox"
                className="esri_geoprocessing_module:Commands.OnEditToolboxProperties" loadOnClick="false"
                smallImage="GenericProperties16"
                helpContextID=""
                condition="esri_geoprocessing_toolbox_valid">
          <tooltip heading="">
            Show properties for the toolbox.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_editToolProperties" hidden="true"
                caption="Properties" extendedCaption="Geoprocessing tool"
                className="esri_geoprocessing_module:Commands.OnEditToolProperties" loadOnClick="false"
                smallImage="GenericProperties16" helpContextID="">
          <tooltip heading="">
            Show properties for the tool.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_helpCommand" hidden="true"
        caption="Help" extendedCaption="Help"
        className="esri_geoprocessing_module:Commands.OnShowHelp" loadOnClick="false"
        smallImage="HelpSystemBlue16" helpContextID="">
          <tooltip heading="">
            Show help for the tool.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_taskInformation" hidden="true"
            caption="Task Information" extendedCaption="Geoprocessing Task Information"
            className="esri_geoprocessing_module:Commands.OnShowServiceInformation" loadOnClick="false"
            smallImage="Text_File16" helpContextID="">
          <tooltip heading="">
            Open the Geoprocessing Task resource web page.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_serviceInformation" hidden="true"
            caption="Service Information" extendedCaption="Geoprocessing Service Information"
            className="esri_geoprocessing_module:Commands.OnShowServiceInformation" loadOnClick="false"
            smallImage="Text_File16" helpContextID="">
          <tooltip heading="">
            Open the Geoprocessing Service resource web page.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_ProjectItem_Tool_clear" hidden="true" caption="Clear" extendedCaption="Project geoprocessing tool" className="esri_geoprocessing_module:Commands.OnClearProjectTool" helpContextID="">
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_ProjectItem_Toolbox_remove" hidden="true" caption="Remove From Project" extendedCaption="Geoprocessing toolbox from Project"
                className="esri_geoprocessing_module:Commands.OnRemoveToolbox" loadOnClick="false"
                condition="esri_projectItem_DefaultToolboxSelectedCondition" menuKeytip="V"
                smallImage="GenericDeleteBlack16" helpContextID="">
          <tooltip heading="">
            Remove the toolbox from the project. The project's reference to the toolbox is removed. The toolbox is not deleted.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_Toolbox_MakeDefaultButton" hidden="true" keytip="H" menuKeytip="K"
                className="esri_geoprocessing_module:Commands.SetDefaultToolboxButton" loadOnClick="false"
                caption="Make Default" extendedCaption="Geoprocessing Toolbox"
                condition="esri_projectItem_SelectedToolboxMakeDefaultCondition"
                smallImage="GenericHome_B_16"
                largeImage="GenericHome_B_32"
                helpContextID="">
          <tooltip heading="">
            Make this toolbox the default toolbox for the project that is currently open.<disabledText></disabledText>
          </tooltip>
        </button>

        <customControl id="esri_geoprocessing_Start" caption="Tools" extendedCaption="Open Geoprocessing pane" isDropDown="true" className="GPStartViewModel" largeImage="GeoprocessingTool32" smallImage="GeoprocessingTool16" helpContextID="">
          <content className="GPDocPaneView" />
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </customControl>

        <button id="esri_geoprocessing_spatialJoinsButton" className="esri_geoprocessing_module:Commands.SpatialJoin" caption="Add Spatial Join" smallImage="TableSpatialJoin16" largeImage="TableSpatialJoin32" condition="esri_mapping_JoinsEnabledCondition" helpContextID="" keytip="RS">
          <tooltip heading="">
            Create a spatial join. Joins attributes from one feature to another based on the spatial relationship.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_newSelectionLayerButton" className="esri_geoprocessing_module:Commands.NewSelectionLayer" caption="New Selection Layer" loadOnClick="false" largeImage="SelectionLayerCreate32">
          <tooltip heading="">
            Copy selected features to a new layer.<disabledText>Not available yet.</disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_exportDataButton" className="esri_geoprocessing_module:Commands.ExportData" caption="Export Features" extendedCaption="Selected layer"
                smallImage="ExportFeatures16" largeImage="ExportFeatures32"
                helpContextID="" loadOnClick="false" keytip="EF" condition="esri_mapping_mapTypeLinkChartState_false">
          <tooltip heading="">
            Convert a shapefile or geodatabase feature class to a shapefile or geodatabase feature class.<disabledText>The selected item is not a feature layer or this layer item does not support the export data capability.</disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_selectByAttributeButton" className="esri_geoprocessing_module:Commands.SelectLayerByAttribute" caption="Select By Attributes" extendedCaption="Active map view" keytip="SBA" smallImage="SelectionSelectByAttributes16" largeImage="SelectionSelectByAttributes32" condition="esri_mapping_mapPane" helpContextID="120003957">
          <tooltip heading="">
            Select features by their attribute values.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_selectByLocationButton" className="esri_geoprocessing_module:Commands.SelectByLocation" caption="Select By Location" keytip="SBL" smallImage="SelectionSelectByLocation16" largeImage="SelectionSelectByLocation32" condition="esri_mapping_mapPane" helpContextID="120003958">
          <tooltip heading="">
            Select features using the location of features in another layer.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_selectByAttributeTableButton" className="esri_geoprocessing_module:Commands.SelectByAttributeTable" caption="Select By Attributes" extendedCaption="Standalone table" smallImage="SelectionSelectByAttributes16" largeImage="SelectionSelectByAttributes32" helpContextID="">
          <tooltip heading="">
            Select rows by constructing an attribute query.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_rasterSelectByAttributeButton" className="esri_geoprocessing_module:Commands.SelectLayerByAttribute" keytip="SBA" caption="By Attributes" smallImage="SelectionSelectByAttributes16" largeImage="SelectionSelectByAttributes32" condition="esri_mapping_mapPane" helpContextID="">
          <tooltip heading="">
            Select features by their attribute values.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableAddFieldButton" className="esri_geoprocessing_module:Commands.TableAddField" caption="New" extendedCaption="Add Field" largeImage="TableAddField32" smallImage="TableAddField16" helpContextID="">
          <tooltip heading="New Field">
            Create a new field in this table.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableDeleteFieldButton" className="esri_geoprocessing_module:Commands.TableDeleteField" loadOnClick="false" caption="Delete" extendedCaption="Delete Field" largeImage="TableDeleteField32" smallImage="TableDeleteField16" helpContextID="">
          <tooltip heading="Delete Field">
            Delete a field from this table.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableAddJoinButton" caption="Add Join" className="esri_geoprocessing_module:Commands.TableAddJoin" smallImage="TableJoinsAdd16" loadOnClick="false" keytip="A" condition="esri_mapping_JoinsEnabledCondition">
          <tooltip heading="">
            Join data to this layer or standalone table based on a common attribute.<disabledText>Joins are not available for subtype layers or stream layers.</disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableGeocodeAddresses" hidden="true" caption="Geocode Table" extendedCaption="Geocode a table - Context Menu Item" className="esri_geoprocessing_module:Commands.GeocodeAddresses" smallImage="GeocodeAddressLocator16" loadOnClick="false">
          <tooltip heading="">
            Geocode the selected table of addresses.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableRemoveJoinButton" caption="Remove Join" className="esri_geoprocessing_module:Commands.TableRemoveJoin" smallImage="TableJoinsRemove16" loadOnClick="false" keytip="RJ" condition="esri_mapping_JoinsEnabledCondition">
          <tooltip heading="">
            Remove joined data from the current table.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableRemoveAllJoinsButton" caption="Remove All Joins" className="esri_geoprocessing_module:Commands.TableRemoveAllJoins" smallImage="TableJoinsRemove16" loadOnClick="false" keytip="RA" condition="esri_mapping_JoinsEnabledCondition">
          <tooltip heading="">
            Remove all joined data from the current table.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableAddRelateButton" caption="Add Relate" className="esri_geoprocessing_module:Commands.TableAddRelate" smallImage="TableRelatesAdd16" loadOnClick="false" keytip="A" condition="esri_mapping_RelatesEnabledCondition">
          <tooltip heading="Add Relate">
            Relate data to this layer or standalone table based on a common attribute. Unlike a join, a relate does not append data to the layer or standalone table.<disabledText>Relates are not available for non-OID tables or subtype layers or stream layers.</disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableRemoveRelateButton" caption="Remove Relate" className="esri_geoprocessing_module:Commands.TableRemoveRelate" smallImage="TableRelatesRemove16" loadOnClick="false" keytip="RR" condition="esri_mapping_RelatesEnabledCondition">
          <tooltip heading="Remove Relate">
            Remove the relate from the current table.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableRemoveAllRelatesButton" caption="Remove All Relates" className="esri_geoprocessing_module:Commands.TableRemoveAllRelates" smallImage="TableRelatesRemove16" loadOnClick="false" keytip="RA" condition="esri_mapping_RelatesEnabledCondition">
          <tooltip heading="Remove All Relates">
            Remove all relates from the current table.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_Add_ScriptTool" hidden="true"
                caption="Script" keytip="NS" extendedCaption="New Script Tool"
                className="esri_geoprocessing_module:Commands.OnNewScript" loadOnClick="false"
                smallImage="GeoprocessingScript16"
                largeImage="GeoprocessingScript32"
                helpContextID=""
                condition="esri_geoprocessing_toolbox_valid">
          <tooltip heading="New Script">
            Create a new script tool in the toolbox.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_Add_ModelTool" hidden="true"
                caption="Model" keytip="NM" extendedCaption="New Model Tool" loadOnClick="false"
                className="esri_geoprocessing_module:Commands.OnNewModel"
                smallImage="GeoprocessingModel16"
                largeImage="GeoprocessingModel32"
                helpContextID=""
                condition="esri_geoprocessing_toolbox_valid">
          <tooltip heading="New Model">
            Create a new model in the toolbox.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_Add_Toolset" hidden="true" caption="Toolset" keytip="NT" extendedCaption="New Toolset"
                className="esri_geoprocessing_module:Commands.OnNewToolset" loadOnClick="false"
                smallImage="GeoprocessingToolset16"
                largeImage="GeoprocessingToolset32"
                helpContextID=""
                condition="esri_geoprocessing_toolbox_valid">
          <tooltip heading="New Toolset">
            Create a new toolset in the toolbox.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_tableAddGeometryAttributesButton" className="esri_geoprocessing_module:Commands.TableAddGeometryAttributes" caption="New Geometry Attributes" smallImage="CalculateGeometry16" largeImage="CalculateGeometry32" loadOnClick="false" helpContextID="">
          <tooltip heading="">
            Create new attribute fields for the input features representing the spatial or geometric characteristics and location of each feature, such as length or area and x-, y-, z-, and m-coordinates.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableCalculateFieldButton" caption="Calculate Field" className="esri_geoprocessing_module:Commands.TableCalculateField" smallImage="TableCalculateField16" largeImage="TableCalculateField32" helpContextID="" loadOnClick="false">
          <tooltip heading="">
            Set the values of this field by specifying a calculation expression. If any of the rows in the table are currently selected, only the values of the selected rows will be calculated.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableSummarizeButton" caption="Summarize" extendedCaption="Summary Statistics" className="esri_geoprocessing_module:Commands.TableSummarize" smallImage="TableSummaryStatistics16" largeImage="TableSummary32" helpContextID="" loadOnClick="false">
          <tooltip heading="Summary Table">
            Create a summary table grouped by the values in the current field. If any of the rows in the table are currently selected, only the values of the selected rows will be calculated.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_tableDisplayXYDataButton" caption="XY Table To Point" className="esri_geoprocessing_module:Commands.TableDisplayXYData" smallImage="XYTableToPoint16" largeImage="XYTableToPoint32" helpContextID="" condition="esri_mapping_singleStandaloneTableSelectedCondition" keytip="TP">
          <tooltip heading="">Creates a point feature class based on coordinate values stored in the selected table.</tooltip>
        </button>
        <button id="esri_geoprocessing_tableMakeXYEventLayerButton" caption="Make XY Event Layer" className="esri_geoprocessing_module:Commands.MakeXYEventLayer" smallImage="AddXYEvent16" largeImage="AddXYEvent32" helpContextID="" condition="esri_mapping_singleStandaloneTableSelectedCondition" keytip="EL">
          <tooltip heading="">Makes an event layer based on coordinate values stored in the selected table.</tooltip>
        </button>
        <button id="esri_geoprocessing_tableExportTableButton" caption="Export Table" extendedCaption="Selected standalone table" className="esri_geoprocessing_module:Commands.TableExportTable" smallImage="ExportTable16" largeImage="ExportTable32" helpContextID="" keytip="ET" loadOnClick="false">
          <tooltip heading="">
            Export current table to new table.<disabledText>This layer or standalone table item does not support the export data capability.</disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_importSymbologyButton" keytip="DI" className="esri_geoprocessing_module:Commands.ImportSymbology" caption="Import" extendedCaption="Apply Symbology From Layer"
                helpContextID="3110012"
                largeImage="LayerImportSymbology32"
                smallImage="LayerImportSymbology16" condition="esri_mapping_singleFeatureLayerSelectedCondition">
          <tooltip heading="">
            Import symbology from another layer.<disabledText></disabledText>
          </tooltip>
        </button>

        <!--Modelbuilder Commands-->

        <button id="esri_modelbuilder_saveModelButton" hidden="true" caption="Save" loadOnClick="false" extendedCaption="ModelBuilder" keytip="SM" className="esri_geoprocessing_module:Commands.SaveModel" smallImage="GenericSave16" largeImage="GenericSave32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Save">
            Save the model edits or save the model as a new model in a new location.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_saveButton" hidden="false" caption="Save" loadOnClick="false" extendedCaption="ModelBuilder" keytip="SM" className="esri_geoprocessing_module:Commands.SaveModel" smallImage="GenericSave16" largeImage="GenericSave32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Save">
            Save the model.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_saveAsButton" hidden="false" caption="Save As" extendedCaption="ModelBuilder" keytip="SA" className="esri_geoprocessing_module:Commands.SaveAsModel" smallImage="EditingSaveEdits_B_16" largeImage="EditingSaveEdits_B_32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Save As">
            Save the model with a new name and new location.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_exportImageButton" caption="Export To Graphic" extendedCaption="ModelBuilder" keytip="G" className="esri_geoprocessing_module:Commands.ExportModelImage" smallImage="Picture16" largeImage="GeoprocessingModel32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Export To Graphic">
            Export model to an image format.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_exportScriptButton" caption="Export To Python File" extendedCaption="ModelBuilder" keytip="P" className="esri_geoprocessing_module:Commands.ExportModelScript" smallImage="GeoprocessingScript16" largeImage="GeoprocessingScript32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Export To Python File">
            Export model to a Python file.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_exportFromModelButton" hidden="true" caption="Export" extendedCaption="ModelBuilder" keytip="E" className="esri_geoprocessing_module:Commands.SendModelToPythonWindow" smallImage="GenericExport16" largeImage="GenericExport32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Export A Model">
            Export a model to a graphic, a Python file or send the code to Python window.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_SentModelToPythonWindowButton" caption="Send To Python Window" extendedCaption="ModelBuilder" keytip="W" className="esri_geoprocessing_module:Commands.SendModelToPythonWindow" smallImage="GeoprocessingPythonWindowShow16" largeImage="GeoprocessingPythonWindowShow32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Send To Python window">
            Export model to Python and add the code to Python window.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_exportReportButton" hidden="true" caption="Export Report" extendedCaption="ModelBuilder" keytip="R" className="esri_geoprocessing_module:Commands.ExportModelReport" smallImage="LabelingTextSymbolTabFormatting16" largeImage="LabelingTextSymbolTabFormatting24" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Export Report">
            Export model to report.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_groupExpandAll" caption="Expand All Groups" extendedCaption="ModelBuilder" keytip="MEG" className="esri_geoprocessing_module:Commands.ExpandAllGroups" smallImage="" largeImage="" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Expand All Groups">
            Expands all groups in a Model<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_groupCollapseAll" caption="Collapse All Groups" extendedCaption="ModelBuilder" keytip="MEG" className="esri_geoprocessing_module:Commands.CollapseAllGroups" smallImage="" largeImage="" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Collapse All Groups">
            Collapses all groups in a Model<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_groupButton" caption="Group" extendedCaption="ModelBuilder" keytip="MG" className="esri_geoprocessing_module:Commands.CreateGroup" smallImage="ModelBuilderGroup16" largeImage="ModelBuilderGroup32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Create a New Group ">
            Create a new empty group or add selected elements to a new group.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_ungroupButton" caption="UnGroup" extendedCaption="ModelBuilder" keytip="UG" className="esri_geoprocessing_module:Commands.UnGroup" smallImage="ModelBuilderUngroup16" largeImage="ModelBuilderUngroup32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="UnGroup">
            Dissolve group around elements.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_progressorOpen" caption="Open" disableIfBusy="false" extendedCaption="ModelBuilder" keytip="OP"
                className="esri_geoprocessing_module:Commands.OpenProgressor" loadOnClick="false"
                smallImage="GeoprocessingResultsFolderOpenState16"
                largeImage="GeoprocessingResultsFolderOpenState32"
                helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Open messages">
            Open the message window.
            <disabledText>The messages are only available after model is run in the current session.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_ZoomStepUp" caption="Zoom In" extendedCaption="ModelBuilder" keytip="ZI" className="esri_geoprocessing_module:Commands.ZoomStepUp" smallImage="ZoomIn16" largeImage="ZoomIn32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Zoom in">
            Zoom into model<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_ZoomStepDown" caption="Zoom Out" extendedCaption="ModelBuilder" keytip="ZO" className="esri_geoprocessing_module:Commands.ZoomStepDown" smallImage="ZoomOut16" largeImage="ZoomOut32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Zoom out">
            Zoom model out<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_fitToWindowButton" caption="Fit to Window" extendedCaption="ModelBuilder" keytip="FTW" className="esri_geoprocessing_module:Commands.FitToWindow" smallImage="ModelBuilderFullExtent16" largeImage="ModelBuilderFullExtent32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Fit to Window">
            Fit model diagram to window so you can see all model elements.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_prevExtentButton" hidden="false" caption="Previous Extent" extendedCaption="ModelBuilder" keytip="PE" flipImageRTL="true" className="esri_geoprocessing_module:Commands.PreviousExtent" smallImage="GenericBlueLeftArrowLongTail16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane" loadOnClick="false">
          <tooltip heading="Previous Extent">
            Go to previous extent.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_nextExtentButton" hidden="false" caption="Next Extent" extendedCaption="ModelBuilder" keytip="NE" flipImageRTL="true" className="esri_geoprocessing_module:Commands.NextExtent" smallImage="GenericBlueRightArrowLongTail16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane" loadOnClick="false">
          <tooltip heading="Next Extent">
            Go to next extent.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_lockButton" caption="Lock" extendedCaption="ModelBuilder" keytip="LL" className="Lock" smallImage="GenericLockNoColor16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane" loadOnClick="false">
          <tooltip heading="Lock Element">
            Locks element from being affected by Auto-Layout.<disabledText>Make a model the active view and select the elements to lock.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_unlockButton" caption="Unlock" extendedCaption="ModelBuilder" keytip="UL" className="Unlock" smallImage="GenericUnLockNoColor16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane" loadOnClick="false">
          <tooltip heading="Unlock Element">
            Unlocks element so it can participate in Auto-Layout.<disabledText>Make a model the active view and select the elements to unlock.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_zoomInButton" hidden="true" loadOnClick="false" caption="Zoom in" extendedCaption="ModelBuilder" keytip="ZI" className="ZoomInSelection" smallImage="ZoomIn16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Zoom In">
            Zoom in on the model.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_zoomOutButton" hidden="true" loadOnClick="false" caption="Zoom out" extendedCaption="ModelBuilder" keytip="ZO" className="ZoomOutSelection" smallImage="ZoomOut16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Zoom Out">
            Zoom out from the model.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_autoLayoutDiagramButton" caption="Auto Layout" extendedCaption="ModelBuilder" keytip="AL" className="esri_geoprocessing_module:Commands.AutoLayout" smallImage="pack://application:,,,/ArcGIS.Desktop.Resources;component/Images/ModelBuilderAutoLayout16.png" largeImage="pack://application:,,,/ArcGIS.Desktop.Resources;component/Images/ModelBuilderAutoLayout32.png" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Auto Layout">
            Automatically arrange the elements in a model.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_RouteAllGroupLinksButton" caption="Route Group Links" extendedCaption="ModelBuilder" keytip="RGL" className="esri_geoprocessing_module:Commands.RouteAllGroupLinks" smallImage="ModelBuilderLinkCascade16" largeImage="ModelBuilderLinkCascade32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Route Group Links">
            Automatically routes the links in a group.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_RouteAllLinksButton" hidden="true" caption="Route All Links" extendedCaption="ModelBuilder" keytip="RAL" className="esri_geoprocessing_module:Commands.RouteAllLinks" smallImage="ModelBuilderLinkCascade16" largeImage="ModelBuilderLinkCascade32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Route All Links">
            Automatically routes the links in a model.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_RouteSelectedLinksButton" hidden="true" caption="Route Link(s)" extendedCaption="ModelBuilder" keytip="RAL" className="esri_geoprocessing_module:Commands.RouteSelectedLinks" smallImage="ModelBuilderLinkCascade16" largeImage="ModelBuilderLinkCascade32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Route Link(s)">
            Automatically routes the selected links in a model.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelBuilder_editToolProperties" hidden="false" caption="Properties" extendedCaption="ModelBuilder" keytip="PR" className="esri_geoprocessing_module:Commands.OnEditModelToolProperties" smallImage="GenericProperties16" largeImage="GenericProperties32" helpContextID="">
          <tooltip heading="Properties">
            Open model tool properties.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_autoSaveButton" caption="Auto Save" keytip="AS" className="MBAutoSave" isChecked="{Binding AutoSave}" loadOnClick="false"   condition="esri_geoprocessing_mb_autosaveEnabledState"
                largeImage="GenericAutoSave32"
                smallImage="GenericAutoSave16" >
          <tooltip heading="Auto Save">
            Automatically saves the edited model. Click to turn the auto save on or off.
            <disabledText>
              Auto save is only supported if the model was created or saved in the current product version. Use the Save button to save the model to the current version.
            </disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_environmentsButton" hidden="false" caption="Environments" extendedCaption="ModelBuilder" keytip="EN" className="esri_geoprocessing_module:Commands.ShowModelEnvironments" smallImage="GeoprocessingEnvironmentSettings16" largeImage="GeoprocessingEnvironmentSettings32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Environments">
            Specify the environment settings geoprocessing tools should use.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_reportViewButton" caption="Report" extendedCaption="ModelBuilder" keytip="RE" className="esri_geoprocessing_module:Commands.ShowModelReportView" smallImage="Open_report16" largeImage="Open_report32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Report">
            Show ModelBuilder Report.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_OpenToolInGPPane" caption="Open Tool" extendedCaption="ModelBuilder" keytip="OT" className="esri_geoprocessing_module:Commands.OpenModelInGPPane" smallImage="GeoprocessingModel16" largeImage="GeoprocessingModel32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Open Tool">
            Open model tool in geoprocessing pane.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_findAndReplace" caption="Find and Replace" hidden="false" extendedCaption="ModelBuilder" keytip="FR" className="esri_geoprocessing_module:Commands.ShowFindAndReplace" smallImage="FindReplaceMenu16" largeImage="FindReplaceMenu32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Find and Replace">
            Find and Replace.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_cutElementButton" caption="Cut" extendedCaption="ModelBuilder" keytip="X" className="esri_geoprocessing_module:Commands.CutElements" smallImage="EditCut16" largeImage="EditCut32" loadOnClick="false" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Cut">
            Cut selected model elements.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_copyElementButton" caption="Copy" extendedCaption="ModelBuilder" keytip="C" className="esri_geoprocessing_module:Commands.CopyElements" smallImage="EditCopy16" largeImage="EditCopy32" loadOnClick="false" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Copy">
            Copy selected model elements.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_pasteElementButton" caption="Paste" extendedCaption="ModelBuilder" keytip="V" className="esri_geoprocessing_module:Commands.PasteElements" smallImage="EditPaste16" largeImage="EditPaste32" loadOnClick="false" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Paste">
            Paste the cut or copied model elements.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <!--<button id="esri_modelbuilder_undoModelButton" hidden="true" caption="Undo" extendedCaption="ModelBuilder" keytip="UN" className="esri_geoprocessing_module:Commands.UndoModel" smallImage="EditUndo_B_16" largeImage="EditUndo_B_32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Undo">
            Undo.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_redoModelButton" hidden="true" caption="Redo" extendedCaption="ModelBuilder" keytip="RE" className="esri_geoprocessing_module:Commands.RedoModel" smallImage="EditRedo_B_16" largeImage="EditRedo_B_32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Redo">
            Redo.<disabledText></disabledText>
          </tooltip>
        </button>-->
        <!--<button id="esri_modelbuilder_cutElementButton" hidden="true" caption="Cut" extendedCaption="ModelBuilder" keytip="X" className="esri_geoprocessing_module:Commands.CutElement" smallImage="EditCut16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Cut">
            Cut selected model elements.<disabledText>Select an element to Cut.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_copyElementButton" hidden="true" caption="Copy" extendedCaption="ModelBuilder" keytip="C" className="esri_geoprocessing_module:Commands.CopyElement" smallImage="EditCopy16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Copy">
            Copy selected model elements.<disabledText>Select an element to Copy.</disabledText>
          </tooltip>
        </button>-->
        <button id="esri_modelbuilder_cutConnectionButton" hidden="true" caption="Remove" extendedCaption="ModelBuilder" keytip="X" className="esri_geoprocessing_module:Commands.CutConnection" smallImage="GenericDeleteRed16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Remove">
            Remove selected connections.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_validateButton" caption="Validate" extendedCaption="ModelBuilder" keytip="MV" className="esri_geoprocessing_module:Commands.ValidateModel" smallImage="GenericCheckMark16" largeImage="GenericCheckMark32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Validate">
            Verify all data elements and parameter values are valid.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_runModelButton" caption="Run" extendedCaption="ModelBuilder" keytip="RM" className="esri_geoprocessing_module:Commands.RunModel" smallImage="GenericRun16" largeImage="GenericRun32" helpContextID="" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Run">
            Run all ready-to-run processes in the model.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_deleteIntermediateDataActualButton" caption="Intermediate" extendedCaption="ModelBuilder" keytip="DI" className="esri_geoprocessing_module:Commands.DeleteIntermediateDataActual" smallImage="GenericDeleteRed16" largeImage="GenericDeleteRed32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Delete Intermediate Data">
            Delete intermediate data from workspace.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_panButton" caption="Pan" extendedCaption="ModelBuilder" keytip="PM" className="PanMode" loadOnClick="false" smallImage="PanTool_B_16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Pan">
            Pan in the model. <disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_selectButton" caption="Select" extendedCaption="ModelBuilder" keytip="SE" className="SelectMode" loadOnClick="false" smallImage="ModelBuilderSelect16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Select">
            Select and move model elements, and make connections.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_selectAllButton" caption="Select All" extendedCaption="ModelBuilder" keytip="SA" className="esri_geoprocessing_module:Commands.SelectAll" smallImage="SelectionSelectAll16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Select All">
            Select all model elements.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_selectAllGroupButton" caption="Group Select All" extendedCaption="ModelBuilder" keytip="SA" className="esri_geoprocessing_module:Commands.SelectAllGroup" smallImage="SelectionSelectAll16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Group Select All">
            Select all group items.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_elementMessagesButton" hidden="true" caption="Messages..." extendedCaption="ModelBuilder" keytip="MM" className="esri_geoprocessing_module:Commands.ElementMessages" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Messages">
            View tool messages.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_collapseSelectedGroup" caption="Fold Selected Group" extendedCaption="ModelBuilder" keytip="FC"
               className="esri_geoprocessing_module:Commands.CollapseSelectedGroup" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_expandSelectedGroup" caption="Unfold Selected Group" extendedCaption="ModelBuilder" keytip="UFC"
               className="esri_geoprocessing_module:Commands.ExpandSelectedGroup" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_openElementButton" caption="Open..." extendedCaption="ModelBuilder" keytip="OE" className="esri_geoprocessing_module:Commands.OpenElement" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Open">
            Open model element. If the element is a tool, the tool dialog is opened. If it is a variable, open the variable control to change the value assigned to the variable.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_editProcessToolButton" hidden="false" caption="Edit..." extendedCaption="ModelBuilder" keytip="ED" className="esri_geoprocessing_module:Commands.EditProcessTool" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Open">
            Open model element. If the element is a tool, the tool dialog is opened. If it is a variable, open the variable control to change the value assigned to the variable.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_ClearSelection" hidden="true" caption="Clear Selection" extendedCaption="Clear Selection" keytip="CLS"
                className="esri_geoprocessing_module:Commands.ClearSelection" smallImage="ClearSelection16"
                helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_CycleDialogs" hidden="true" caption="Cycle Dialogs" extendedCaption="Cycle Dialogs" keytip="CDS" className="esri_geoprocessing_module:Commands.CycleDialogs" smallImage="" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_ExtentLeft" caption="Pan Left" extendedCaption="ModelBuilder" keytip="PL"
                className="esri_geoprocessing_module:Commands.PanLeft" helpContextID="" condition="esri_geoprocessing_modelBuilderPane"
                smallImage="PanTool_B_16">
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_ExtentRight" caption="Pan Right" extendedCaption="ModelBuilder"
                keytip="PR" className="esri_geoprocessing_module:Commands.PanRight" smallImage="PanTool_B_16"
                helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_ExtentUp" caption="Pan Up" extendedCaption="ModelBuilder" keytip="PL"
                className="esri_geoprocessing_module:Commands.PanUp" helpContextID="" condition="esri_geoprocessing_modelBuilderPane"
                smallImage="PanTool_B_16">
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_ExtentDown" caption="Pan Down" extendedCaption="ModelBuilder"
                keytip="PR" className="esri_geoprocessing_module:Commands.PanDown" smallImage="PanTool_B_16"
                helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_CycleActiveItems" hidden="true" caption="Cycle active items" extendedCaption="Cycle active items" keytip="CI" className="esri_geoprocessing_module:Commands.CycleActiveItems" smallImage="" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="">
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_parameterButton" hidden="true" caption="Parameter" extendedCaption="Switch ModelBuilder parameter" keytip="PS" className="esri_geoprocessing_module:Commands.ModelParameter" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Parameter">
            Remove model parameter.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_parameterOffButton" caption="Unset Parameter" extendedCaption="ModelBuilder" keytip="PF" className="esri_geoprocessing_module:Commands.ModelParameter" smallImage="GenericCheckMark16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Parameter">
            Remove model parameter.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_parameterOnButton" caption="Set Parameter" extendedCaption="ModelBuilder" keytip="PN" className="esri_geoprocessing_module:Commands.ModelParameter" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Parameter">
            Make model parameter. Model parameters are displayed when the model is opened as a geoprocessing tool. Make an output a parameter so that it is added to the map when the tool runs.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_modelBuilder_addToDisplayOnButton" caption="Remove Add To Display" extendedCaption="ModelBuilder" keytip="AD" className="esri_geoprocessing_module:Commands.RemoveFromDisplay" smallImage="GenericCheckMark16" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Add To Display">
            Remove Add To Display.
            <disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelBuilder_addToDisplayOffButton" caption="Add To Display" extendedCaption="ModelBuilder" keytip="RAD"
                className="esri_geoprocessing_module:Commands.AddToDisplay" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Add To Display">
            When a model is open in the diagram view, checking Add to Display will add the output to the map and unchecking will prevent the output from being added to the map or will remove the current output from the map. To add output to display from a model tool dialog, make the output a model parameter.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_modelBuilder_DeleteIntermediateDataOffButton" caption="Intermediate Data" extendedCaption="ModelBuilder" keytip="DD"
                className="esri_geoprocessing_module:Commands.DeleteIntermediateData" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Intermediate Data">
            When the model is open in a diagram view, checking intermediate data will delete the output after the model runs and Delete Intermediate button is clicked from the ribbon. If the model is run from the model tool dialog, anything checked as intermediate will be automatically deleted.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelBuilder_DeleteIntermediateDataOnButton" caption="Remove Intermediate Data" extendedCaption="ModelBuilder" keytip="PD"
                className="esri_geoprocessing_module:Commands.DeleteIntermediateDataOff" helpContextID="" condition="esri_geoprocessing_modelBuilderPane"
                smallImage="GenericCheckMark16">
          <tooltip heading="Intermediate Data">
            Remove Intermediate Data<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_runProcessButton" hidden="true" caption="Run" extendedCaption="ModelBuilder process" keytip="RU" className="esri_geoprocessing_module:Commands.RunProcess" smallImage="GenericRun16" largeImage="GenericRun32" helpContextID="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Run">
            Run model up to this process.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_renameElementButton" caption="Rename" extendedCaption="ModelBuilder" keytip="RE"
                className="esri_geoprocessing_module:Commands.RenameElement" helpContextID="" condition="esri_geoprocessing_modelBuilderPane"
                smallImage="Rename16"
                largeImage="Rename32">
          <tooltip heading="Rename">
            Rename.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_createVariableButton" caption="Variable" extendedCaption="ModelBuilder" keytip="AV" className="esri_geoprocessing_module:Commands.CreateModelVariable" helpContextID="" smallImage="ModelBuilderVariable16" largeImage="ModelBuilderVariable32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Create Variable">
            Variables are names that store values which can be changed. These values can be layers, dataset paths, numbers, strings, or many other things.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_createVariableButton" hidden="true" caption="Create Variable..." extendedCaption="ModelBuilder" keytip="AV" className="esri_geoprocessing_module:Commands.CreateVariableElement" helpContextID="" smallImage="ModelBuilderVariable16" largeImage="ModelBuilderVariable32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Create Variable">
            Variables are names that store values which can be changed. These values can be layers, dataset paths, numbers, strings, or many other things.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_createLabelButton" caption="Create Label" extendedCaption="ModelBuilder" keytip="LB" className="esri_geoprocessing_module:Commands.CreateLabelCmd" helpContextID="" smallImage="MapRibbonLabeling16" largeImage="MapRibbonLabeling32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Create Label">
            Create label.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_toolSuggestionButton" caption="Suggest Tool" extendedCaption="ModelBuilder" keytip="ST" className="esri_geoprocessing_module:Commands.ShowModelPane" helpContextID="120005081" smallImage="LightbulbOn16" largeImage="LightbulbOn32" condition="esri_geoprocessing_ToolSuggestions_installed">
          <tooltip heading="Suggest Tool">
            Tools are suggested based on existing tools in the model to help create a workflow.<disabledText>Install Semantic Search to enable this functionality.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateCountButton" hidden="true" caption="For" extendedCaption="ModelBuilder" keytip="FR" className="esri_geoprocessing_module:Commands.IterateCount" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="For">
            Iterates over a starting and ending value by a given value. It works exactly like For in any scripting/programming language, executing through a set number of items.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateConditionButton" hidden="true" caption="While" extendedCaption="ModelBuilder" keytip="WH" className="esri_geoprocessing_module:Commands.IterateCondition" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="While">
            Works exactly like 'while' in any scripting/programming language, executing "while" a condition is true or false for the input or set of inputs.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateFeatureSelectionButton" hidden="true" caption="Iterate Feature Selection" extendedCaption="ModelBuilder" keytip="FS" className="esri_geoprocessing_module:Commands.IterateFeatureSelection" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Feature Selection">
            Iterates over features in a feature class.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateRowSelectionButton" hidden="true" caption="Iterate Row Selection" extendedCaption="ModelBuilder" keytip="RS" className="esri_geoprocessing_module:Commands.IterateRowSelection" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Row Selection">
            Iterates over rows in a table.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_IterateFieldsButton" hidden="true" keytip="FD" caption="Iterate Fields" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IterateFields" loadOnClick="false" helpContextID=""  smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Fields">
            Lists all the fields from a table.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateFieldValuesButton" hidden="true" caption="Iterate Field Values" extendedCaption="ModelBuilder" keytip="FV" className="esri_geoprocessing_module:Commands.IterateFieldValues" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Field Values">
            Iterates over each value in a field.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateMultivalueButton" hidden="true" caption="Iterate Multivalue" extendedCaption="ModelBuilder" keytip="MI" className="esri_geoprocessing_module:Commands.IterateMultivalue" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Multivalue">
            Iterates over a list of values.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateDatasetsButton" hidden="true" caption="Iterate Datasets" extendedCaption="ModelBuilder" keytip="DS" className="esri_geoprocessing_module:Commands.IterateDatasets" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Datasets">
            Iterates over datasets in a Workspace or Feature Dataset.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateFeatureClassesButton" hidden="true" caption="Iterate Feature Classes" extendedCaption="ModelBuilder" keytip="FC" className="esri_geoprocessing_module:Commands.IterateFeatureClasses" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Feature Classes">
            Iterates over feature classes in a Workspace or Feature Dataset.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateFilesButton" hidden="true" caption="Iterate Files" extendedCaption="ModelBuilder" keytip="FI" className="esri_geoprocessing_module:Commands.IterateFiles" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Files">
            Iterates over files in a folder.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateRastersButton" hidden="true" caption="Iterate Rasters" extendedCaption="ModelBuilder" keytip="RA" className="esri_geoprocessing_module:Commands.IterateRasters" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Rasters">
            Iterates over rasters in a Workspace or a Raster Catalog.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateTablesButton" hidden="true" caption="Iterate Tables" extendedCaption="ModelBuilder" keytip="TE" className="esri_geoprocessing_module:Commands.IterateTables" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Tables">
            Iterates over tables in a workspace.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateWorkspacesButton" hidden="true" caption="Iterate Workspaces" extendedCaption="ModelBuilder" keytip="WK" className="esri_geoprocessing_module:Commands.IterateWorkspaces" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Workspaces">
            Iterates over workspaces in a folder.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateLayersButton" hidden="true" keytip="LY" caption="Iterate Layers" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IterateLayers" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Layers">
            Iterates layers in a map.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_iterateTimeButton" hidden="true" keytip="T" caption="Iterate Time" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IterateTime" loadOnClick="false" helpContextID="" smallImage="ModelBuilderIterator16" largeImage="ModelBuilderIterator32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Iterate Time">
            Iterates over time in a date field.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_getFieldValueButton" hidden="true" caption="Get Field Value" extendedCaption="ModelBuilder" keytip="F" className="esri_geoprocessing_module:Commands.GetFieldValue" helpContextID="" smallImage="ModelBuilderAddModelOnlyTools16" largeImage="ModelBuilderAddModelOnlyTools32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Get Field Value">
            Gets the value of the first row of a table for the specified field.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_parsePathExtButton" hidden="true" caption="Parse Path" extendedCaption="ModelBuilder" keytip="PP" className="esri_geoprocessing_module:Commands.ParsePathExt" helpContextID="" smallImage="ModelBuilderAddModelOnlyTools16" largeImage="ModelBuilderAddModelOnlyTools32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Parse Path">
            Parses the input into its file, path, name, or extension. The output can be used as in-line variables in the output name of other tools.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_selectDataButton" hidden="true" caption="Select Data" extendedCaption="ModelBuilder" keytip="SD" className="esri_geoprocessing_module:Commands.SelectData" helpContextID="" smallImage="ModelBuilderAddModelOnlyTools16" largeImage="ModelBuilderAddModelOnlyTools32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Select Data">
            Selects data in a parent data element such as a folder, geodatabase, feature dataset, or coverage.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_calculateValueButton" hidden="true" caption="Calculate Value" extendedCaption="ModelBuilder" keytip="CV" className="esri_geoprocessing_module:Commands.CalculateValue" helpContextID="" smallImage="ModelBuilderAddModelOnlyTools16" largeImage="ModelBuilderAddModelOnlyTools32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Calculate Value">
            Returns a value based on a specified Python expression.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_collectValuesButton" hidden="true" caption="Collect Values" extendedCaption="ModelBuilder" keytip="CO" className="esri_geoprocessing_module:Commands.CollectValues" helpContextID="" smallImage="ModelBuilderAddModelOnlyTools16" largeImage="ModelBuilderAddModelOnlyTools32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Collect Values">
            Collects output values of an iterator, or converts a list of multivalues into a single value.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_customMessageButton" hidden="true" caption="Custom Message" extendedCaption="ModelBuilder" keytip="CU" className="esri_geoprocessing_module:Commands.CustomMessage" helpContextID="" smallImage="ModelBuilderAddModelOnlyTools16" largeImage="ModelBuilderAddModelOnlyTools32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Custom Message">
            Returns a custom message.
          </tooltip>
        </button>
        <button id="esri_modelbuilder_mergeBranchButton" hidden="true" caption="Merge Branch" extendedCaption="ModelBuilder" keytip="MB" className="esri_geoprocessing_module:Commands.MergeBranch" helpContextID="" smallImage="ModelBuilderAddMergeBranch16" largeImage="ModelBuilderAddMergeBranch32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Merge Branch">
            Merges two or more logical branches into a single output.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_stopButton" hidden="true" caption="Stop" extendedCaption="ModelBuilder" keytip="ST" className="esri_geoprocessing_module:Commands.Stop" helpContextID="" smallImage="ModelBuilderAddStop16" largeImage="ModelBuilderAddStop32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="Stop">
            Stops iteration if all the input values meet the specified conditon of either True or False. It is functionally similar to While iterator but is useful to stop a model if there is one While iterator in a model and no additional iterators can be added.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <!--<button>NEW BUTTONS START.</Button>-->
        <button id="esri_modelbuilder_IfDataExistsButton" hidden="true" keytip="DE" caption="If Data Exists" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IfDataExists" helpContextID="" smallImage="ModelBuilder_If16" largeImage="ModelBuilder_If32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="If Data Exists">
            Evaluates if the specified data exists in the workspace.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_IfFieldValueIsButton" hidden="true" keytip="FV" caption="If Field Value Is" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IfFieldValueIs" helpContextID="" smallImage="ModelBuilder_If16" largeImage="ModelBuilder_If32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="If Field Value Is">
            Evaluates if the value of a field matches a specified value or evaluates if the two fields in the same data have any matching values.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_IfFieldExistsButton" hidden="true" keytip="FE" caption="If Field Exists" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IfFieldExists" helpContextID="" smallImage="ModelBuilder_If16" largeImage="ModelBuilder_If32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="If Field Exists">
            Evaluates if the input data has the specified field/s.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_IfSelectionExistsButton" hidden="true" keytip="SE" caption="If Selection Exists" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IfSelectionExists" helpContextID="" smallImage="ModelBuilder_If16" largeImage="ModelBuilder_If32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="If Selection Exists">
            Evaluates if the data has a selection and if selected records count matches a specified value.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_IfRowCountIsButton" hidden="true" keytip="RC" caption="If Row Count Is" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IfRowCountIs" helpContextID="" smallImage="ModelBuilder_If16" largeImage="ModelBuilder_If32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="If Row Count Is">
            Evaluates if the row count of the input data matches a specified value.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_IfCoordinationSystemIsButton" keytip="CS" hidden="true" caption="If Coordinate System Is" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IfCoordinationSystemIs" helpContextID="" smallImage="ModelBuilder_If16" largeImage="ModelBuilder_If32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="If Coordinate System Is">
            Evaluates if the input data is projected to the specified coordinate system.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_IfDataTypeIsButton" hidden="true" keytip="DT" caption="If Data Type Is" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IfDataTypeIs" helpContextID="" smallImage="ModelBuilder_If16" largeImage="ModelBuilder_If32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="If Data Type Is">
            Evaluates if the input data matches the specified data type/s.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_IfExpressionIsButton" hidden="true" keytip="EI" caption="If Expresssion Is" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IfExpressionIs" helpContextID="" smallImage="ModelBuilder_If16" largeImage="ModelBuilder_If32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="If Expression Is">
            Evaluates the specified expression.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_IfValueIsButton" hidden="true" keytip="V" caption="If Value Is" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IfValueIs" helpContextID="" smallImage="ModelBuilder_If16" largeImage="ModelBuilder_If32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="If Value Is">
            Evaluates if the input value matches the specified criteria.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>

        <button id="esri_modelbuilder_IfFeatureTypeIsButton" hidden="true" keytip="FT" caption="If Feature Type Is" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IfFeatureTypeIs" helpContextID="" smallImage="ModelBuilder_If16" largeImage="ModelBuilder_If32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="If Feature Type Is">
            Evaluates if the input data matches the specified feature type/s.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_IfSpatialRelationshipExistsButton" hidden="true" keytip="SR" caption="If Spatial Relationship Is" extendedCaption="ModelBuilder" className="esri_geoprocessing_module:Commands.IfSpatialRelationshipExists" helpContextID="" smallImage="ModelBuilder_If16" largeImage="ModelBuilder_If32" condition="esri_geoprocessing_anyMBPane">
          <tooltip heading="If Spatial Relationship Is">
            Evaluates if the inputs are spatially related based on a specified spatial relationship.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_NewModelButton" className="esri_geoprocessing_module:Commands.NewModel" caption="New" extendedCaption="ModelBuilder" keytip="NM" largeImage="ModelBuilderNewModel32" smallImage="ModelBuilderNewModel16" helpContextID="">
          <tooltip heading="New Model">
            Create a new blank geoprocessing model.

            A model is a geoprocessing workflow represented as a diagram that strings together a sequence of tools, using the output of one tool as the input to another tool. You create, edit, and manage models in ModelBuilder.<disabledText>Not available yet.</disabledText>
          </tooltip>
        </button>

        <button id="esri_geoprocessing_Toolbox_SaveAs" hidden="true" keytip="SA" caption="Save Toolbox to Version" className="esri_geoprocessing_module:Commands.OnSaveAsToolbox" helpContextID=""
                condition="esri_geoprocessing_toolbox_valid">
          <tooltip heading="Save Toolbox to Version">
            Save toolbox to a specified version<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_geoprocessing_repairToolbox" hidden="true" keytip="RT" caption="Repair Toolbox" className="esri_geoprocessing_module:Commands.OnRepairToolbox" helpContextID=""
                condition="esri_geoprocessing_toolbox_broken">
          <tooltip heading="Try to repair broken toolbox">
            Repair broken toolbox<disabledText></disabledText>
          </tooltip>
        </button>

        <!-- className="esri_geoprocessing_module:Commands.OnCopy"  -->
        <button id="esri_geoprocessing_editCopy" hidden="true" caption="Copy" extendedCaption="Geoprocessing tool"
                className="ArcGIS.Desktop.Framework.CopyCMD" assembly="ArcGIS.Desktop.Framework.dll" loadOnClick="false"
                helpContextID=""
                smallImage="EditCopy16" >
          <tooltip heading="Copy">
            Copy the selected tool.
          </tooltip>
        </button>

        <!-- className="esri_geoprocessing_module:Commands.OnPaste"  -->
        <button id="esri_geoprocessing_pasteTool" hidden="true" caption="Paste" extendedCaption="Geoprocessing tool"
                className="ArcGIS.Desktop.Framework.PasteCMD" assembly="ArcGIS.Desktop.Framework.dll" loadOnClick="false"
                helpContextID="" menuKeytip="P" smallImage="EditPaste16">
          <tooltip heading="Paste">Paste the copied tool into the toolbox.</tooltip>
        </button>
        <button id="esri_geoprocessing_editPythonToolbox" hidden="true" caption="Edit" extendedCaption="Python toolbox" className="esri_geoprocessing_module:Commands.OnEditPythonToolbox" helpContextID="" loadOnClick="false">
          <tooltip heading="Edit">
            Edit Python toolbox<disabledText>Decrypt toolbox to edit</disabledText>
          </tooltip>

        </button>

        <dynamicMenu id="esri_modelbuilder_Variable_NodeDynamicMenu" hidden="true" caption="Add to Display" helpContextID="" loadOnClick="true" extendedCaption="ModelBuilder" className="NodeRightClickDynamicMenu"/>
        <dynamicMenu id="esri_modelbuilder_connectElementsDynamicMenu" hidden="true" caption="Connect" extendedCaption="ModelBuilder" className="ConnectElementsDynamicMenu"/>
        <dynamicMenu id="esri_modelbuilder_makeVariableFromParameterDynamicMenu" hidden="true" caption="From Parameter" extendedCaption="ModelBuilder" className="MakeVariableFromParameterDynamicMenu"/>
        <dynamicMenu id="esri_modelbuilder_makeVariableFromEnvironmentDynamicMenu" hidden="true" caption="From Environment" extendedCaption="ModelBuilder" className="MakeVariableFromEnvironmentDynamicMenu"/>
        <!--<dynamicMenu id="esri_modelbuilder_toolSuggestionDynamicMenu" hidden="true" caption="Suggest Next Tool" extendedCaption="ModelBuilder" assembly="Extensions\Geoprocessing\ArcGIS.Desktop.GeoProcessing.dll" className="ArcGIS.Desktop.GeoProcessing.ToolSuggestView" smallImage="LightbulbOn16" largeImage="LightbulbOn32" /><dynamicMenu id="esri_modelbuilder_searchFilterDynamicMenu" hidden="true" caption="Search Filter" extendedCaption="ModelBuilder" className="SearchFilterDynamicMenu"/>-->
        <button id="esri_geoprocessing_checkPythonToolboxSyntax" hidden="true" caption="Check Syntax..." className="esri_geoprocessing_module:Commands.OnCheckPythonToolboxSyntax" helpContextID="" loadOnClick="false">
          <tooltip heading="Check Syntax">Check the tools inside the toolbox for syntax errors</tooltip>
        </button>
        <button id="esri_geoprocessing_resetPythonToolboxAccess" hidden="true" caption="Refresh Python Toolbox Access Permission" className="esri_geoprocessing_module:Commands.OnResetPythonToolboxAccess" helpContextID="" loadOnClick="false">
          <tooltip heading="Refresh Python Toolbox Access Permission">
            Reset Python toolbox access permission and reload the toolbox.
          </tooltip>
        </button>
        <button id="esri_geoprocessing_ItemUnpackOpenButton" className="esri_geoprocessing_module:Commands.OnAddPackageToProject" caption="Add To Project" smallImage="GenericAdd16" helpContextID="">
          <tooltip heading="">
            Unpack a geoprocessing package and add its contents to the project. Future changes to or downloads of the package are not reflected in the project.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_modelbuilder_exportGroupToModel" className="esri_geoprocessing_module:Commands.exportGroupToModel" hidden="true" caption="Save as Model" extendedCaption="ModelBuilder" helpContextID="" smallImage="ModelBuilderNewModel16" largeImage="ModelBuilderNewModel32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Export Group to a New Model">
            Takes the Group and all dependent elments and creates a submodel out of the group.
          </tooltip>
        </button>
        <button id="esri_modelbuilder_dissolveGroup" hidden="true" className="esri_geoprocessing_module:Commands.UnGroup" caption="UnGroup" extendedCaption="ModelBuilder" helpContextID="" smallImage="ModelBuilderUngroup16" largeImage="ModelBuilderUngroup32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="UnGroup">
            Dissolve group around elements.
          </tooltip>
        </button>
        <button id="esri_modelbuilder_renameGroup" className="esri_geoprocessing_module:Commands.renameGroup" hidden="true" caption="Rename" extendedCaption="ModelBuilder" helpContextID="" smallImage="" largeImage="" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Rename">
            Rename the Group.
          </tooltip>
        </button>
        <button id="esri_modelbuilder_AddSubGroup" className="esri_geoprocessing_module:Commands.AddToGroup" caption="Create Group" extendedCaption="ModelBuilder" helpContextID="" smallImage="ModelBuilderGroup16" largeImage="ModelBuilderGroup32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="Create Group">
            Adds a new Group to the current Group.
          </tooltip>
        </button>
        <button id="esri_modelbuilder_AutoLayoutGroup" className="esri_geoprocessing_module:Commands.AutoLayoutGroup" caption="Auto Layout Group" extendedCaption="ModelBuilder" helpContextID="" smallImage="ModelBuilderAutoLayout16" largeImage="ModelBuilderAutoLayout32" condition="esri_geoprocessing_modelBuilderPane">
          <tooltip heading="AutoLayout">
            Auto layout the group elements when a group is selected.
          </tooltip>
        </button>

        <button id="esri_datainterop_startWorkbench" className="esri_geoprocessing_module:Commands.StartWorkBench" caption="Workbench" keytip="WB"
                largeImage="FME_WB32" smallImage="FME_WB16" helpContextID="120003896" productID="esri_product_datainterop" condition="esri_geoprocessing_DataInterop_installed">
          <tooltip heading="Workbench application">
            Workbench is a powerful tool for data conversion, sharing, transformation, validation, and integration.<disabledText>Please check that the Data Interoperability Extension is installed and licensed. Administrators may download software from MyEsri according to the instructions in this article: https://support.esri.com/en/technical-article/000018698 </disabledText>
          </tooltip>
        </button>
        <button id="esri_datainterop_dataInspector" className="esri_geoprocessing_module:Commands.StartDataInspector" caption="Data Inspector" keytip="DI"
                largeImage="FMEInspector32" smallImage="FMEInspector16" helpContextID="" productID="esri_product_datainterop" condition="esri_geoprocessing_DataInterop_installed">
          <tooltip heading="Data Inspector application">
            Data Inspector lets you visualize and explore any format data against an ArcGIS Online background map and optionally export all or selected features to another format.<disabledText>Authorize and enable the Data Interoperability Extension to access this functionality</disabledText>
          </tooltip>
        </button>
        <button id="esri_datainterop_quickTranslator" className="esri_geoprocessing_module:Commands.StartQuickTranslator" caption="Quick Translator" keytip="QT"
                largeImage="FMEQuickTranslator32" smallImage="FMEQuickTranslator16" helpContextID="" productID="esri_product_datainterop" condition="esri_geoprocessing_DataInterop_installed">
          <tooltip heading="Quick Translator application">
            Quick Translator allows you to perform simple translations between formats or run a workspace you have previously configured.<disabledText>Authorize and enable the Data Interoperability Extension to access this functionality</disabledText>
          </tooltip>
        </button>
        <button id="esri_datainterop_newETLTool" className="NewETLToolButton" caption="Spatial ETL Tool"
                smallImage="FMEgpTool16"
                condition="esri_geoprocessing_toolbox_valid">
          <tooltip heading="">
            Add Spatial ETL Tool.<disabledText></disabledText>
          </tooltip>
        </button>
        <dynamicMenu id="esri_geoprocessing_NewETLTool" caption="Spatial ETL Tool" className="NewETLToolMenu"/>
        <dynamicMenu id="esri_geoprocessing_PythonToolboxEncryptionMenu" caption="Python Toolbox" className="PythonToolboxEncryptionMenu" />
        <dynamicMenu id="esri_geoprocessing_ToolboxDynamicMenu" caption="Toolbox Context Dynamic Menu" className="ToolboxContextDynamicMenu" />

        <customControl id="esri_geoprocessingAnalysisButton_readytouse" keytip="AO" caption="Ready To Use Tools" className="PortalGalleryCtrl" isDropDown="true" staysOpenOnClick="false" loadOnClick="true" disableIfBusy="false"
          largeImage="PortalAnalysis32" helpContextID="120003891"
          loadingMessage="Loading ..." condition="esri_mapping_mapTypeLinkChartState_false">
          <content className="PortalContent"/>
          <tooltip heading="Ready To Use Tools">
            Ready-To-Use Tools use ArcGIS Online hosted data and analysis capabilities.<disabledText>You must be signed in to ArcGIS Online as your active portal to use these tools.</disabledText>
          </tooltip>
        </customControl>

        <customControl id="esri_geoprocessingAnalysisButton_feature" keytip="PC" caption="Feature Analysis" className="PortalGalleryCtrl" isDropDown="true" staysOpenOnClick="false" loadOnClick="true" disableIfBusy="false"
          largeImage="PortalAnalysisFeature32"
          condition="esri_geoprocessing_PortalCondition" helpContextID="120003892"
          loadingMessage="Loading ...">
          <content className="PortalContent"/>
          <tooltip heading="Feature Analysis">
            Run Feature Analysis tools on your portal. These tools use portal layers as input and create new layers hosted on your portal as output.<disabledText>You must be connected to a portal that has feature analysis tools.</disabledText>
          </tooltip>
        </customControl>
        <customControl id="esri_geoprocessingAnalysisButton_raster" keytip="RC" caption="Raster Analysis" className="RAPortalGalleryCtrl" isDropDown="true" staysOpenOnClick="false" loadOnClick="true" disableIfBusy="false"
          largeImage="PortalAnalysisRaster32"
          condition="esri_geoprocessing_PortalCondition" helpContextID="120003893"
          loadingMessage="Loading ...">
          <content className="PortalContent"/>
          <tooltip heading="Raster Analysis">
            Run Raster Analysis tools on your portal. These tools use portal layers as input and create new layers hosted on your portal as output.
Additionally, use Raster Functions and the Raster Function Editor to build custom imagery and raster functions that you can submit to your portal for distributed processing. <disabledText>You must be connected to a portal with raster analysis tools.</disabledText>
          </tooltip>
        </customControl>

        <button id="esri_geoprocessing_encryptPyt" caption="Encrypt..." className="EncryptPythonToolboxButton" />
        <button id="esri_geoprocessing_decryptPyt" caption="Decrypt..." className="DecryptPythonToolboxButton" />

        <!-- BIM Controls -->
        <button id="esri_mapping_CreateBuildingSceneLayerPackageButton" keytip="CB" caption="Create Building Scene Layer" className="esri_geoprocessing_module:BimCommands.CreateSceneLayerPackage"
                largeImage="GeoprocessingToolbox_Management" smallImage="GeoprocessingToolbox_Management">
          <tooltip heading="Create Building Scene Layer Package">
            Create a building scene layer package from the Building Layer.<disabledText></disabledText>
          </tooltip>
        </button>

        <!-- CAD Controls -->
        <button id="esri_geoprocessing_cadDefineProjectionButton" keytip="DP" loadOnClick="false" caption="Define Projection" className="esri_geoprocessing_module:CadCommands.DefineProjection"
                        largeImage="GeoprocessingToolbox_Management" smallImage="GeoprocessingToolbox_Management">
          <tooltip heading="Define Projection">
            Change the spatial reference of the data source.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_pnotebook_New" hidden="true" caption="New" keytip="N" extendedCaption="New" className="esri_geoprocessing_module:Commands.AddOpenNewNotebook"
                loadOnClick="false" helpContextID="" smallImage="NewNotebook16"
                largeImage="NewNotebook32" condition="esri_geoprocessing_proNotebookPane">
          <tooltip heading="New" >
            Create new Python Notebook.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_pnotebook_AddOpenNewNotebook" hidden="true" caption="Python" keytip="O" extendedCaption="New Notebook" className="esri_geoprocessing_module:Commands.AddOpenNewNotebook"
                        loadOnClick="false" helpContextID="120003889" smallImage="NewNotebook16"
                        largeImage="NewNotebook32">
          <tooltip heading="New Notebook" >
            Create new Python Notebook.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_pnotebook_Save" hidden="true" caption="Save" keytip="S" extendedCaption="Save" className="esri_geoprocessing_module:Commands.SavePythonNotebook"
                loadOnClick="false" helpContextID="" smallImage="GenericSave16"
                largeImage="GenericSave32">
          <tooltip heading="Save (Ctrl+S)" >
            Save Python Notebook.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_pnotebook_Interrupt" hidden="true" caption="Interrupt Kernel" keytip="S" extendedCaption="Interrupt" className="esri_geoprocessing_module:Commands.InterruptPythonNotebook"
                loadOnClick="false" helpContextID="" smallImage="Cancel16"
                largeImage="Cancel32">
          <tooltip heading="Interrupt Kernel">
            Interrupts the notebook kernel.
            <disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_pnotebook_exportToPythonFile" hidden="true" caption="Export To Python File" extendedCaption="Export To Python"
                className="esri_geoprocessing_module:Commands.ExportNotebookToPython" keytip="EP"
                smallImage="GeoprocessingScript16"
                largeImage="GeoprocessingScript32" helpContextID="">
          <tooltip heading="Export To Python">
            Export notebook to python file.<disabledText></disabledText>
          </tooltip>
        </button>
        <button id="esri_pnotebook_exportToHTMLFile" hidden="true" caption="Export To HTML File" extendedCaption="Export To HTML"
                className="esri_geoprocessing_module:Commands.ExportNotebookToHtml" keytip="EH"
                smallImage="HTMLFile16"
                largeImage="HTMLFile32" helpContextID="">
          <tooltip heading="Export To HTML">
            Export notebook to HTML file.<disabledText></disabledText>
          </tooltip>
        </button>

        <button id="esri_pnotebook_exportToPython" hidden="true" caption="Export" extendedCaption="Export"
                className="esri_geoprocessing_module:Commands.ExportNotebookToPython"
                smallImage="GeoprocessingScript16"
                largeImage="GeoprocessingScript32" helpContextID="">
          <tooltip heading="Export To Python">
            Export Notebook to Python file.<disabledText></disabledText>
          </tooltip>
        </button>
        <!--<button id="esri_pnotebook_Saveas" hidden="true" caption="Save as" keytip="A" extendedCaption="Save As" className="esri_geoprocessing_module:Commands.SavePythonNotebookAs" loadOnClick="false" helpContextID="" smallImage="EditingSaveEdits_B_16" largeImage="EditingSaveEdits_B_32">
          <tooltip heading="Save as" >
            Save Python notebook with a new name.
            <disabledText></disabledText>
          </tooltip>
        </button>-->

        <!-- Linear Referencing buttons -->
        <button id="esri_geoprocessing_linearReferencingtoolsViewTabButton" className="esri_geoprocessing_module:Commands.ShowTools" caption="Tools" extendedCaption="Open Geoprocessing pane" keytip="TS"
        largeImage="GeoprocessingToolbox32"
        smallImage="GeoprocessingToolbox16"
        helpContextID="" disableIfBusy="false">
          <tooltip heading="Geoprocessing">
            Show the Geoprocessing pane.

You can search for a specific tool, see a list of favorite and recently run tools, and explore all tools and toolboxes that are included in ArcGIS Pro.<disabledText></disabledText>
          </tooltip>
        </button>

        <button className="esri_geoprocessing_module:Commands.OnCalibrateRoutes" id="esri_linearReferencing_calibrateRoutesButton" loadOnClick="false" caption="Calibrate Routes" extendedCaption="Calibrate Routes" keytip="CA"
                largeImage="GPCalibrateRoutes32"
                smallImage="GPCalibrateRoutes16">
          <tooltip heading="">
            Recalculates route measures using points.<disabledText>This tool does not support Calibrate Routes.</disabledText>
          </tooltip>
        </button>
        <button className="esri_geoprocessing_module:Commands.OnCreateRoutesButton" id="esri_linearReferencing_createRoutesButton" loadOnClick="false" caption="Create Routes" extendedCaption="Create Routes" keytip="CR"
                largeImage="GPCreateRoute16"
                smallImage="GPCreateRoute32">
          <tooltip heading="">
            Creates routes from existing lines. The input line features that share a common identifier are merged to create a single route.<disabledText>This tool does not support Create Routes.</disabledText>
          </tooltip>
        </button>
        <button className="esri_geoprocessing_module:Commands.OnDissolveRouteEventsButton" id="esri_linearReferencing_dissolveRouteEventsButton" loadOnClick="false" caption="Dissolve Route Events" extendedCaption="Dissolve Route Events" keytip="DI"
                largeImage="GPDissolveRouteEvents16"
                smallImage="GPDissolveRouteEvents32">
          <tooltip heading="">
            Removes redundant information from event tables or separates event tables having more than one descriptive attribute into individual tables.<disabledText>This tool does not support Dissolve Route Events.</disabledText>
          </tooltip>
        </button>
        <button className="esri_geoprocessing_module:Commands.OnLocateFeaturesAlongRoutesButton" id="esri_linearReferencing_locateFeaturesAlongRoutesButton" loadOnClick="false" caption="Locate Features Along Routes" extendedCaption="Locate Features Along Routes" keytip="LO"
                largeImage="GPLocateFeatureAlongRoutes16"
                smallImage="GPLocateFeatureAlongRoutes32">
          <tooltip heading="">
            Computes the intersection of input features (point, line, or polygon) and route features and writes the route and measure information to a new event table.<disabledText>This tool does not support Locate Features Along Routes.</disabledText>
          </tooltip>
        </button>
        <button className="esri_geoprocessing_module:Commands.OnMakeRouteEventLayerButton" id="esri_linearReferencing_makeRouteEventLayerButton" loadOnClick="false" caption="Make Route Event Layer" extendedCaption="Make Route Event Layer" keytip="MA"
                largeImage="GPMakeRouteEventLayer32"
                smallImage="GPMakeRouteEventLayer16">
          <tooltip heading="">
            Creates a temporary feature layer using routes and route events.<disabledText>This tool does not support Make Route Event Layer.</disabledText>
          </tooltip>
        </button>
        <button className="esri_geoprocessing_module:Commands.OnOverlayRouteEventsButton" id="esri_linearReferencing_overlayRouteEventsButton" loadOnClick="false" caption="Overlay Route Events" extendedCaption="Overlay Route Events" keytip="OV"
                largeImage="GPOverlayRouteEvents16"
                smallImage="GPOverlayRouteEvents32">
          <tooltip heading="">
            Overlays two event tables to create an output event table that represents the union or intersection of the input.<disabledText>This tool does not support Overlay Route Events.</disabledText>
          </tooltip>
        </button>
        <button className="esri_geoprocessing_module:Commands.OnTransformRouteEventsButton" id="esri_linearReferencing_transformRouteEventsButton" loadOnClick="false" caption="Transform Route Events" extendedCaption="Transform Route Events" keytip="TR"
                largeImage="GPTransformRouteEvents16"
                smallImage="GPTransformRouteEvents32">
          <tooltip heading="">
            Transforms the measures of events from one route reference to another and writes them to a new event table.<disabledText>This tool does not support Transform Route Events.</disabledText>
          </tooltip>
        </button>

      </controls>

      <panes>
        <pane id="esri_geoprocessing_modelBuilderPane" caption="ModelBuilder" className="ModelBuilderPaneViewModel" smallImage="GeoprocessingModel16" isClosable="true" defaultTab="esri_geoprocessing_MBHomeTab">
          <content className="ModelBuilderPaneView" />
        </pane>
        <pane id="esri_geoprocessing_MBReportPane" caption="ModelReport" className="ModelBuilderReportViewModel" smallImage="GeoprocessingModel16" isClosable="true" defaultTab="esri_geoprocessing_MBHomeTab">
          <content className="ModelBuilderReportView" />
        </pane>
        <pane id="esri_geoprocessing_proNotebookPane" caption="ProNotebook" className="ProNotebookPaneViewModel" smallImage="PythonNotebook16" isClosable="true">
          <content className="ProNotebookPaneView" />
        </pane>
      </panes>

      <dockPanes>
        <dockPane id="esri_geoprocessing_toolBoxes" smallImage="GeoprocessingToolbox16" caption="Geoprocessing" className="GPDocPaneViewModel" dock="group" initiallyVisible="false" dockWith="esri_core_projectDockPane">
          <content className="GPDocPaneView" />
        </dockPane>
        <dockPane id="esri_geoprocessing_pythonWindow" smallImage="GeoprocessingPythonWindowShow16" caption="Python" className="PythonWindowDockPaneViewModel" dock="bottom" initiallyVisible="false" minheight="64" height="124" hasHelp="true" helpContextID="120001016">
          <content className="PythonWindowView" />
        </dockPane>
        <dockPane id="esri_geoprocessing_dataEngineeringDockPaneFunction" smallImage="DataEngineeringGeneral16" caption="Data Engineering Functions" className="DEDockPaneFunctionViewModel" dock="right" initiallyVisible="false" minheight="64" height="124" hasHelp="true" condition="esri_geoprocessing_anyDataEngineeringPane">
          <content className="DEDockPaneFunction" />
        </dockPane>
        <!--<dockPane id="esri_geoprocessing_NewBigDataConnectionDockPane" caption="New Big Data Connection" className="ArcGIS.Desktop.GeoProcessing.BDC.BDCWizardDockPaneViewModel" dock="right" initiallyVisible="false" minheight="64" height="124" hasHelp="true" >
           <content className="ArcGIS.Desktop.GeoProcessing.BDC.BDCWizardDockPaneView" />
        </dockPane>-->
      </dockPanes>

      <palettes>
        <buttonPalette id="esri_geoprocessing_selectByPalette" dropDown="true" showItemCaption="false" itemsInRow="1" menuStyle="true">
          <button refID="esri_geoprocessing_selectByAttributeButton" />
          <button refID="esri_geoprocessing_selectByLocationButton" />
        </buttonPalette>
        <toolPalette id="esri_geoprocessing_bimSelectToolPalette" itemsInRow="1" showItemCaption="true" caption="Select" extendedCaption="Open select tool palette" keytip="SE">
          <tool refID="esri_mapping_selectByRectangleTool" />
          <tool refID="esri_mapping_selectByPolygonTool" />
          <tool refID="esri_mapping_selectByLassoTool" />
          <tool refID="esri_mapping_selectByCircleTool" />
          <tool refID="esri_mapping_selectByLineTool" />
          <tool refID="esri_mapping_selectByTraceTool"/>
        </toolPalette>

        <toolPalette  id="esri_modelbuilder_linkShapeToolPalette" caption="Link Shape" keytip="LT" dropDown="true" showItemCaption="true" itemsInRow="1" menuStyle="true"
          smallImage="ModelBuilderLinkPolyline16"
          largeImage="ModelBuilderLinkPolyline32">
          <tooltip heading="Link Shape">
            The shape of the connector used while drawing, or after Auto Layout is applied.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_bezierLinkTypeButton"/>
          <button refID="esri_modelbuilder_cascadeLinkTypeButton"/>
          <button refID="esri_modelbuilder_polylineLinkTypeButton"/>
          <button refID="esri_modelbuilder_splineLinkTypeButton"/>
        </toolPalette >
        <toolPalette id="esri_dataInterop_FMEToolPalette" showItemCaption="true" caption="Data Interop" keytip="WL">
          <tool refID="esri_datainterop_startWorkbench"/>
          <tool refID="esri_datainterop_dataInspector"/>
          <tool refID="esri_datainterop_quickTranslator"/>
        </toolPalette>
      </palettes>

      <menus>
        <menu id="esri_modelbuilder_gridSettingsPallette" caption="Grid Setting" keytip="GS" dropDown="true" hidden="false" showItemCaption="true" menuStyle="true" itemsInRow="3" condition="esri_geoprocessing_mb_usegridrouter"
                smallImage="ModelBuilderGridSize16"
                largeImage="ModelBuilderGridSize32">
          <tooltip heading="Grid Setting">
            Grid settings used to route the links.<disabledText></disabledText>
          </tooltip>
          <customControl refID="esri_modelbuilder_gridRouterSettings" />

        </menu>

        <menu id="esri_modelbuilder_utilitiesPalette" caption="Utilities" keytip="UT" dropDown="true" hidden ="false"
                showItemCaption="true" itemsInRow="1" menuStyle="true"
                smallImage="ModelBuilderAddModelOnlyTools16"
                largeImage="ModelBuilderAddModelOnlyTools32">
          <tooltip heading="Utilities">
            ModelBuilder utilities include diverse operations all focused on extending the capabilities of a model.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_calculateValueButton" />
          <button refID="esri_modelbuilder_collectValuesButton" />
          <button refID="esri_modelbuilder_getFieldValueButton"/>
          <!--<button refID="esri_modelbuilder_parsePathButton" />-->
          <button refID="esri_modelbuilder_parsePathExtButton" />
          <button refID="esri_modelbuilder_selectDataButton"/>
          <button refID="esri_modelbuilder_customMessageButton"/>
        </menu>

        <menu id="esri_modelbuilder_iteratorPalette" caption="Iterators" keytip="IT" dropDown="true" hidden ="false"
              smallImage="ModelBuilderIterator16"
              largeImage="ModelBuilderIterator32"
              showItemCaption="true" itemsInRow="1" menuStyle="true">
          <tooltip heading="Iterators">
            Iterators help repeat process/es on a set of input data or values.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_iterateCountButton" />
          <button refID="esri_modelbuilder_iterateConditionButton" />
          <button refID="esri_modelbuilder_iterateFeatureSelectionButton" />
          <button refID="esri_modelbuilder_iterateRowSelectionButton" />
          <button refID="esri_modelbuilder_IterateFieldsButton" />
          <button refID="esri_modelbuilder_iterateFieldValuesButton" />
          <button refID="esri_modelbuilder_iterateMultivalueButton" />
          <button refID="esri_modelbuilder_iterateDatasetsButton" />
          <button refID="esri_modelbuilder_iterateFeatureClassesButton" />
          <button refID="esri_modelbuilder_iterateFilesButton" />
          <button refID="esri_modelbuilder_iterateLayersButton" />
          <button refID="esri_modelbuilder_iterateTimeButton" />
          <button refID="esri_modelbuilder_iterateRastersButton" />
          <button refID="esri_modelbuilder_iterateTablesButton" />
          <button refID="esri_modelbuilder_iterateWorkspacesButton" />
        </menu>

        <menu  id="esri_modelbuilder_conditionalPalette" caption="Logical" keytip="LT" dropDown="true" hidden ="false"
               smallImage="ModelBuilder_If16"
               largeImage="ModelBuilder_If32"
               showItemCaption="true" itemsInRow="1" menuStyle="true">
          <tooltip heading="Logical">
            Logical tools help you create and merge "if-then-else" branches to control the flow of model processes.<disabledText>Make a model the active view.</disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_IfDataExistsButton" />
          <button refID="esri_modelbuilder_IfFieldExistsButton" />
          <button refID="esri_modelbuilder_IfSelectionExistsButton" />
          <button refID="esri_modelbuilder_IfCoordinationSystemIsButton" />
          <button refID="esri_modelbuilder_IfDataTypeIsButton" />
          <button refID="esri_modelbuilder_IfExpressionIsButton" />
          <button refID="esri_modelbuilder_IfFeatureTypeIsButton" />
          <button refID="esri_modelbuilder_IfFieldValueIsButton" />
          <button refID="esri_modelbuilder_IfRowCountIsButton" />
          <button refID="esri_modelbuilder_IfSpatialRelationshipExistsButton" />
          <button refID="esri_modelbuilder_IfValueIsButton" />
          <button refID="esri_modelbuilder_mergeBranchButton" separator="true"/>
          <button refID="esri_modelbuilder_stopButton" />
        </menu>

        <menu id="esri_geoprocessing_insertMenu" caption="Toolbox" extendedCaption="Add geoprocessing toolbox to project" keytip="TX" largeImage="GeoprocessingToolbox32" smallImage="GeoprocessingToolbox16" helpContextID="120003956">
          <tooltip heading="Toolbox">
            Add geoprocessing tools to your project.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_geoprocessing_addToolboxButton"/>
          <button refID="esri_geoprocessing_newToolboxButton_atbx"/>
          <button refID="esri_geoprocessing_newPythonToolboxButton" />
          <button refID="esri_core_refresh" separator="true" />
        </menu>

        <menu id="esri_makeDefaultGPSubMenu" caption="Make Default and Remove From Project" hidden="true">
          <button refID="esri_geoprocessing_Toolbox_MakeDefaultButton" separator="true"/>
          <button refID="esri_geoprocessing_ProjectItem_Toolbox_remove"/>
        </menu>

        <menu id="esri_geoprocessing_ToolboxProjectItemMenu" hidden="true" caption="GP Toolbox Project Item" contextMenu="true">
          <menu refID="esri_geoprocessing_Toolbox_AddMenu" />
          <button refID="esri_geoprocessing_Toolbox_MakeDefaultButton" />
          <button refID="esri_core_refresh" separator="true"/>
          <button refID="esri_core_rename"/>
          <button refID="esri_core_openFileLocation"/>
          <button refID="esri_geoprocessing_Toolbox_SaveAs" />
          <button refID="esri_geoprocessing_pasteTool" separator="true" />
          <button refID="esri_geoprocessing_ProjectItem_Toolbox_remove" separator="true"/>
          <button refID="esri_core_editDeleteButton"/>
          <button refID="esri_projectItemViewMetadata" separator="true"/>
          <button refID="esri_projectItemEditMetadata" separator="false"/>
          <button refID="esri_geoprocessing_editToolboxProperties" separator="true"/>
        </menu>

        <menu id="esri_geoprocessing_FileToolboxProjectItemMenu" hidden="true" caption="GP Toolbox Project Item" contextMenu="true">
          <menu refID="esri_geoprocessing_Toolbox_AddMenu" />
          <button refID="esri_geoprocessing_Toolbox_MakeDefaultButton" separator="true"/>
          <button refID="esri_geoprocessing_ProjectItem_Toolbox_remove"/>
          <button refID="esri_addFavoriteButton" separator="true"/>
          <button refID="esri_addProjectItemToNewProjectsButton"/>
          <button refID="esri_geoprocessing_Toolbox_SaveAs"  separator="true"/>
          <button refID="esri_core_refresh" separator="true"/>
          <button refID="esri_geoprocessing_pasteTool" separator="true" />
          <button refID="esri_core_editCopyPaths"/>
          <button refID="esri_core_editDeleteButton" separator="true"/>
          <button refID="esri_core_rename"/>
          <button refID="esri_core_openFileLocation"/>
          <menu refID="esri_metadataSubMenu" inline="true" separator="true"/>
          <button refID="esri_geoprocessing_editToolboxProperties" separator="true"/>
        </menu>
        <!--<menu id="esri_cutCopyPasteGPSubMenu" caption="Cut and Copy and Copy Path and Paste in GP" hidden="true">
          <button refID="esri_core_editCutButton" separator="true" />
          <button refID="esri_core_editCopyButton" />
          <button refID="esri_core_editCopyPaths"/>
          <button refID ="esri_geoprocessing_pasteTool"/>
        </menu>-->
        <!--<menu id="esri_deleteRenameOpenFolderGPSubMenu" caption="Delete and Rename and Open Folder" hidden="true">
          <button refID="esri_geoprocessing_deleteToolbox" />
          <button refID="esri_core_rename"/>
          <button refID="esri_core_openFileLocation"/>
        </menu>-->
        <menu id="esri_geoprocessing_ToolboxMenu" hidden="true" caption="GP Toolbox Project Item" contextMenu="true">
          <menu refID="esri_geoprocessing_Toolbox_AddMenu" />
          <button refID="esri_core_addToProject" separator="true"/>
          <button refID="esri_addFavoriteButton" separator="true"/>
          <button refID="esri_addProjectItemToNewProjectsButton"/>
          <menu refID="esri_geoprocessing_Toolbox_SaveAsMenu" separator="true" />
          <button refID="esri_geoprocessing_Toolbox_SaveAs" separator="true" />
          <button refID="esri_core_refresh" separator="true"/>
          <button refID="esri_core_editCutButton" separator="true" />
          <button refID="esri_core_editCopyButton" />
          <button refID ="esri_geoprocessing_pasteTool"/>
          <button refID="esri_core_editCopyPaths"/>
          <button refID="esri_core_editDeleteButton" separator="true"/>
          <button refID="esri_core_rename"/>
          <menu refID="esri_metadataSubMenu" inline="true" separator="true"/>
          <button refID="esri_geoprocessing_editToolboxProperties" separator="true"/>
        </menu>
        <menu id="esri_geoprocessing_FileToolboxMenu" hidden="true" caption="GP Toolbox Project Item" contextMenu="true">
          <menu refID="esri_geoprocessing_Toolbox_AddMenu" />
          <button refID="esri_core_addToProject" separator="true" />
          <button refID="esri_addFavoriteButton" separator="true"/>
          <button refID="esri_addProjectItemToNewProjectsButton"/>
          <menu refID="esri_geoprocessing_Toolbox_SaveAsMenu" separator="true" />
          <button refID="esri_geoprocessing_Toolbox_SaveAs" separator="true"/>
          <button refID="esri_core_refresh" separator="true"/>
          <button refID="esri_core_editCutButton" separator="true" />
          <button refID="esri_core_editCopyButton" />
          <button refID="esri_geoprocessing_pasteTool"/>
          <button refID="esri_core_editCopyPaths"/>
          <button refID="esri_core_editDeleteButton" separator="true"/>
          <button refID="esri_core_rename"/>
          <button refID="esri_core_openFileLocation"/>
          <dynamicMenu refID="esri_geoprocessing_ToolboxDynamicMenu" inline="true"/>
          <menu refID="esri_metadataSubMenu" inline="true" separator="true"/>
          <button refID="esri_geoprocessing_editToolboxProperties" separator="true"/>
        </menu>
        <menu id="esri_geoprocessing_PythonToolboxProjectMenu" hidden="true" caption="GP Python Toolbox Project Item" contextMenu="true">
          <button refID="esri_geoprocessing_editPythonToolbox" />
          <button refID="esri_geoprocessing_checkPythonToolboxSyntax" />
          <dynamicMenu refID="esri_geoprocessing_PythonToolboxEncryptionMenu" inline="true" />
          <menu refID="esri_makeDefaultGPSubMenu" inline="true" separator="true"/>
          <button refID="esri_addFavoriteButton" separator="true"/>
          <button refID="esri_addProjectItemToNewProjectsButton"/>
          <button refID="esri_core_refresh" separator="true" />
          <button refID="esri_core_editCopyButton" separator="true"/>
          <button refID="esri_core_editCopyPaths"/>
          <button refID="esri_core_editDeleteButton" separator="true"/>
          <button refID="esri_core_rename"/>
          <button refID="esri_core_openFileLocation"/>
          <menu refID="esri_metadataSubMenu" inline="true" separator="true"/>
          <button refID="esri_geoprocessing_editToolboxProperties" separator="true"/>
        </menu>
        <menu id="esri_geoprocessing_FilePythonToolboxProjectMenu" hidden="true" caption="GP Python Toolbox Project Item" contextMenu="true">
          <button refID="esri_geoprocessing_editPythonToolbox" />
          <button refID="esri_geoprocessing_checkPythonToolboxSyntax" />
          <button refID="esri_geoprocessing_resetPythonToolboxAccess" />
          <dynamicMenu refID="esri_geoprocessing_PythonToolboxEncryptionMenu" inline="true" />
          <button refID="esri_geoprocessing_Toolbox_MakeDefaultButton" separator="true" />
          <button refID="esri_geoprocessing_ProjectItem_Toolbox_remove" />
          <button refID="esri_addFavoriteButton" separator="true"/>
          <button refID="esri_addProjectItemToNewProjectsButton"/>
          <button refID="esri_core_refresh" separator="true" />
          <button refID="esri_core_editCopyButton" separator="true"/>
          <button refID="esri_core_editCopyPaths"/>
          <button refID="esri_core_editDeleteButton" separator="true"/>
          <button refID="esri_core_rename"/>
          <button refID="esri_core_openFileLocation"/>
          <menu refID="esri_metadataSubMenu" inline="true" separator="true"/>
          <button refID="esri_geoprocessing_editToolboxProperties" separator="true"/>
        </menu>
        <menu id="esri_geoprocessing_PythonToolboxMenu" hidden="true" caption="GP Toolbox Project Item" contextMenu="true">
          <button refID="esri_core_addToProject" />
          <button refID="esri_geoprocessing_editPythonToolbox"/>
          <button refID="esri_geoprocessing_checkPythonToolboxSyntax" />
          <button refID="esri_geoprocessing_resetPythonToolboxAccess" />
          <dynamicMenu refID="esri_geoprocessing_PythonToolboxEncryptionMenu" inline="true" />
          <button refID="esri_core_refresh" separator="true"/>
          <button refID="esri_core_rename"/>
          <button refID="esri_core_openFileLocation"/>
          <button refID="esri_core_editCopyButton" separator="true"/>
          <button refID="esri_core_editDeleteButton" separator="false"/>
          <button refID="esri_projectItemViewMetadata" separator="true"/>
          <button refID="esri_projectItemEditMetadata" separator="false"/>
          <button refID="esri_geoprocessing_editToolboxProperties" separator="true"/>
        </menu>
        <menu id="esri_geoprocessing_FilePythonToolboxMenu" hidden="true" caption="GP Toolbox Project Item" contextMenu="true">
          <button refID="esri_geoprocessing_editPythonToolbox"/>
          <button refID="esri_geoprocessing_checkPythonToolboxSyntax" />
          <button refID="esri_geoprocessing_resetPythonToolboxAccess" />
          <dynamicMenu refID="esri_geoprocessing_PythonToolboxEncryptionMenu" inline="true" />
          <button refID="esri_core_addToProject" separator="true"/>
          <button refID="esri_addFavoriteButton" separator="true"/>
          <button refID="esri_addProjectItemToNewProjectsButton"/>
          <button refID="esri_core_refresh" separator="true"/>
          <button refID="esri_core_editCutButton" separator="true"/>
          <button refID="esri_core_editCopyButton"/>
          <button refID="esri_core_editCopyPaths"/>
          <button refID="esri_core_editDeleteButton" separator="true"/>
          <button refID="esri_core_rename"/>
          <button refID="esri_core_openFileLocation"/>
          <menu refID="esri_metadataSubMenu" inline="true" separator="true"/>
          <button refID="esri_geoprocessing_editToolboxProperties" separator="true"/>
        </menu>
        <menu id="esri_geoprocessing_ServerToolboxMenu" hidden="true" caption="GP Server Toolbox Project Item" contextMenu="true">
          <button refID="esri_core_refresh"/>
          <button refID="esri_projectItemViewMetadata" separator="true"/>
          <button refID="esri_geoprocessing_serviceInformation"/>
        </menu>
        <menu id="esri_geoprocessing_ToolsetMenu" hidden="true" caption="Toolset menu" contextMenu="true">
          <menu refID="esri_geoprocessing_Toolbox_AddMenu"/>
          <button refID="esri_core_editCutButton" separator="true"/>
          <button refID="esri_geoprocessing_pasteTool"/>
          <button refID="esri_core_editCopyPaths" />
          <button refID="esri_core_editDeleteButton" separator="true"/>
          <button refID="esri_core_rename"/>
        </menu>

        <menu id="esri_geoprocessing_Toolbox_AddMenu" hidden="true" caption="New" extendedCaption="Add new item to geoprocessing toolbox" contextMenu="true" largeImage="GenericNewSparkleLarge32" smallImage="GenericNewSparkleLarge16"
              condition="esri_geoprocessing_toolbox_valid">
          <button refID="esri_geoprocessing_Add_ModelTool" />
          <button refID="esri_geoprocessing_Add_ScriptTool" />
          <button refID="esri_geoprocessing_Add_Toolset" />
          <dynamicMenu refID="esri_geoprocessing_NewETLTool" inline="true" />
        </menu>

        <!--<menu id="esri_geoprocessing_Toolbox_SaveAsMenu" hidden="true" caption="Save As" extendedCaption="Geoprocessing toolbox" contextMenu="true">
          <button refID="esri_geoprocessing_saveAsToolbox106" />
          <button refID="esri_geoprocessing_saveAsToolbox107" />
          <button refID="esri_geoprocessing_saveAsToolbox108" />
          <button refID="esri_geoprocessing_saveAsToolbox22" />
          <button refID="esri_geoprocessing_saveAsToolbox23" />
          <button refID="esri_geoprocessing_saveAsToolbox24" />
          <button refID="esri_geoprocessing_saveAsToolbox25" />
          <button refID="esri_geoprocessing_saveAsToolbox26" />
          <button refID="esri_geoprocessing_saveAsToolbox27" />
          <button refID="esri_geoprocessing_saveAsToolbox28" />
          <button refID="esri_geoprocessing_saveAsToolbox29" />
          <button refID="esri_geoprocessing_saveAsToolbox93" />
        </menu>-->
        <menu id="esri_geoprocessing_historyItemMenu" hidden="true" caption="History" extendedCaption="Geoprocessing History" contextMenu="true">
          <button refID="esri_geoprocessing_openToolHistory"/>
          <button refID="esri_geoprocessing_runToolFromHistory"/>
          <button refID="esri_geoprocessing_addToModelBuilder" seperator="true"/>
          <button refID="esri_geoprocessing_addToHistoryFavorities" seperator="true"/>
          <button refID="esri_geoprocessing_showResult"/>
          <button refID="esri_geoprocessing_copy_snippetHistory" separator="true"/>
          <button refID="esri_geoprocessing_sendToPythonWindow"/>
          <button refID="esri_geoprocessing_sendToNotebook"/>
          <button refID="esri_geoprocessing_makePythonScript"/>
          <button refID="esri_geoprocessing_helpCommand" separator="true"/>
          <button refID="esri_geoprocessing_removeToolHistory" separator="true"/>
        </menu>

        <menu id="esri_geoprocessing_historyItemMenuUnPin" hidden="true" caption="History" extendedCaption="Geoprocessing History" contextMenu="true">
          <button refID="esri_geoprocessing_openToolHistory"/>
          <button refID="esri_geoprocessing_runToolFromHistory"/>
          <button refID="esri_geoprocessing_addToModelBuilder" seperator="true"/>
          <button refID="esri_geoprocessing_removefromHistoryFavorities" seperator="true"/>
          <button refID="esri_geoprocessing_showResult"/>
          <button refID="esri_geoprocessing_copy_snippetHistory" separator="true"/>
          <button refID="esri_geoprocessing_sendToPythonWindow"/>
          <button refID="esri_geoprocessing_sendToNotebook"/>
          <button refID="esri_geoprocessing_makePythonScript"/>
          <button refID="esri_geoprocessing_helpCommand" separator="true"/>
          <button refID="esri_geoprocessing_removeToolHistory" separator="true"/>
        </menu>

        <menu id="esri_geoprocessing_historyScheduleMenu" hidden="true" caption="Schedule" extendedCaption="Geoprocessing Schedule" contextMenu="true">
          <button refID="esri_geoprocessing_EditSchedule"/>
          <button refID="esri_geoprocessing_runNow" separator="true"/>
          <button refID="esri_geoprocessing_ClearLogs"/>
          <button refID="esri_geoprocessing_PauseSchedule"/>
          <button refID="esri_geoprocessing_DeleteSchedule" separator="true"/>
        </menu>

        <menu id="esri_geoprocessing_historyPausedScheduleMenu" hidden="true" caption="Schedule" extendedCaption="Geoprocessing Schedule" contextMenu="true">
          <button refID="esri_geoprocessing_EditSchedule" />
          <button refID="esri_geoprocessing_ClearLogs" separator="true"/>
          <button refID="esri_geoprocessing_UnPauseSchedule"/>
          <button refID="esri_geoprocessing_DeleteSchedule" separator="true"/>
        </menu>

        <menu id="esri_geoprocessing_historyScheduleLogMenu" hidden="true" caption="Schedule Log Item" extendedCaption="Geoprocessing Schedule Log Item " contextMenu="true">
          <button refID="esri_geoprocessing_openToolHistory"/>
          <button refID="esri_geoprocessing_showResult"/>
          <button refID="esri_geoprocessing_removeToolHistory" separator="true"/>
        </menu>

        <menu id="esri_geoprocessing_historyContainerMenu" hidden="true" caption="History" contextMenu="true">
          <button refID="esri_geoprocessing_removeErrorsFromToolHistory" />
          <button refID="esri_geoprocessing_clearToolHistory" separator="true" />
        </menu>
        <menu id="esri_geoprocessing_SystemToolMenu" hidden="true" caption="SystemTools Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_editTool" />
          <button refID="esri_geoprocessing_BatchTool"/>
          <button refID="esri_geoprocessing_addToFavorities" separator="true"/>
          <button refID="esri_geoprocessing_addToUserFavorities" />
          <button refID="esri_geoprocessing_addToGallery" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_core_editCopyButton" separator="true" />
          <button refID="esri_geoprocessing_helpCommand" separator="true"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true" />
        </menu>
        <menu id="esri_geoprocessing_SystemScriptToolMenu" hidden="true" caption="SystemTools Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_editTool" />
          <button refID="esri_geoprocessing_BatchTool" />
          <button refID="esri_geoprocessing_addToFavorities" separator="true" />
          <button refID="esri_geoprocessing_addToGallery" />
          <button refID="esri_geoprocessing_addToUserFavorities" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_core_editCopyButton" separator="true" />
          <button refID="esri_geoprocessing_helpCommand" separator="true"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true" />
        </menu>
        <menu id="esri_geoprocessing_mainProgressMenu" hidden="true" caption="Progress Menu" contextMenu="true">
          <button refID="esri_geoprocessing_HideMainProgressor"/>
          <button refID="esri_geoprocessing_openToolHistory" separator="true"/>
          <button refID="esri_geoprocessing_showResult"/>
          <!--button refID="esri_geoprocessing_runToolFromHistory"/-->
          <button refID="esri_geoprocessing_copy_snippetHistory"/>
          <button refID="esri_geoprocessing_sendToPythonWindow"/>
          <button refID="esri_geoprocessing_sendToNotebook"/>
          <button refID="esri_geoprocessing_showToolHistory" separator="true"/>
        </menu>

        <menu id="esri_geoprocessing_ToolMenu" hidden="true" caption="Tool Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_editTool" />
          <button refID="esri_geoprocessing_BatchTool" separator="true"/>
          <button refID="esri_geoprocessing_addToFavorities" />
          <button refID="esri_geoprocessing_addToUserFavorities" />
          <button refID="esri_geoprocessing_addToGallery" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_core_editCopyButton" separator="true"/>
          <button refID="esri_core_rename" separator="true"/>
          <button refID="esri_core_editDeleteButton"/>
          <button refID="esri_projectItemViewMetadata" separator="true"/>
          <button refID="esri_projectItemEditMetadata" separator="false"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true"/>
        </menu>

        <menu id="esri_geoprocessing_CustomToolMenu" hidden="true" caption="Tool Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_editTool" />
          <button refID="esri_geoprocessing_addToFavorities" separator="true"/>
          <button refID="esri_geoprocessing_addToUserFavorities" />
          <button refID="esri_geoprocessing_addToGallery" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_core_editCopyButton" separator="true"/>
          <button refID="esri_core_editDeleteButton"/>
          <button refID="esri_projectItemViewMetadata" separator="true"/>
          <button refID="esri_projectItemEditMetadata" separator="false"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true"/>
        </menu>

        <menu id="esri_geoprocessing_ToolModelMenu" hidden="true" caption="Tool Model Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_editTool" />
          <button refID="esri_geoprocessing_BatchTool"/>
          <button refID="esri_geoprocessing_addToFavorities" separator="true"/>
          <button refID="esri_geoprocessing_addToUserFavorities" />
          <button refID="esri_geoprocessing_addToGallery" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_core_editCutButton"  separator="true" />
          <button refID="esri_core_editCopyButton"/>
          <button refID="esri_core_editCopyPaths"/>
          <button refID="esri_core_editDeleteButton" separator="true"/>
          <menu refID="esri_metadataSubMenu" inline="true" separator="true"/>
          <button refID="esri_geoprocessing_helpCommand" separator="true"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true"/>
        </menu>
        <menu id="esri_geoprocessing_PythonToolMenu" hidden="true" caption="Python Toolbox Tool" contextMenu="True">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_editPythonToolbox" />
          <button refID="esri_geoprocessing_BatchTool"/>
          <button refID="esri_geoprocessing_addToFavorities" separator="true"/>
          <button refID="esri_geoprocessing_addToUserFavorities" />
          <button refID="esri_geoprocessing_addToGallery" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_projectItemViewMetadata" separator="true"/>
          <button refID="esri_projectItemEditMetadata" separator="false"/>
          <button refID="esri_geoprocessing_helpCommand" separator="true"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true"/>
        </menu>
        <menu id="esri_geoprocessing_ServiceTaskMenu" hidden="true" caption="Geoprocessing Task Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_BatchTool"/>
          <button refID="esri_geoprocessing_addToGallery" separator="true" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_projectItemViewMetadata" separator="true"/>
          <button refID="esri_geoprocessing_taskInformation"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true"/>
        </menu>
        <menu id="esri_geoprocessing_SearchItemMenu" hidden="true" caption="Search Tool Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool"/>
          <button refID="esri_geoprocessing_editTool"/>
          <button refID="esri_geoprocessing_locateTool"/>
          <button refID="esri_geoprocessing_BatchTool"/>
          <button refID="esri_geoprocessing_addToFavorities" separator="true"/>
          <button refID="esri_geoprocessing_addToUserFavorities" />
          <button refID="esri_geoprocessing_addToGallery" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_geoprocessing_helpCommand" separator="true"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true"/>
        </menu>
        <menu id="esri_geoprocessing_FavoritesItemMenu" hidden="true" caption="Project Favorites Tool Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_editTool" />
          <button refID="esri_geoprocessing_locateTool"/>
          <button refID="esri_geoprocessing_BatchTool"/>
          <button refID="esri_geoprocessing_addToUserFavorities" separator="true"/>
          <button refID="esri_geoprocessing_addToGallery" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_geoprocessing_removeFromFavorities" separator="true" />
          <button refID="esri_geoprocessing_helpCommand" separator="true"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true" />
        </menu>
        <menu id="esri_geoprocessing_UserFavoritesItemMenu" hidden="true" caption="My Favorites Tool Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_editTool" />
          <button refID="esri_geoprocessing_locateTool"/>
          <button refID="esri_geoprocessing_BatchTool"/>
          <button refID="esri_geoprocessing_addToGallery" separator="true"/>
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_geoprocessing_removeFromUserFavorities" separator="true" />
          <button refID="esri_geoprocessing_helpCommand" separator="true"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true" />
        </menu>
        <menu id="esri_geoprocessing_GalleryItemMenu" hidden="true" caption="Gallery Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_geoprocessing_removeFromGallery" />
        </menu>
        <menu id="esri_geoprocessing_RecentItemMenu" hidden="true" caption="Recent Tool Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_editTool" />
          <button refID="esri_geoprocessing_locateTool"/>
          <button refID="esri_geoprocessing_BatchTool"/>
          <button refID="esri_geoprocessing_addToFavorities" separator="true"/>
          <button refID="esri_geoprocessing_addToUserFavorities"/>
          <button refID="esri_geoprocessing_addToGallery" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_geoprocessing_helpCommand" separator="true"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true" />
        </menu>
        <menu id="esri_geoprocessing_RecentUserFavItemMenu" hidden="true" caption="Recent Tool Item" contextMenu="true">
          <button refID="esri_geoprocessing_openTool" />
          <button refID="esri_geoprocessing_editTool" />
          <button refID="esri_geoprocessing_locateTool"/>
          <button refID="esri_geoprocessing_BatchTool"/>
          <button refID="esri_geoprocessing_addToFavorities" separator="true"/>
          <button refID="esri_geoprocessing_addToGallery" />
          <button refID="esri_geoprocessing_addToModelBuilder" />
          <button refID="esri_geoprocessing_removeFromUserFavorities" separator="true" />
          <button refID="esri_geoprocessing_helpCommand" separator="true"/>
          <button refID="esri_geoprocessing_editToolProperties" separator="true" />
        </menu>
        <menu id="esri_geoprocessing_ToolDialogTitleMenu" hidden="true" caption="GP Dialog Title Menu" contextMenu="true">
          <button refID="esri_geoprocessing_addToFavorities" />
          <button refID="esri_geoprocessing_addToUserFavorities"/>
          <button refID="esri_geoprocessing_addToGallery" />
        </menu>

        <menu id="esri_geoprocessing_tableJoinsMenu" caption="Joins" largeImage="TableJoins32" smallImage="TableJoins16" keytip="RJ" helpContextID="120003959">
          <tooltip heading="Joins">
            Menu to create, remove, or remove all joins.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_geoprocessing_tableAddJoinButton" />
          <button refID="esri_geoprocessing_tableRemoveJoinButton" />
          <button refID="esri_geoprocessing_tableRemoveAllJoinsButton" />
        </menu>

        <menu id="esri_geoprocessing_tableRelatesMenu" caption="Relates" largeImage="TableRelates32" smallImage="TableRelates16" keytip="RR" helpContextID="120003960">
          <tooltip heading="Relates">
            Menu to create, remove, or remove all relates.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_geoprocessing_tableAddRelateButton" />
          <button refID="esri_geoprocessing_tableRemoveRelateButton" />
          <button refID="esri_geoprocessing_tableRemoveAllRelatesButton" />
        </menu>

        <menu id="esri_geoprocessing_JoinsRelatesMenu" caption="Joins and Relates">
          <button refID="esri_geoprocessing_tableAddJoinButton" />
          <button refID="esri_geoprocessing_tableRemoveJoinButton" />
          <button refID="esri_geoprocessing_tableRemoveAllJoinsButton" />
          <button refID="esri_geoprocessing_spatialJoinsButton" separator="true" />
          <button refID="esri_geoprocessing_tableAddRelateButton" separator="true"/>
          <button refID="esri_geoprocessing_tableRemoveRelateButton" />
          <button refID="esri_geoprocessing_tableRemoveAllRelatesButton" />
        </menu>

        <menu id="esri_geoprocessing_StandaloneJoinsRelatesMenu" caption="Joins and Relates">
          <button refID="esri_geoprocessing_tableAddJoinButton" />
          <button refID="esri_geoprocessing_tableRemoveJoinButton" />
          <button refID="esri_geoprocessing_tableRemoveAllJoinsButton" />
          <button refID="esri_geoprocessing_tableAddRelateButton" separator="true"/>
          <button refID="esri_geoprocessing_tableRemoveRelateButton" />
          <button refID="esri_geoprocessing_tableRemoveAllRelatesButton" />
        </menu>

        <menu id="esri_geoprocessing_StandaloneCreatePointsFromTableMenu" caption="Create Points From Table" smallImage="TableToPoint16" largeImage="TableToPoint32" >
          <button refID="esri_geoprocessing_tableDisplayXYDataButton" />
          <button refID="esri_geoprocessing_tableMakeXYEventLayerButton" />
        </menu>
        <menu id="esri_geoprocessing_StandaloneCreatePointsFromTableRibbonMenu" caption="From Table" smallImage="TableToPoint16" largeImage="TableToPoint32" keytip="MP">
          <tooltip heading="Create Points From Table">
            Menu to create points from a standalone table.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_geoprocessing_tableDisplayXYDataButton" />
          <button refID="esri_geoprocessing_tableMakeXYEventLayerButton" />
        </menu>

        <!--Model Element Menus-->

        <menu id="esri_modelbuilder_ModelElement_Common_Top" hidden="true" caption="Variable">
          <button refID="esri_modelbuilder_openElementButton" />
        </menu>

        <menu id="esri_modelbuilder_ModelElement_Common_Bottom" hidden="true" caption="Variable">
          <button refID="esri_modelbuilder_createLabelButton"/>
          <button refID="esri_modelbuilder_renameElementButton"/>
          <button refID="esri_modelbuilder_groupButton" separator ="true"/>
          <button refID="esri_modelbuilder_cutElementButton" separator="true"/>
          <button refID="esri_modelbuilder_copyElementButton"/>
          <button refID="esri_modelbuilder_selectAllButton" />
        </menu>

        <!--<menu id="esri_modelbuilder_Variable_ParamOn" hidden="true" caption="Variable">
          <button refID="esri_modelbuilder_parameterOnButton"/>
        </menu>

        <menu id="esri_modelbuilder_Variable_ParamOff" hidden="true" caption="Variable">
          <button refID="esri_modelbuilder_parameterOffButton"/>
        </menu>-->

        <menu id="esri_modelbuilder_Variable_DynamicMenu" caption="Add 222" hidden="true">
          <!--<tooltip heading="Add To Display">
            <disabledText></disabledText>
          </tooltip>-->

          <button refID="esri_modelbuilder_openElementButton"/>

          <dynamicMenu refID="esri_modelbuilder_Variable_NodeDynamicMenu" inline="true"/>

          <button refID="esri_modelbuilder_createLabelButton"/>
          <button refID="esri_modelbuilder_renameElementButton"/>
          <button refID="esri_modelbuilder_groupButton" separator ="true"/>
          <button refID="esri_modelbuilder_cutElementButton" separator="true"/>
          <button refID="esri_modelbuilder_copyElementButton"/>
          <button refID="esri_modelbuilder_selectAllButton" />
        </menu>

        <!--<menu id="esri_modelbuilder_Variable_AddToDisplayOff" hidden="true" caption="Variable">
          <button refID="esri_modelBuilder_addToDisplayOffButton"/>
        </menu>-->

        <menu id="esri_modelbuilder_Variable_DeleteIntermediateDataOn" hidden="true" caption="Variable">
          <button refID="esri_modelBuilder_DeleteIntermediateDataOnButton"/>
        </menu>

        <menu id="esri_modelbuilder_Variable_DeleteIntermediateDataOff" hidden="true" caption="Variable">
          <button refID="esri_modelBuilder_DeleteIntermediateDataOffButton"/>
        </menu>

        <menu id="esri_modelbuilder_Process" hidden="true" caption="Process">
          <button refID="esri_modelbuilder_runProcessButton"/>
          <!--<menu   refID="esri_modelbuilder_toolSuggestionMenu" separator="true"/>-->
          <menu   refID="esri_modelbuilder_toolCreateVariableMenu" separator="true"/>
          <button refID="esri_modelbuilder_elementMessagesButton"/>
        </menu>

        <menu id="esri_modelbuilder_EditableProcess" hidden="true" caption="Process">
          <button refID="esri_modelbuilder_editProcessToolButton"/>
          <button refID="esri_modelbuilder_runProcessButton"/>
          <menu refID="esri_modelbuilder_toolCreateVariableMenu" separator="true"/>
          <button refID="esri_modelbuilder_elementMessagesButton"/>
        </menu>

        <menu id="esri_modelbuilder_toolMenu" hidden="true" caption="Tool" extendedCaption="ModelBuilder">
          <tooltip heading="Tool">
            Edit tool properties.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_runProcessButton"/>
          <!--<menu   refID="esri_modelbuilder_toolSuggestionMenu" separator="true"/>-->
          <menu refID="esri_modelbuilder_toolCreateVariableMenu" separator="true"/>
          <button refID="esri_modelbuilder_elementMessagesButton"/>
        </menu>

        <menu id="esri_modelbuilder_editToolMenu" hidden="true" caption="Tool" extendedCaption="ModelBuilder">
          <tooltip heading="Tool">
            Edit tool properties.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_editProcessToolButton"/>
        </menu>

        <menu id="esri_modelbuilder_labelMenu" hidden="true" caption="Variable">
          <button refID="esri_modelbuilder_renameElementButton"/>
          <button refID="esri_modelbuilder_groupButton" separator ="true"/>
          <button refID="esri_modelbuilder_cutElementButton" separator="true"/>
          <button refID="esri_modelbuilder_copyElementButton"/>
          <button refID="esri_modelbuilder_selectAllButton" />
        </menu>

        <!--Model Create variable Menu-->
        <menu id="esri_modelbuilder_toolCreateVariableMenu" hidden="true" caption="Create Variable">
          <tooltip heading="Create Variable">
            Create variable.<disabledText></disabledText>
          </tooltip>
          <dynamicMenu refID="esri_modelbuilder_makeVariableFromParameterDynamicMenu" inline="false"/>
          <dynamicMenu refID="esri_modelbuilder_makeVariableFromEnvironmentDynamicMenu" inline="false"/>
        </menu>

        <!--Model Suggest Tool Menu-->
        <menu id="esri_modelbuilder_nextToolSuggestionMenu" hidden="true" caption="Suggest Next Tools">
          <tooltip heading="Suggest Next Tool">
            Suggest Next Tool.<disabledText>Install Semantic Search to enable this functionality.</disabledText>
          </tooltip>
          <customControl refID="esri_modelbuilder_toolsSuggestion" />
        </menu>
        
        <!--Model Menu-->
        <menu id="esri_modelbuilder_modelMenu" hidden="true" caption="Model">
          <tooltip heading="Model">
            Edit model properties.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_createVariableButton"  separator="true"/>
          <button refID="esri_modelbuilder_createLabelButton"/>
          <button refID="esri_modelbuilder_validateButton" separator="true" />
          <button refID="esri_modelbuilder_runModelButton" />
          <button refID="esri_modelbuilder_groupButton" separator ="true"/>
          <button refID="esri_modelbuilder_groupExpandAll" />
          <button refID="esri_modelbuilder_groupCollapseAll"/>
          <button refID="esri_modelbuilder_autoLayoutDiagramButton" separator="true"/>
          <button refID="esri_modelbuilder_RouteAllLinksButton"/>
          <button refID="esri_modelbuilder_cutElementButton" separator="true"/>
          <button refID="esri_modelbuilder_copyElementButton"/>
          <button refID="esri_modelbuilder_pasteElementButton"/>
          <button refID="esri_modelbuilder_selectAllButton" />
          <button refID="esri_modelbuilder_panButton"  separator="true"/>
          <!--<button refID="esri_modelbuilder_undoModelButton" separator="true"/>-->
          <!--<button refID="esri_modelbuilder_redoModelButton"/>-->
        </menu>
        <menu id="esri_modelbuilder_modelMenu2" hidden="true" caption="Model">
          <tooltip heading="Model">
            Edit model properties.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_selectButton"/>
          <button refID="esri_modelbuilder_createVariableButton"  separator="true"/>
          <button refID="esri_modelbuilder_createLabelButton"/>
          <button refID="esri_modelbuilder_groupButton" separator ="true"/>
          <button refID="esri_modelbuilder_groupExpandAll" />
          <button refID="esri_modelbuilder_groupCollapseAll"/>
          <button refID="esri_modelbuilder_cutElementButton" separator="true"/>
          <button refID="esri_modelbuilder_copyElementButton"/>
          <button refID="esri_modelbuilder_pasteElementButton"/>
          <!--<button refID="esri_modelbuilder_undoModelButton" separator="true"/>-->
          <!--<button refID="esri_modelbuilder_redoModelButton"/>-->
        </menu>

        <!--Model Connection Menu-->
        <menu id="esri_modelbuilder_inputConnectionMenu" hidden="true" caption="Connection">
          <tooltip heading="Connection">
            Edit connection properties.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_cutConnectionButton"/>
          <button refID="esri_modelbuilder_RouteSelectedLinksButton"/>
        </menu>
        <menu id="esri_modelbuilder_outputConnectionMenu" hidden="true" caption="Connection">
          <tooltip heading="Connection">
            Edit connection properties.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_cutConnectionButton"/>
          <button refID="esri_modelbuilder_RouteSelectedLinksButton"/>
        </menu>
        <menu id="esri_modelbuilder_connectMenu" hidden="true" caption="Connect">
          <tooltip heading="Connect">
            Connect elements.<disabledText></disabledText>
          </tooltip>
          <dynamicMenu refID="esri_modelbuilder_connectElementsDynamicMenu" inline="true"/>
        </menu>

        <!--Model Group Menu-->
        <menu id="esri_modelbuilder_groupContextMenu" hidden="true" caption="Group">
          <tooltip heading="Group Menu">
            ModelBuilder Group.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_AddSubGroup" separator="true"/>
          <button refID="esri_modelbuilder_renameElementButton" />
          <button refID="esri_modelbuilder_dissolveGroup" />
          <button refID="esri_modelbuilder_exportGroupToModel" separator="true"/>
          <button refID="esri_modelbuilder_AutoLayoutGroup" separator="true" />
          <button refID="esri_modelbuilder_RouteAllGroupLinksButton" />
          <button refID="esri_modelbuilder_cutElementButton" separator="true"/>
          <button refID="esri_modelbuilder_copyElementButton" />
          <button refID="esri_modelbuilder_pasteElementButton"/>
          <button refID="esri_modelbuilder_selectAllGroupButton"/>
        </menu>
        <menu id="esri_modelbuilder_groupContextMenuFolded" hidden="true" caption="Group">
          <tooltip heading="Group Menu">
            ModelBuilder Group.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_modelbuilder_renameElementButton" />
          <button refID="esri_modelbuilder_dissolveGroup" />
          <button refID="esri_modelbuilder_exportGroupToModel" separator="true"/>
          <button refID="esri_modelbuilder_cutElementButton" separator="true"/>
          <button refID="esri_modelbuilder_copyElementButton" />
          <button refID="esri_modelbuilder_pasteElementButton"/>
          <button refID="esri_modelbuilder_selectAllGroupButton"/>
        </menu>

        <!--Model Search Menu-->
        <menu id="esri_modelbuilder_searchFilterContextMenu" hidden="true" caption="Filter">
          <tooltip heading="Search Filter">
            Select SearchFilter Items<disabledText></disabledText>
          </tooltip>
          <dynamicMenu refID="esri_modelbuilder_searchFilterDynamicMenu" inline="true"/>
        </menu>

        <!--Package Menu-->
        <menu id="esri_geoprocessing_packageMenu" hidden="true" caption="Package">
          <tooltip heading="Package">
            Geoprocessing Package.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_geoprocessing_ItemUnpackOpenButton"/>
          <button refID="esri_core_editCutButton" separator="true"/>
          <button refID="esri_core_editCopyButton"/>
          <button refID="esri_core_editCopyPaths"/>
          <button refID="esri_core_editDeleteButton" separator="true"/>
          <button refID="esri_core_rename"/>
          <button refID="esri_core_openFileLocation"/>
          <button refID="esri_projectItemViewMetadata" separator="true"/>
        </menu>

        <!--Geoprocessing Sample menu-->
        <menu id="esri_geoprocessing_sampleMenu" hidden="true" caption="Geoprocessing Sample">
          <!--<tooltip heading="Geoprocessing Sample">
            Geoprocessing Sample.<disabledText></disabledText>
          </tooltip>-->
          <button refID="esri_geoprocessing_ItemUnpackOpenButton"/>
          <!--<button refID="esri_projectItemViewMetadata" separator="true"/>
          <button refID="esri_core_editCopyButton" separator="true"/>
          <button refID="esri_core_editDeleteButton"/>
          <button refID="esri_core_rename"/>-->
        </menu>

        <!-- Cad Gallery Menu-->
        <menu id="esri_geoprocessing_CadGalleryItemMenu" hidden="true" caption="Gallery Items" contextMenu="true">
          <button refID="esri_geoprocessing_openTool"/>
        </menu>
        <!-- Cad Gallery Menu-->

        <!-- Bim Gallery Menu-->
        <menu id="esri_geoprocessing_BimGalleryItemMenu" hidden="true" caption="Gallery Items" contextMenu="true">
          <button refID="esri_geoprocessing_openTool"/>
        </menu>
        <!-- Bim Gallery Menu-->F

      </menus>

      <splitButtons>
        <splitButton id="esri_pnotebook_Export" keytip="EF" extendedCaption="Export" caption="Export">
          <button refID="esri_pnotebook_exportToPython" />
          <button refID="esri_pnotebook_exportToPythonFile" />
          <button refID="esri_pnotebook_exportToHTMLFile" />
        </splitButton>

        <splitButton id="esri_geoprocessing_PythonSplitButton" keytip="PF" extendedCaption="Python" caption="Python">
          <button refID="esri_pnotebook_AddOpenNewNotebook"/>
          <gallery refID ="esri_geoprocessing_PythonOptsGallery"/>
        </splitButton>

        <splitButton id="esri_geoprocessing_toolsSplitButton" hidden="true" keytip="TS" caption="Search Tools" extendedCaption="ModelBuilder">
          <button refID="esri_geoprocessing_toolsButton" />
          <!--<gallery refID="esri_geoprocessingToolSearchGallery" />-->
          <customControl refID="esri_geoprocessing_toolsSearch" />
        </splitButton>

        <splitButton id="esri_modelbuilder_toolSuggestionSplitButton" autoDisablePopup="true"  hidden="true" keytip="TS" caption="Suggest Tool" extendedCaption="ModelBuilder">
          <button refID="esri_modelbuilder_toolSuggestionButton" />
          <customControl refID="esri_modelbuilder_toolsRibbonSuggestion" />
        </splitButton>
        
        <splitButton id="esri_modelbuilder_exportSplitButton" caption="Export A Model" extendedCaption="ModelBuilder" keytip="EX">
          <button refID="esri_modelbuilder_exportFromModelButton" />
          <button refID="esri_modelbuilder_exportScriptButton" />
          <button refID="esri_modelbuilder_SentModelToPythonWindowButton" />
          <button refID="esri_modelbuilder_exportImageButton" />
        </splitButton>
        <splitButton id="esri_modelbuilder_saveSplitButton" hidden ="false" caption="Save A Model" extendedCaption="ModelBuilder" keytip="SM">
          <button refID="esri_modelbuilder_saveModelButton" />
          <button refID="esri_modelbuilder_saveButton" />
          <button refID="esri_modelbuilder_saveAsButton" />
        </splitButton>
        <splitButton id="esri_linearReferencing_toolsSplitButton" caption="Tools" extendedCaption="Open Geoprocessing pane" keytip="TS"
        largeImage="GeoprocessingToolbox32"
        smallImage="GeoprocessingToolbox16"
        helpContextID="" disableIfBusy="false">
          <tooltip heading="Geoprocessing">
            Show the Geoprocessing pane.

You can search for a specific tool, see a list of favorite and recently run tools, and explore all tools and toolboxes that are included in ArcGIS Pro.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_geoprocessing_linearReferencingtoolsViewTabButton" />
          <button refID="esri_linearReferencing_calibrateRoutesButton" />
          <button refID="esri_linearReferencing_createRoutesButton" />
          <button refID="esri_linearReferencing_dissolveRouteEventsButton" />
          <button refID="esri_linearReferencing_locateFeaturesAlongRoutesButton" />
          <button refID="esri_linearReferencing_makeRouteEventLayerButton" />
          <button refID="esri_linearReferencing_overlayRouteEventsButton" />
          <button refID="esri_linearReferencing_transformRouteEventsButton" />
        </splitButton>
      </splitButtons>
      <galleries>

        <gallery id="esri_geoprocessing_PythonOptsGallery" caption="Python options" resizable="false" loadingMessage="Loading..."
                 showGroup="false" showItemCaption="true" loadOnClick="true" showItemCaptionBelow="true" disableIfBusy="false" condition=""
                 largeImage="NewNotebook32"
                 smallImage="NewNotebook16"
                 dataTemplateFile="pack://application:,,,/ArcGIS.Desktop.GeoProcessing;component/Python/RibbonGalleryItemTemplate.xaml"
                 templateID="BasicIconHeaderTextTemplate" className="PythonOptionsGallery"
                 helpContextID="" itemsInRow="1" itemWidth="170" itemHeight="20">
          <tooltip heading="">
            Python options.<disabledText></disabledText>
          </tooltip>
        </gallery>

        <gallery id="esri_geoprocessingGallery" className="FavoritesGalleryViewModel" caption="Analysis Gallery" keytip="GG"
                 itemsInRow="5" itemWidth="76" itemHeight="62"
                 dataTemplateFile="pack://application:,,,/ArcGIS.Desktop.GeoProcessing;component/Favorites and Gallery/GalleryTemplate.xaml"
                 templateID="GalleryItemTemplate" showItemCaption="true"
                 showGroup="true"
                 showItemCaptionBelow="true"
        disableIfBusy="false"
        resizable="true"
        largeImage="GeoprocessingTool32"
        smallImage="GeoprocessingTool16"
        condition="esri_mapping_openProjectCondition"
        helpContextID="">
          <tooltip heading="">
            A gallery of geoprocessing tools.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_geoprocessing_toolsButtonFromGallery" />
          <button refID="esri_geoprocessing_CustomizeGalleryButton" />
        </gallery>

        <!-- CAD Tool Gallery-->
        <gallery id="esri_cadToolsGallery" className="ArcGIS.Desktop.Internal.GeoProcessing.CadGalleryViewModel" caption="Cad Tool Gallery" keytip="CG"
                itemsInRow="7" itemWidth="76" itemHeight="62"
                dataTemplateFile="pack://application:,,,/ArcGIS.Desktop.GeoProcessing;component/Favorites and Gallery/GalleryTemplate.xaml"
                templateID="GalleryItemTemplate" showItemCaption="true"
                showGroup="true"
                showItemCaptionBelow="true"
                disableIfBusy="false"
                resizable="true"
                largeImage="GeoprocessingTool32"
                condition="esri_mapping_singleLayerSelectedCondition"
                helpContextID="">
          <tooltip heading="">
            A gallery of CAD tools.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_geoprocessing_toolsButtonFromGallery" />
        </gallery>

        <!-- Bim Tool Gallery -->
        <gallery id="esri_bimToolsGallery" className="ArcGIS.Desktop.Internal.GeoProcessing.BimGalleryViewModel" caption="Building Tool Gallery" keytip="CG"
                itemsInRow="4" itemWidth="76" itemHeight="62"
                dataTemplateFile="pack://application:,,,/ArcGIS.Desktop.GeoProcessing;component/Favorites and Gallery/GalleryTemplate.xaml"
                templateID="GalleryItemTemplate" showItemCaption="true"
                showGroup="true"
                showItemCaptionBelow="true"
                disableIfBusy="false"
                resizable="true"
                largeImage="GeoprocessingTool32"
                condition=""
                helpContextID="">
          <tooltip heading="">
            A gallery of Bim tools.<disabledText></disabledText>
          </tooltip>
          <button refID="esri_geoprocessing_toolsButtonFromGallery" />
        </gallery>

      </galleries>
    </insertModule>
    <!-- Bim Tool Gallery -->

    <updateModule refID="esri_core_module">
      <!--<tabs>
        <updateTab refID="esri_core_analysis_NoPanes">
          <insertGroup refID="esri_geoprocessing_analysisTools" />
          <insertGroup refID="esri_geoprocessing_ToolsGallery" />
          <insertGroup refID="esri_geoprocessing_portal" />
          <insertGroup refID="esri_geoprocessing_analysis2" />
          <insertGroup refID="esri_datasourcesraster_rasterLayerTemplateGroup"/>
          <insertGroup refID="esri_geoprocessing_datainterop_group"/>
        </updateTab>

        <updateTab refID="esri_core_analysisTab">
          <insertGroup refID="esri_geoprocessing_analysisTools" />
          <insertGroup refID="esri_geoprocessing_ToolsGallery" />
          <insertGroup refID="esri_geoprocessing_portal" />
          <insertGroup refID="esri_geoprocessing_analysis2" />
          <insertGroup refID="esri_datasourcesraster_rasterLayerTemplateGroup"/>
          <insertGroup refID="esri_geoprocessing_datainterop_group"/>
        </updateTab>
      </tabs>-->
    </updateModule>

    <updateModule refID="esri_mapping">
      <tabs>
        <!--<updateTab refID="esri_mapping_analysisTab">
          <insertGroup refID="esri_geoprocessing_analysisTools" />
          <insertGroup refID="esri_geoprocessing_ToolsGallery" />
          <insertGroup refID="esri_geoprocessing_portal" />
          <insertGroup refID="esri_geoprocessing_analysis2" />
          <insertGroup refID="esri_datasourcesraster_rasterLayerTemplateGroup"/>
          <insertGroup refID="esri_geoprocessing_datainterop_group"/>
        </updateTab>-->

        <!-- Cad Tool Gallery -->
        <updateTab refID="esri_mapping_featureLayerCadTab">
          <insertGroup refID="esri_geoprocessing_cadToolsGroup"/>
        </updateTab>

        <!-- BIM Tool Gallery -->
        <updateTab refID="esri_mapping_BimContextualTab">
          <insertGroup refID="esri_geoprocessing_bimToolsGroup"/>
        </updateTab>

        <!-- Linear Referencing Tab -->
        <updateTab refID="esri_mapping_linearReferencingTab">
          <insertGroup refID="esri_geoprocessing_linearReferencingGroup"/>
        </updateTab>

        <!-- standalone table tab -->
        <updateTab refID="esri_mapping_dataLayerDataTab">
          <insertGroup refID="esri_geoprocessing_makePointsGroup" placeWith="esri_mapping_layerSelectionGroup" insert="before"/>
        </updateTab>

        <updateTab refID="esri_mapping_dataSubtypeValueTableDataTab">
          <insertGroup refID="esri_geoprocessing_makePointsGroup" placeWith="esri_mapping_layerSelectionGroup" insert="before"/>
        </updateTab>

      </tabs>

      <groups>
        <updateGroup refID="esri_mapping_selectionGroup">
          <insertButton refID="esri_geoprocessing_selectByAttributeButton" placeWith="esri_mapping_selectToolPalette" insert="after"/>
          <insertButton refID="esri_geoprocessing_selectByLocationButton" placeWith="esri_geoprocessing_selectByAttributeButton" insert="after" />
        </updateGroup>
        <updateGroup refID="esri_mapping_layerRelationshipsGroup">
          <insertButton refID="esri_geoprocessing_spatialJoinsButton" placeWith="esri_mapping_tableRelatesGallery" insert="before" />
          <insertMenu refID="esri_geoprocessing_tableJoinsMenu" placeWith="esri_mapping_tableRelatesGallery" insert="before" />
          <insertMenu refID="esri_geoprocessing_tableRelatesMenu" placeWith="esri_mapping_tableRelatesGallery" insert="before" />
        </updateGroup>
        <updateGroup refID="esri_mapping_standaloneRelationshipsGroup">
          <insertMenu refID="esri_geoprocessing_tableJoinsMenu" placeWith="esri_mapping_tableRelatesGallery" insert="before" />
          <insertMenu refID="esri_geoprocessing_tableRelatesMenu" placeWith="esri_mapping_tableRelatesGallery" insert="before" />
        </updateGroup>
        <!--<updateGroup refID="esri_mapping_layerSelectionGroup">
          <insertButton refID="esri_geoprocessing_newSelectionLayerButton" placeWith="esri_mapping_annotateSelectedButton" insert="before"/>
        </updateGroup>-->
        <updateGroup refID="esri_mapping_layerToolsGroup">
          <insertButton refID="esri_geoprocessing_exportDataButton"/>
          <insertButton refID="esri_geoprocessing_tableExportTableButton"/>
        </updateGroup>
        <updateGroup refID="esri_mapping_subtypeGroupTableToolsGroup">
          <insertButton refID="esri_geoprocessing_tableExportTableButton"/>
        </updateGroup>
        <updateGroup refID="esri_mapping_layerSymbology">
          <insertButton refID="esri_geoprocessing_importSymbologyButton" size="middle" placeWith="esri_mapping_layerDisplayFilters"/>
        </updateGroup>
        <updateGroup refID="esri_mapping_streamLayerSymbology">
          <insertButton refID="esri_geoprocessing_importSymbologyButton" size="middle"/>
        </updateGroup>

        <!--Cad Define Projection Button -->
        <updateGroup refID="esri_mapping_georeference_GeoreferenceGroup">
          <insertButton refID="esri_geoprocessing_cadDefineProjectionButton" size ="large" insert="before" placeWith="esri_mapping_georeferenceing_StartSessionButton"/>
        </updateGroup>
        <!--Define Projection 2D Button -->
        <updateGroup refID="esri_mapping_georeference_RasterGeoreferenceGroup">
          <insertButton refID="esri_geoprocessing_cadDefineProjectionButton" size ="large" insert="before" placeWith="esri_datasourcesraster_georefBimCadStartButton"/>
        </updateGroup>
        <updateGroup refID="esri_mapping_buildingLayerConversionGroup">
          <insertButton refID="esri_mapping_CreateBuildingSceneLayerPackageButton" size ="large" insert="before"/>
        </updateGroup>

      </groups>

      <menus>
        <updateMenu refID="esri_mapping_layerContextMenu">
          <insertMenu refID="esri_geoprocessing_JoinsRelatesMenu" insert="after" placeWith="esri_reports_newLayerReport" separator="true"/>
        </updateMenu>
        <updateMenu refID="esri_mapping_unregisteredLayerContextMenu">
          <insertMenu refID="esri_geoprocessing_JoinsRelatesMenu" insert="after" placeWith="esri_reports_newLayerReport" separator="true"/>
        </updateMenu>

        <updateMenu refID="esri_mapping_subtypeValueLayerContextMenu">
          <insertMenu refID="esri_geoprocessing_JoinsRelatesMenu" insert="after" placeWith="esri_reports_newLayerReport" separator="true"/>
        </updateMenu>

        <updateMenu refID="esri_mapping_subtypeValueTableContextMenu">
          <insertMenu refID="esri_geoprocessing_StandaloneJoinsRelatesMenu" insert="after" placeWith="esri_reports_newLayerReport" separator="true"/>
          <insertMenu refID="esri_geoprocessing_StandaloneCreatePointsFromTableMenu" insert="after" condition="esri_mapping_validMapMemberSelectedState" placeWith="esri_geoprocessing_StandaloneJoinsRelatesMenu" separator="true"/>
          <insertButton refID="esri_geoprocessing_tableGeocodeAddresses" insert="after" condition="esri_mapping_validMapMemberSelectedState" placeWith="esri_geoprocessing_StandaloneCreatePointsFromTableMenu" separator="false"/>
        </updateMenu>

        <updateMenu refID="esri_mapping_standaloneTableContextMenu">
          <insertMenu refID="esri_geoprocessing_StandaloneJoinsRelatesMenu" insert="after" placeWith="esri_reports_newLayerReport" separator="true"/>
          <insertMenu refID="esri_geoprocessing_StandaloneCreatePointsFromTableMenu" insert="after" condition="esri_mapping_validMapMemberSelectedState" placeWith="esri_geoprocessing_StandaloneJoinsRelatesMenu" separator="true"/>
          <insertButton refID="esri_geoprocessing_tableGeocodeAddresses" insert="after" condition="esri_mapping_validMapMemberSelectedState" placeWith="esri_geoprocessing_StandaloneCreatePointsFromTableMenu" separator="false"/>
        </updateMenu>
        <updateMenu refID="esri_mapping_unregisteredStandaloneTableContextMenu">
          <insertMenu refID="esri_geoprocessing_StandaloneJoinsRelatesMenu" insert="after" placeWith="esri_reports_newLayerReport" separator="true"/>
          <insertMenu refID="esri_geoprocessing_StandaloneCreatePointsFromTableMenu" insert="after" condition="esri_mapping_validMapMemberSelectedState" placeWith="esri_geoprocessing_StandaloneJoinsRelatesMenu" separator="true"/>
          <insertButton refID="esri_geoprocessing_tableGeocodeAddresses" insert="after" condition="esri_mapping_validMapMemberSelectedState" placeWith="esri_geoprocessing_StandaloneCreatePointsFromTableMenu" separator="false"/>
        </updateMenu>

        <updateMenu refID="esri_mapping_rasterLayerContextMenu">
          <insertMenu refID="esri_geoprocessing_JoinsRelatesMenu" insert="after" placeWith="esri_reports_newLayerReport" separator="true"/>
        </updateMenu>
      </menus>
      <palettes>
        <updateToolPalette refID="esri_mapping_rasterLayerSelectionMenu">
          <insertTool refID="esri_geoprocessing_rasterSelectByAttributeButton" placeWith="esri_mapping_selectVisibleRastersButton" insert="after" />
        </updateToolPalette>
      </palettes>
    </updateModule>

    <updateModule refID="esri_layouts">
      <!--<tabs>
        <updateTab refID="esri_layouts_analysisTab">
          <insertGroup refID="esri_geoprocessing_analysisTools" />
          <insertGroup refID="esri_geoprocessing_ToolsGallery" />
          <insertGroup refID="esri_geoprocessing_portal" />
          <insertGroup refID="esri_geoprocessing_analysis2" />
          <insertGroup refID="esri_datasourcesraster_rasterLayerTemplateGroup"/>
          <insertGroup refID="esri_geoprocessing_datainterop_group"/>
        </updateTab>
      </tabs>-->
      <groups>
        <updateGroup refID="esri_layouts_dockWindows">
          <insertButton refID="esri_geoprocessing_pythonButton" />
        </updateGroup>
      </groups>
    </updateModule>

    <!--<updateModule refID="esri_knowledgeGraph_module">
      <tabs>
        <updateTab refID="esri_knowledgeGraph_analysisTab">
          <insertGroup refID="esri_geoprocessing_analysisTools" />
          <insertGroup refID="esri_geoprocessing_ToolsGallery" />
          <insertGroup refID="esri_geoprocessing_portal" />
        </updateTab>
      </tabs>
    </updateModule>-->

    <!--<updateModule refID="esri_reports_module">
      <tabs>
        <updateTab refID="esri_reports_analysisTab">
          <insertGroup refID="esri_geoprocessing_analysisTools" />
          <insertGroup refID="esri_geoprocessing_ToolsGallery" />
          <insertGroup refID="esri_geoprocessing_portal" />
          <insertGroup refID="esri_geoprocessing_analysis2" />
          <insertGroup refID="esri_datasourcesraster_rasterLayerTemplateGroup"/>
          <insertGroup refID="esri_geoprocessing_datainterop_group"/>
        </updateTab>
      </tabs>
    </updateModule>-->

    <!--<updateModule refID="esri_geodatabase_module">
      <menus>
        <updateMenu refID="GDBProjectNewItemMenu">
          <insertButton refID="esri_geoprocessing_newToolboxButton"/>
        </updateMenu>
        <updateMenu refID="folderGeneralItemMenu">
          <insertButton refID="esri_geoprocessing_newToolboxButton"/>
        </updateMenu>
       </menus>
    </updateModule> -->
  </modules>

  <propertySheets>
    <insertSheet id="esri_geoprocessing_ToolEditor" caption="" pageHeight="400" pageWidth="800" resizable="true">
      <page id="esri_geoprocessing_ToolGeneralPage" caption="General" className="ToolGeneralViewModel">
        <content className="ToolGeneralView" />
      </page>
      <page id="esri_geoprocessing_ToolParametersPage" caption="Parameters" className="ToolParametersViewModel" >
        <content className="ToolParametersView" />
      </page>
      <page id="esri_geoprocessing_scriptToolExecutionPage" caption="Execution" className="ScriptToolExecutionViewModel" condition="esri_geoprocessing_EditingScriptToolCondition">
        <content className="ScriptToolExecutionView" />
      </page>
      <page id="esri_geoprocessing_scriptToolValidationPage" caption="Validation" className="ScriptToolValidationViewModel" condition="esri_geoprocessing_EditingScriptToolCondition">
        <content className="ScriptToolValidationView" />
      </page>
      <page id="esri_geoprocessing_ToolEnvironmentsPage" caption="Environments" className="ToolEnvironmentsViewModel" condition="esri_geoprocessing_EnviromentsToolPropsCondition">
        <content className="ToolEnvironmentsView" />
      </page>
    </insertSheet>

    <updateSheet refID="esri_core_optionsPropertySheet">
      <insertPage id="esri_geoprocessing_optionsPropertyPage" caption="Geoprocessing" className="GPOptionsViewModel" group="Application" placeWith="esri_editing_versioningPropertyPage" insert="after">
        <content className="GPOptionsView" />
      </insertPage>
    </updateSheet>

    <updateSheet refID="esri_core_optionsPropertySheet">
      <insertPage id="esri_mb_optionsPropertyPage" caption="ModelBuilder" className="ArcGIS.Desktop.Internal.GeoProcessing.MBOptionsViewModel" group="Application" placeWith="esri_geoprocessing_optionsPropertyPage" insert="after">
        <content className="MBOptionsView" />
      </insertPage>
    </updateSheet>

    <insertSheet id="esri_geoprocessing_toolboxProperties" caption="Toolbox Properties" pageHeight="400" pageWidth="800" resizable="true">
      <page id="esri_geoprocessing_toolboxPropertiesPage" caption="Toolbox Properties" className="ToolboxPropertiesViewModel">
        <content className="ToolboxPropertiesView"/>
      </page>
    </insertSheet>

    <insertSheet id="esri_geoprocessing_dataInterop_ToolPropertyEditor" caption="" pageHeight="400" pageWidth="800" resizable="true">
      <page id="esri_dataInterop_ETLToolGeneralPage" caption="General" className="ETLToolPropertyViewModel">
        <content className="ETLToolPropertyView" />
      </page>
      <page id="esri_dataInterop_ToolParametersPage" caption="Workspace" className="ETLToolWorkspacePreviewViewModel" >
        <content className="ETLToolWorkspacePreviewView" />
      </page>
    </insertSheet>
  </propertySheets>
</ArcGIS>
