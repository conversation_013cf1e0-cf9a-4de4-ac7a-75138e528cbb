# XIAOFUTools 开发指南

## 开发环境配置

### 必需软件
- **Visual Studio 2022** (17.0+)
- **ArcGIS Pro 3.5+** 
- **.NET 8.0 SDK**
- **ArcGIS Pro SDK for .NET**

### 开发环境设置
1. 安装ArcGIS Pro SDK for .NET
2. 配置Visual Studio ArcGIS Pro项目模板
3. 设置调试环境指向ArcGIS Pro

## 新工具开发流程

### 1. 创建工具模块

在`Tools`目录下创建新的工具文件夹，例如`NewTool`：

```
Tools/NewTool/
├── NewToolButton.cs
├── NewToolDockPane.cs
├── NewToolDockPaneView.xaml
├── NewToolDockPaneView.xaml.cs
└── NewToolViewModel.cs
```

### 2. 实现Button类

```csharp
using ArcGIS.Desktop.Framework.Contracts;
using XIAOFUTools.Tools.Authorization;

namespace XIAOFUTools.Tools.NewTool
{
    internal class NewToolButton : Button
    {
        protected override void OnClick()
        {
            try
            {
                // 授权检查
                if (!AuthorizationChecker.CheckAuthorizationWithPrompt("新工具"))
                {
                    return;
                }

                // 打开停靠窗格
                NewToolDockPane.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开工具时出错: {ex.Message}", "错误");
            }
        }
    }
}
```

### 3. 实现DockPane类

```csharp
using ArcGIS.Desktop.Framework.Contracts;

namespace XIAOFUTools.Tools.NewTool
{
    internal class NewToolDockPane : DockPane
    {
        private const string _dockPaneID = "XIAOFUTools_NewToolDockPane";

        protected NewToolDockPane() { }

        internal static void Show()
        {
            DockPane pane = FrameworkApplication.DockPaneManager.Find(_dockPaneID);
            pane?.Activate();
        }
    }
}
```

### 4. 创建XAML界面

```xml
<UserControl x:Class="XIAOFUTools.Tools.NewTool.NewToolDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Grid>
        <StackPanel Margin="10">
            <TextBlock Text="新工具界面" FontSize="16" FontWeight="Bold"/>
            <Button Content="执行操作" Command="{Binding ExecuteCommand}" Margin="0,10"/>
        </StackPanel>
    </Grid>
</UserControl>
```

### 5. 实现ViewModel

```csharp
using System.Windows.Input;
using XIAOFUTools.Common;

namespace XIAOFUTools.Tools.NewTool
{
    internal class NewToolViewModel : PropertyChangedBase
    {
        public ICommand ExecuteCommand { get; }

        public NewToolViewModel()
        {
            ExecuteCommand = new RelayCommand(ExecuteOperation, CanExecute);
        }

        private async void ExecuteOperation()
        {
            try
            {
                await QueuedTask.Run(() =>
                {
                    // 在这里实现GIS操作逻辑
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作失败: {ex.Message}", "错误");
            }
        }

        private bool CanExecute()
        {
            // 返回命令是否可执行
            return true;
        }
    }
}
```

### 6. 注册到Config.daml

在`Config.daml`文件中添加工具配置：

```xml
<!-- 在controls节点中添加按钮定义 -->
<button id="XIAOFUTools_NewToolButton" 
        caption="新工具" 
        className="XIAOFUTools.Tools.NewTool.NewToolButton" 
        loadOnClick="true" 
        smallImage="Images\NewTool_16.png" 
        largeImage="Images\NewTool_32.png">
  <tooltip heading="新工具">
    新工具的功能描述
    <disabledText>无权限使用新工具</disabledText>
  </tooltip>
</button>

<!-- 在dockPanes节点中添加停靠窗格定义 -->
<dockPane id="XIAOFUTools_NewToolDockPane" 
          caption="新工具" 
          className="XIAOFUTools.Tools.NewTool.NewToolDockPane" 
          dock="group" 
          dockWith="esri_core_projectDockPane">
</dockPane>

<!-- 在相应的buttonPalette中添加按钮引用 -->
<button refID="XIAOFUTools_NewToolButton" />
```

## 常用开发模式

### 异步操作模式

```csharp
private async Task ProcessDataAsync()
{
    await QueuedTask.Run(() =>
    {
        // 所有ArcGIS Pro API调用必须在QueuedTask中执行
        var map = MapView.Active.Map;
        var layers = map.GetLayersAsFlattenedList();
        
        foreach (var layer in layers)
        {
            // 处理图层
        }
    });
}
```

### 进度显示模式

```csharp
private async Task ProcessWithProgress()
{
    var progressDialog = new ProgressDialog("处理中...", "正在处理数据", 100, false);
    progressDialog.Show();

    try
    {
        for (int i = 0; i < 100; i++)
        {
            // 执行处理逻辑
            await Task.Delay(50);
            
            // 更新进度
            progressDialog.Update(i + 1, $"处理第 {i + 1} 项");
        }
    }
    finally
    {
        progressDialog.Hide();
    }
}
```

### 错误处理模式

```csharp
private async Task SafeOperation()
{
    try
    {
        await QueuedTask.Run(() =>
        {
            // GIS操作
        });
    }
    catch (Exception ex)
    {
        // 记录错误
        System.Diagnostics.Debug.WriteLine($"错误: {ex.Message}");
        
        // 用户提示
        MessageBox.Show($"操作失败: {ex.Message}", "错误", 
                       MessageBoxButton.OK, MessageBoxImage.Error);
    }
}
```

## 调试技巧

### 1. 设置调试环境
- 在项目属性中设置启动程序为ArcGIS Pro
- 配置调试参数和工作目录

### 2. 日志调试
```csharp
// 使用Debug输出
System.Diagnostics.Debug.WriteLine("调试信息");

// 使用Console输出（在输出窗口可见）
Console.WriteLine("控制台信息");
```

### 3. 断点调试
- 在关键代码行设置断点
- 使用条件断点过滤特定情况
- 利用调用堆栈跟踪执行流程

## 性能优化建议

### 1. 内存管理
```csharp
// 正确的资源释放
using (var geodatabase = new Geodatabase(connectionPath))
{
    // 使用geodatabase
} // 自动释放资源
```

### 2. 批量操作
```csharp
// 使用编辑操作批量处理
var editOperation = new EditOperation();
editOperation.Name = "批量更新";

foreach (var feature in features)
{
    editOperation.Modify(feature, attributes);
}

await editOperation.ExecuteAsync();
```

### 3. 缓存优化
```csharp
// 缓存常用对象
private static readonly Dictionary<string, SpatialReference> _spatialRefCache 
    = new Dictionary<string, SpatialReference>();
```

## 测试策略

### 单元测试
```csharp
[TestMethod]
public void TestCoordinateTransform()
{
    // 准备测试数据
    var point = MapPointBuilder.CreateMapPoint(120.0, 30.0);
    
    // 执行测试
    var result = CoordinateTransformHelper.WGS84ToGCJ02(point);
    
    // 验证结果
    Assert.IsNotNull(result);
    Assert.AreNotEqual(point.X, result.X);
}
```

### 集成测试
- 测试与ArcGIS Pro的集成
- 验证工具在真实环境中的表现
- 测试用户界面交互

## 发布流程

### 1. 版本管理
- 更新版本号
- 编写变更日志
- 标记Git版本标签

### 2. 构建发布
```bash
# 清理项目
dotnet clean

# 构建Release版本
dotnet build -c Release

# 生成Add-in包
# ArcGIS Pro会自动生成.esriAddinX文件
```

### 3. 质量检查
- 代码审查
- 功能测试
- 性能测试
- 兼容性测试

## 常见问题解决

### 1. 编译错误
- 检查ArcGIS Pro SDK版本兼容性
- 确认所有依赖包已正确安装
- 清理并重新构建项目

### 2. 运行时错误
- 检查授权状态
- 验证ArcGIS Pro版本
- 查看输出窗口的错误信息

### 3. 界面问题
- 检查XAML语法
- 验证数据绑定路径
- 确认ViewModel属性通知

## 代码规范

### 命名约定
- 类名使用PascalCase
- 方法名使用PascalCase
- 变量名使用camelCase
- 常量使用UPPER_CASE

### 注释规范
```csharp
/// <summary>
/// 计算两点之间的距离
/// </summary>
/// <param name="point1">起始点</param>
/// <param name="point2">结束点</param>
/// <returns>距离值（米）</returns>
public double CalculateDistance(MapPoint point1, MapPoint point2)
{
    // 实现逻辑
}
```

### 文件组织
- 每个类一个文件
- 相关类放在同一命名空间
- 使用有意义的文件夹结构
