<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 数据处理功能青蓝色渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>

    <!-- 文件渐变 -->
    <linearGradient id="fileGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:0.9" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="fileShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="1" stdDeviation="1.5" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="32" height="32" rx="8" fill="url(#bgGradient)"/>

  <!-- TXT文件 -->
  <g transform="translate(6,8)">
    <!-- 文件主体 -->
    <rect x="0" y="0" width="8" height="12" rx="1" fill="url(#fileGradient)" filter="url(#fileShadow)"/>
    
    <!-- 文件折角 -->
    <path d="M6 0 L8 0 L8 2 Z" fill="#4facfe" opacity="0.3"/>
    <path d="M6 0 L6 2 L8 2" fill="none" stroke="#4facfe" stroke-width="0.5" opacity="0.5"/>
    
    <!-- TXT标识 -->
    <rect x="1" y="13" width="6" height="2" rx="1" fill="#e17055"/>
    <text x="4" y="14.8" fill="#ffffff" font-family="SF Pro, Arial, sans-serif" font-size="4" font-weight="bold" text-anchor="middle">TXT</text>
    
    <!-- 文本内容行 -->
    <line x1="1" y1="3" x2="6" y2="3" stroke="#4facfe" stroke-width="0.8" opacity="0.6"/>
    <line x1="1" y1="5" x2="7" y2="5" stroke="#4facfe" stroke-width="0.8" opacity="0.6"/>
    <line x1="1" y1="7" x2="5" y2="7" stroke="#4facfe" stroke-width="0.8" opacity="0.6"/>
    <line x1="1" y1="9" x2="6" y2="9" stroke="#4facfe" stroke-width="0.8" opacity="0.6"/>
    
    <!-- 坐标数据标识 -->
    <text x="4" y="4" fill="#4facfe" font-family="SF Pro, Arial, sans-serif" font-size="2.5" font-weight="bold" text-anchor="middle" opacity="0.8">X,Y</text>
  </g>

  <!-- 转换箭头 -->
  <path d="M15 16 L19 16 M17 14 L19 16 L17 18" stroke="#ffffff" stroke-width="2.5" stroke-linecap="round"/>

  <!-- SHP要素 -->
  <g transform="translate(22,12)">
    <!-- 多边形要素 -->
    <path d="M0 2 L3 0 L6 3 L4 8 L1 6 Z" fill="url(#fileGradient)" filter="url(#fileShadow)"/>
    
    <!-- 要素边框 -->
    <path d="M0 2 L3 0 L6 3 L4 8 L1 6 Z" fill="none" stroke="#4facfe" stroke-width="1" opacity="0.7"/>
    
    <!-- 节点标识 -->
    <circle cx="0" cy="2" r="0.8" fill="#00f2fe"/>
    <circle cx="3" cy="0" r="0.8" fill="#00f2fe"/>
    <circle cx="6" cy="3" r="0.8" fill="#00f2fe"/>
    <circle cx="4" cy="8" r="0.8" fill="#00f2fe"/>
    <circle cx="1" cy="6" r="0.8" fill="#00f2fe"/>
    
    <!-- SHP标识 -->
    <rect x="0" y="10" width="6" height="2" rx="1" fill="#00b894"/>
    <text x="3" y="11.8" fill="#ffffff" font-family="SF Pro, Arial, sans-serif" font-size="4" font-weight="bold" text-anchor="middle">SHP</text>
  </g>

  <!-- 数据流指示 -->
  <g transform="translate(16,6)">
    <circle cx="-4" cy="0" r="1" fill="#ffffff" opacity="0.6"/>
    <circle cx="0" cy="0" r="1" fill="#ffffff" opacity="0.7"/>
    <circle cx="4" cy="0" r="1" fill="#ffffff" opacity="0.8"/>
  </g>

  <!-- 功能标识 -->
  <text x="16" y="28" fill="#ffffff" font-family="SF Pro, Arial, sans-serif" font-size="4" font-weight="bold" text-anchor="middle" opacity="0.8">TXT→SHP</text>
</svg>
