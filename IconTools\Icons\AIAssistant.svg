<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 简洁背景渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="32" height="32" rx="8" fill="url(#bgGradient)"/>

  <!-- 主对话气泡 -->
  <ellipse cx="16" cy="14" rx="12" ry="8" fill="#ffffff" opacity="0.95"/>

  <!-- 气泡尾巴 -->
  <path d="M8 20 L12 24 L16 20 Z" fill="#ffffff" opacity="0.95"/>

  <!-- AI文字 -->
  <text x="16" y="18" fill="#667eea" font-family="SF Pro, Arial, sans-serif" font-size="10" font-weight="bold" text-anchor="middle">AI</text>

  <!-- 装饰点 -->
  <circle cx="10" cy="10" r="1.5" fill="#667eea" opacity="0.6"/>
  <circle cx="16" cy="8" r="1.5" fill="#667eea" opacity="0.6"/>
  <circle cx="22" cy="10" r="1.5" fill="#667eea" opacity="0.6"/>
</svg>
