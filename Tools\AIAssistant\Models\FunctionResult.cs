using System;
using Newtonsoft.Json;

namespace XIAOFUTools.Tools.AIAssistant.Models
{
    /// <summary>
    /// 函数执行结果模型
    /// </summary>
    public class FunctionResult
    {
        /// <summary>
        /// 调用ID（与FunctionCall的Id对应）
        /// </summary>
        [JsonProperty("tool_call_id")]
        public string ToolCallId { get; set; }

        /// <summary>
        /// 调用ID的别名，方便访问
        /// </summary>
        [JsonIgnore]
        public string Id
        {
            get => ToolCallId;
            set => ToolCallId = value;
        }

        /// <summary>
        /// 角色（固定为"tool"）
        /// </summary>
        [JsonProperty("role")]
        public string Role { get; set; } = "tool";

        /// <summary>
        /// 函数名称
        /// </summary>
        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string Name { get; set; }

        /// <summary>
        /// 执行结果内容
        /// </summary>
        [JsonProperty("content")]
        public string Content { get; set; }

        /// <summary>
        /// 是否执行成功
        /// </summary>
        [JsonIgnore]
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息（如果执行失败）
        /// </summary>
        [JsonIgnore]
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 执行时间（毫秒）
        /// </summary>
        [JsonIgnore]
        public long ExecutionTimeMs { get; set; }

        /// <summary>
        /// 执行时间戳
        /// </summary>
        [JsonIgnore]
        public DateTime Timestamp { get; set; }

        public FunctionResult()
        {
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 创建成功的函数结果
        /// </summary>
        /// <param name="toolCallId">调用ID</param>
        /// <param name="functionName">函数名称</param>
        /// <param name="content">结果内容</param>
        /// <param name="executionTimeMs">执行时间</param>
        /// <returns>函数结果</returns>
        public static FunctionResult CreateSuccess(string toolCallId, string functionName, string content, long executionTimeMs = 0)
        {
            return new FunctionResult
            {
                ToolCallId = toolCallId,
                Name = functionName,
                Content = content,
                Success = true,
                ExecutionTimeMs = executionTimeMs
            };
        }

        /// <summary>
        /// 创建失败的函数结果
        /// </summary>
        /// <param name="toolCallId">调用ID</param>
        /// <param name="functionName">函数名称</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="executionTimeMs">执行时间</param>
        /// <returns>函数结果</returns>
        public static FunctionResult CreateFailure(string toolCallId, string functionName, string errorMessage, long executionTimeMs = 0)
        {
            return new FunctionResult
            {
                ToolCallId = toolCallId,
                Name = functionName,
                Content = $"执行失败: {errorMessage}",
                Success = false,
                ErrorMessage = errorMessage,
                ExecutionTimeMs = executionTimeMs
            };
        }

        /// <summary>
        /// 创建带结构化数据的成功结果
        /// </summary>
        /// <param name="toolCallId">调用ID</param>
        /// <param name="functionName">函数名称</param>
        /// <param name="data">结构化数据</param>
        /// <param name="message">描述信息</param>
        /// <param name="executionTimeMs">执行时间</param>
        /// <returns>函数结果</returns>
        public static FunctionResult CreateSuccessWithData(string toolCallId, string functionName, object data, string message = null, long executionTimeMs = 0)
        {
            var result = new
            {
                success = true,
                message = message ?? "执行成功",
                data = data,
                execution_time_ms = executionTimeMs
            };

            return new FunctionResult
            {
                ToolCallId = toolCallId,
                Name = functionName,
                Content = JsonConvert.SerializeObject(result, Formatting.Indented),
                Success = true,
                ExecutionTimeMs = executionTimeMs
            };
        }

        /// <summary>
        /// 获取结果的字符串表示
        /// </summary>
        /// <returns>格式化的结果字符串</returns>
        public override string ToString()
        {
            var status = Success ? "成功" : "失败";
            var time = ExecutionTimeMs > 0 ? $" ({ExecutionTimeMs}ms)" : "";
            return $"FunctionResult: {Name} - {status}{time}";
        }
    }
}
