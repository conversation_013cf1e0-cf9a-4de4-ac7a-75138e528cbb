/* 简化的代码高亮样式 */
.hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
    background: #f8f8f8;
    color: #333;
}

.hljs-keyword {
    color: #0066cc;
    font-weight: bold;
}

.hljs-string {
    color: #009900;
}

.hljs-comment {
    color: #999999;
    font-style: italic;
}

.hljs-number {
    color: #cc6600;
}

.hljs-function {
    color: #cc0066;
}

.hljs-variable {
    color: #663399;
}

.hljs-operator {
    color: #666666;
}

.hljs-punctuation {
    color: #666666;
}
