<Window x:Class="XIAOFUTools.Tools.About.AboutDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="关于 XIAOFU工具箱" 
        Height="500" Width="450"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterScreen"
        ShowInTaskbar="False"
        WindowStyle="SingleBorderWindow">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 标题样式 -->
            <Style x:Key="TitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="20"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="#2c3e50"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="Margin" Value="0,10"/>
            </Style>
            
            <!-- 版本信息样式 -->
            <Style x:Key="VersionTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="#3498db"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="Margin" Value="0,5"/>
            </Style>
            
            <!-- 信息标签样式 -->
            <Style x:Key="InfoLabelStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="#34495e"/>
                <Setter Property="Margin" Value="0,6,0,2"/>
            </Style>
            
            <!-- 信息内容样式 -->
            <Style x:Key="InfoContentStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="11"/>
                <Setter Property="Foreground" Value="#7f8c8d"/>
                <Setter Property="TextWrapping" Value="Wrap"/>
                <Setter Property="LineHeight" Value="16"/>
            </Style>
            
            <!-- 警告文本样式 -->
            <Style x:Key="WarningTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="11"/>
                <Setter Property="Foreground" Value="#e74c3c"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="TextWrapping" Value="Wrap"/>
                <Setter Property="LineHeight" Value="16"/>
                <Setter Property="Margin" Value="0,5"/>
            </Style>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 头部区域 -->
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 图标 -->
            <Image Grid.Column="0"
                   Source="pack://application:,,,/XIAOFUTools;component/Images/Toolbox_32.png"
                   Width="32" Height="32"
                   VerticalAlignment="Center"
                   Margin="0,0,12,0"/>

            <!-- 标题和版本信息 -->
            <StackPanel Grid.Column="1" Orientation="Vertical" VerticalAlignment="Center">
                <TextBlock Text="XIAOFU工具箱"
                          FontSize="16" FontWeight="Bold"
                          Foreground="#2c3e50"
                          Margin="0,0,0,2"/>
                <TextBlock x:Name="VersionText"
                          Text="版本 1.0.0"
                          FontSize="12" FontWeight="SemiBold"
                          Foreground="#3498db"/>
            </StackPanel>
        </Grid>

        <!-- 主要内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,12">
            <StackPanel>
                <!-- 当前版本号 -->
                <TextBlock Text="当前版本号:" Style="{StaticResource InfoLabelStyle}"/>
                <TextBlock x:Name="CurrentVersionText" Text="1.0.1" Style="{StaticResource InfoContentStyle}"/>

                <!-- 开发环境 -->
                <TextBlock Text="开发环境:" Style="{StaticResource InfoLabelStyle}"/>
                <TextBlock x:Name="DevelopmentEnvironmentText"
                          Text="ArcGIS Pro 3.5+ / .NET 8.0 / Visual Studio 2022"
                          Style="{StaticResource InfoContentStyle}"/>

                <!-- 版本更新信息 -->
                <TextBlock Text="版本更新信息:" Style="{StaticResource InfoLabelStyle}"/>
                <TextBlock x:Name="UpdateInfoText"
                          Text="增加动态工具-面积分割"
                          Style="{StaticResource InfoContentStyle}"/>

                <!-- 作者信息 -->
                <TextBlock Text="作者信息:" Style="{StaticResource InfoLabelStyle}"/>
                <TextBlock x:Name="AuthorInfoText"
                          Text="作者: XIAOFU&#x0a;哔哩哔哩: XIAOFUGIS&#x0a;QQ群: 967758553&#x0a;联系方式: WX（fu76488）/ QQ（1922759464）"
                          Style="{StaticResource InfoContentStyle}"/>

                <!-- 特别提醒 -->
                <TextBlock Text="特别提醒:" Style="{StaticResource InfoLabelStyle}"/>
                <TextBlock x:Name="SpecialNoticeText" 
                          Text="自制有点奇奇怪怪问题很正常。使用前自己研究先，哪个功能不会用联系作者，数据无价记得备份喔!!!" 
                          Style="{StaticResource WarningTextStyle}"/>
            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,12,0,0">
            <Button Content="确定" 
                    Width="80" Height="30" 
                    IsDefault="True"
                    Click="OkButton_Click"
                    Style="{StaticResource ButtonStyle}"/>
        </StackPanel>
    </Grid>
</Window>
