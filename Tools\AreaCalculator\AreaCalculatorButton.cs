using System;
using ArcGIS.Desktop.Framework.Contracts;
using XIAOFUTools.Tools.Authorization;

namespace XIAOFUTools.Tools.AreaCalculator
{
    /// <summary>
    /// 计算面积按钮
    /// </summary>
    internal class AreaCalculatorButton : <PERSON><PERSON>
    {
        protected override void OnClick()
        {
            try
            {
                // 检查授权
                if (!AuthorizationChecker.CheckAuthorizationWithPrompt("计算面积工具"))
                {
                    return;
                }

                // 打开计算面积停靠窗格
                AreaCalculatorDockPane.Show();
            }
            catch (Exception ex)
            {
                ArcGIS.Desktop.Framework.Dialogs.MessageBox.Show($"打开停靠窗格时出错: {ex.Message}", "错误");
            }
        }
    }
}
