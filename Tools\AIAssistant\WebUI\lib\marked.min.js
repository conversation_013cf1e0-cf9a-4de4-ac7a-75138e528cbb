// 简化的Markdown解析器（用于演示）
// 在实际部署时，请使用完整的marked.js库
(function() {
    'use strict';
    
    window.marked = {
        parse: function(markdown) {
            if (!markdown) return '';
            
            let html = markdown;
            
            // 标题
            html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
            html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
            html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
            
            // 粗体
            html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            
            // 斜体
            html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
            
            // 代码块
            html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, function(match, lang, code) {
                return '<pre><code class="language-' + (lang || '') + '">' + 
                       code.replace(/</g, '&lt;').replace(/>/g, '&gt;') + 
                       '</code></pre>';
            });
            
            // 行内代码
            html = html.replace(/`(.*?)`/g, '<code>$1</code>');
            
            // 列表
            html = html.replace(/^\* (.*$)/gim, '<li>$1</li>');
            html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
            
            // 链接
            html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
            
            // 换行
            html = html.replace(/\n/g, '<br>');
            
            return html;
        },
        
        setOptions: function(options) {
            // 简化实现，忽略选项
        }
    };
})();
