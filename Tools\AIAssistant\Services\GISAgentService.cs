using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArcGIS.Core.CIM;
using ArcGIS.Desktop.Framework.Threading.Tasks;
using ArcGIS.Desktop.Mapping;
using XIAOFUTools.Tools.AIAssistant.Models;

namespace XIAOFUTools.Tools.AIAssistant.Services
{
    /// <summary>
    /// GIS Agent服务 - 专门处理GIS相关任务
    /// </summary>
    public class GISAgentService
    {
        /// <summary>
        /// 获取当前GIS上下文信息
        /// </summary>
        /// <returns>GIS上下文</returns>
        public async Task<GISContext> GetCurrentGISContextAsync()
        {
            return await QueuedTask.Run(() =>
            {
                var context = new GISContext();

                try
                {
                    var mapView = MapView.Active;
                    if (mapView?.Map != null)
                    {
                        var map = mapView.Map;

                        // 基本地图信息
                        context.MapName = map.Name ?? "未命名地图";

                        // 获取比例尺（安全方式）
                        try
                        {
                            context.Scale = mapView.Camera?.Scale ?? 0;
                        }
                        catch
                        {
                            context.Scale = 0;
                        }

                        // 地图范围（安全方式）
                        try
                        {
                            var extent = mapView.Extent;
                            if (extent != null)
                            {
                                context.CurrentExtent = new MapExtent
                                {
                                    XMin = extent.XMin,
                                    YMin = extent.YMin,
                                    XMax = extent.XMax,
                                    YMax = extent.YMax
                                };
                            }
                        }
                        catch
                        {
                            context.CurrentExtent = null;
                        }

                        // 坐标系
                        try
                        {
                            context.SpatialReference = map.SpatialReference?.Name ?? "未知坐标系";
                        }
                        catch
                        {
                            context.SpatialReference = "未知坐标系";
                        }

                        // 活动图层信息
                        context.ActiveLayers = GetLayerInfoList(map);

                        // 选中要素数量
                        context.SelectedFeatureCount = GetSelectedFeatureCount(map);
                    }
                    else
                    {
                        // 没有活动地图时的默认值
                        context.MapName = "无活动地图";
                        context.SpatialReference = "未知坐标系";
                        context.Scale = 0;
                        context.SelectedFeatureCount = 0;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"获取GIS上下文失败: {ex.Message}");
                }

                return context;
            });
        }

        /// <summary>
        /// 使用GIS上下文增强用户提示
        /// </summary>
        /// <param name="userMessage">用户消息</param>
        /// <param name="gisContext">GIS上下文</param>
        /// <returns>增强后的提示</returns>
        public string EnhancePromptWithGISContext(string userMessage, GISContext gisContext)
        {
            var contextInfo = new List<string>();
            
            if (gisContext != null)
            {
                // 地图基本信息
                if (!string.IsNullOrEmpty(gisContext.MapName))
                {
                    contextInfo.Add($"当前地图: {gisContext.MapName}");
                }
                
                if (gisContext.Scale > 0)
                {
                    contextInfo.Add($"当前比例尺: 1:{gisContext.Scale:N0}");
                }
                
                if (!string.IsNullOrEmpty(gisContext.SpatialReference))
                {
                    contextInfo.Add($"坐标系: {gisContext.SpatialReference}");
                }
                
                // 图层信息
                if (gisContext.ActiveLayers?.Count > 0)
                {
                    var layerNames = gisContext.ActiveLayers.Take(5).Select(l => l.Name);
                    contextInfo.Add($"活动图层: {string.Join(", ", layerNames)}");
                    
                    if (gisContext.ActiveLayers.Count > 5)
                    {
                        contextInfo.Add($"(还有{gisContext.ActiveLayers.Count - 5}个图层)");
                    }
                }
                
                // 选中要素
                if (gisContext.SelectedFeatureCount > 0)
                {
                    contextInfo.Add($"已选中{gisContext.SelectedFeatureCount}个要素");
                }
            }
            
            // 构建增强提示
            if (contextInfo.Count > 0)
            {
                string context = string.Join("; ", contextInfo);
                return $"[当前GIS环境: {context}]\n\n用户问题: {userMessage}";
            }
            
            return userMessage;
        }



        /// <summary>
        /// 获取图层信息列表
        /// </summary>
        private List<LayerInfo> GetLayerInfoList(Map map)
        {
            var layerInfos = new List<LayerInfo>();

            try
            {
                if (map == null) return layerInfos;

                var layers = map.GetLayersAsFlattenedList();
                if (layers == null) return layerInfos;

                foreach (var layer in layers.Take(10)) // 限制数量避免信息过多
                {
                    if (layer == null) continue;

                    var layerInfo = new LayerInfo
                    {
                        Name = layer.Name ?? "未命名图层",
                        LayerType = layer.GetType()?.Name ?? "未知类型",
                        IsVisible = layer.IsVisible
                    };
                    
                    // 如果是要素图层，获取更多信息
                    if (layer is FeatureLayer featureLayer)
                    {
                        try
                        {
                            var featureClass = featureLayer.GetFeatureClass();
                            if (featureClass != null)
                            {
                                var definition = featureClass.GetDefinition();
                                if (definition != null)
                                {
                                    layerInfo.GeometryType = definition.GetShapeType().ToString();
                                    layerInfo.FeatureCount = (int)featureClass.GetCount();
                                    layerInfo.IsEditable = featureLayer.IsEditable;

                                    // 获取字段信息（前几个字段）
                                    var fields = definition.GetFields();
                                    if (fields != null && fields.Count > 0)
                                    {
                                        layerInfo.Fields = fields.Take(5).Select(f => f.Name).ToList();
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // 记录错误但不影响其他图层的处理
                            System.Diagnostics.Debug.WriteLine($"获取图层 {layer.Name} 详细信息失败: {ex.Message}");
                        }
                    }
                    
                    layerInfos.Add(layerInfo);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取图层信息失败: {ex.Message}");
            }
            
            return layerInfos;
        }

        /// <summary>
        /// 获取选中要素数量
        /// </summary>
        private int GetSelectedFeatureCount(Map map)
        {
            try
            {
                if (map == null) return 0;

                var selection = map.GetSelection();
                if (selection == null) return 0;

                // 使用SelectionSet的Count属性直接获取总数
                return selection.Count;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取选中要素数量失败: {ex.Message}");
                return 0;
            }
        }


    }
}
