# XIAOFUTools 工具界面和实现规范

## 界面风格统一规范

### 1. 工具窗口布局

每个工具窗口应包含以下基本组件：
- 参数设置区域：包含工具所需的各种参数输入控件
- 日志和进度区域：
  - 进度条放在日志文本框上方紧邻，不需要标签文字
  - 日志窗口用Border包裹，占满整个宽度
  - 进度条和日志窗口组合在一起，成为一个整体
- 状态信息区域：显示当前执行状态的简短文本
- 按钮区域：底部显示帮助按钮（左侧）、"取消"和"执行/运行"按钮（右侧）

### 2. 样式应用

- 所有控件应使用Styles/ControlStyles.xaml中定义的样式
- 按钮样式：
  - 执行按钮使用ExecuteButtonStyle
  - 取消按钮使用CancelButtonStyle
  - 帮助按钮使用HelpButtonStyle
  - 一般按钮使用DefaultButtonStyle
- 文本框使用TextBoxStyle
- 下拉列表使用ComboBoxStyle
- 复选框使用CheckBoxStyle
- 日志文本框使用LogTextBoxStyle
- 进度条使用ProgressBarStyle，高度设为6px，不需要显示进度文字标签

### 3. 边距和间距

- 窗口外边距(Margin)统一为12
- 控件间垂直间距统一为10
- 控件标签与控件之间的水平间距统一为10

### 4. 日志和进度区域布局

- 进度条：
  - 使用ProgressBarStyle样式
  - 高度设为6px
  - 不显示"进度:"标签
  - 占满整个宽度(Grid.ColumnSpan=2)
  - 与日志窗口之间间距设为2px
- 日志窗口：
  - 使用Border包裹，BorderBrush="#CDCDCD", BorderThickness="1"
  - TextBox使用LogTextBoxStyle
  - 不显示"操作日志:"标签
  - 占满整个宽度(Grid.ColumnSpan=2)
  - BorderThickness="0"(因已被Border包裹)

### 5. 窗口尺寸

- 宽度：450像素（可根据内容适当调整）
- 高度：无日志窗口时350像素，有日志窗口时550像素

## 功能实现规范

### 1. ViewModel规范

所有工具ViewModel应包含以下基本功能：
- ShowHelpCommand：显示帮助信息的命令
- CancelCommand：关闭窗口的命令
- Execute方法：执行工具主要功能
- CanExecute方法：判断工具是否可以执行
- ShowHelp方法：显示帮助内容

### 2. 视图规范

- 视图文件(.xaml)应使用Border作为根容器，设置圆角和边框
- 使用Grid作为主要布局控件
- 标题区域使用FontSize为16的TextBlock
- 使用统一的工具按钮图标和样式

### 3. 帮助内容规范

帮助内容应包含以下几个部分：
- 功能描述：简要说明工具的用途
- 参数说明：列出各个参数的含义和用法
- 操作步骤：列出使用工具的基本步骤
- 注意事项：提醒用户需要注意的问题

### 4. 日志记录规范

如果工具包含日志功能，应实现以下方法：
- LogInfo：记录一般信息
- LogError：记录错误信息
- LogWarning：记录警告信息
- ClearLog：清除日志内容

## 示例工具参考

### 完整实现示例
以下工具完全遵循了本规范，可作为新工具开发的标准参考：
1. **地块中文编号工具** (ChineseNumbering) - 简单参数工具
2. **批量裁剪要素图层工具** (BatchLayerClip) - 复杂参数工具
3. **批量添加数据工具** (BatchAddData) - 列表展示工具
4. **特殊坐标转换工具** (SpecialCoordinateTransform) - 文件处理工具

### 开发检查清单
在开发新工具时，请确保：
- [ ] 使用了统一的样式资源
- [ ] 实现了标准的命令模式
- [ ] 添加了适当的错误处理
- [ ] 提供了用户帮助信息
- [ ] 通过了界面一致性检查
- [ ] 测试了键盘导航功能
- [ ] 验证了无障碍访问性

## 质量保证

### 1. 界面测试
- 在不同分辨率下测试界面显示
- 验证所有控件的交互功能
- 检查文本显示的完整性

### 2. 用户体验测试
- 新用户首次使用测试
- 常用操作流程测试
- 错误场景处理测试

### 3. 兼容性测试
- 不同Windows版本兼容性
- 不同ArcGIS Pro版本兼容性
- 高DPI显示适配测试