<UserControl x:Class="XIAOFUTools.Tools.ExportLayout.ExportLayoutDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="450">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Margin="6">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 输出设置 -->
            <GroupBox Grid.Row="0" Header="输出设置" Style="{StaticResource GroupBoxStyle}">
                <StackPanel>
                    <!-- 输出文件夹 -->
                    <Grid Margin="0,0,0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="45"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="输出文件夹:" VerticalAlignment="Center"/>
                        <TextBox Grid.Column="1" Text="{Binding OutputFolder}"
                                 Style="{StaticResource TextBoxStyle}" Height="20" Margin="0,0,3,0"/>
                        <Button Grid.Column="2" Content="浏览" Height="20" Margin="0" Width="45"
                                Command="{Binding BrowseFolderCommand}"
                                Style="{StaticResource DefaultButtonStyle}"/>
                    </Grid>

                    <!-- 分辨率 -->
                    <Grid Margin="0,0,0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="分辨率 (DPI):" VerticalAlignment="Center"/>
                        <TextBox Grid.Column="1" Text="{Binding Resolution}"
                                 Style="{StaticResource TextBoxStyle}" Height="20"/>
                    </Grid>

                    <!-- 导出格式 -->
                    <Grid Margin="0,0,0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="导出格式:" VerticalAlignment="Center"/>
                        <ComboBox Grid.Column="1" SelectedItem="{Binding SelectedFormat}"
                                  ItemsSource="{Binding ExportFormats}"
                                  Style="{StaticResource ComboBoxStyle}" Height="20"/>
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!-- 布局列表 -->
            <GroupBox Grid.Row="1" Header="布局列表" Style="{StaticResource GroupBoxStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 全选/反选按钮 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,3">
                        <Button Content="全选" Command="{Binding SelectAllCommand}"
                                Style="{StaticResource DefaultButtonStyle}" Width="60" Height="20" Margin="0,0,4,0"/>
                        <Button Content="反选" Command="{Binding InvertSelectionCommand}"
                                Style="{StaticResource DefaultButtonStyle}" Width="60" Height="20" Margin="0,0,4,0"/>
                        <Button Content="⟲" Width="20" Height="20" Margin="0,0,0,0"
                                Style="{StaticResource DefaultButtonStyle}"
                                Command="{Binding RefreshLayoutsCommand}"
                                ToolTip="刷新布局列表"
                                FontSize="12" FontWeight="Bold"/>
                    </StackPanel>

                    <!-- 布局列表 -->
                    <Border Grid.Row="1" BorderBrush="#CDCDCD" BorderThickness="1">
                        <ListBox ItemsSource="{Binding Layouts}" Background="White">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <CheckBox Content="{Binding Name}"
                                              IsChecked="{Binding IsSelected}"
                                              Style="{StaticResource CheckBoxStyle}"/>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </Border>
                </Grid>
            </GroupBox>
        </Grid>

        <!-- 底部按钮区域 -->
        <Grid Grid.Row="1" Margin="0,0,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 帮助按钮 -->
            <Button Grid.Column="0" Content="?"
                    Style="{StaticResource HelpButtonStyle}"
                    Command="{Binding ShowHelpCommand}"
                    ToolTip="显示帮助信息"/>

            <!-- 空白区域 -->
            <Grid Grid.Column="1"/>

            <!-- 停止按钮 -->
            <Button Grid.Column="2" Content="停止" Command="{Binding StopCommand}"
                    Style="{StaticResource CancelButtonStyle}"
                    IsEnabled="{Binding IsRunning}"
                    Background="#FFE74C3C" Foreground="White" Margin="0,0,4,0"/>

            <!-- 开始按钮 -->
            <Button Grid.Column="3" Content="开始" Command="{Binding StartCommand}"
                    Style="{StaticResource ExecuteButtonStyle}"
                    Width="60"/>
        </Grid>
    </Grid>
</UserControl>
