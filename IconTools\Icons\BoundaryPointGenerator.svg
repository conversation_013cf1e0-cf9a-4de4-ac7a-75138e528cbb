<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 统一蓝色渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#74b9ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0984e3;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="32" height="32" rx="8" fill="url(#bgGradient)"/>

  <!-- 中心多边形 -->
  <path d="M6 8 L26 6 L28 16 L24 26 L8 28 L4 18 Z" fill="#ffffff" opacity="0.9"/>

  <!-- 边界点 -->
  <circle cx="26" cy="11" r="2.5" fill="#e17055"/>
  <circle cx="6" cy="21" r="2.5" fill="#e17055"/>
  <circle cx="16" cy="5" r="2.5" fill="#e17055"/>
  <circle cx="16" cy="27" r="2.5" fill="#e17055"/>

  <!-- 连接线 -->
  <line x1="24" y1="8" x2="26" y2="11" stroke="#ffffff" stroke-width="2" stroke-dasharray="2,2" opacity="0.7"/>
  <line x1="8" y1="24" x2="6" y2="21" stroke="#ffffff" stroke-width="2" stroke-dasharray="2,2" opacity="0.7"/>
  <line x1="16" y1="7" x2="16" y2="5" stroke="#ffffff" stroke-width="2" stroke-dasharray="2,2" opacity="0.7"/>
  <line x1="16" y1="25" x2="16" y2="27" stroke="#ffffff" stroke-width="2" stroke-dasharray="2,2" opacity="0.7"/>

  <!-- 方向标识 -->
  <text x="26" y="13" fill="#ffffff" font-family="SF Pro, Arial, sans-serif" font-size="3" font-weight="bold" text-anchor="middle">E</text>
  <text x="6" y="23" fill="#ffffff" font-family="SF Pro, Arial, sans-serif" font-size="3" font-weight="bold" text-anchor="middle">W</text>
  <text x="16" y="7" fill="#ffffff" font-family="SF Pro, Arial, sans-serif" font-size="3" font-weight="bold" text-anchor="middle">N</text>
  <text x="16" y="29" fill="#ffffff" font-family="SF Pro, Arial, sans-serif" font-size="3" font-weight="bold" text-anchor="middle">S</text>
</svg>