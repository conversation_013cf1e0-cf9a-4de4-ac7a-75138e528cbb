using System;
using System.Threading.Tasks;
using System.Windows.Threading;
using Microsoft.Web.WebView2.Wpf;
using Newtonsoft.Json;
using XIAOFUTools.Tools.AIAssistant.Models;

namespace XIAOFUTools.Tools.AIAssistant.Services
{
    /// <summary>
    /// WebView2与C#后端的通信桥梁
    /// </summary>
    public class WebViewBridge
    {
        private readonly WebView2 _webView;
        private readonly AIAssistantDockPaneViewModel _viewModel;

        public WebViewBridge(WebView2 webView, AIAssistantDockPaneViewModel viewModel)
        {
            _webView = webView ?? throw new ArgumentNullException(nameof(webView));
            _viewModel = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
        }

        /// <summary>
        /// 处理来自JavaScript的消息
        /// </summary>
        /// <param name="message">JSON格式的消息</param>
        public async void HandleWebMessage(string message)
        {
            try
            {
                var messageObj = JsonConvert.DeserializeObject<WebMessage>(message);
                
                switch (messageObj.Type)
                {
                    case "sendMessage":
                        await HandleSendMessage(messageObj.Data);
                        break;
                    case "clearConversation":
                        HandleClearConversation();
                        break;
                    case "getConversationHistory":
                        await HandleGetConversationHistory();
                        break;
                    default:
                        Console.WriteLine($"未知消息类型: {messageObj.Type}");
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理WebView消息时出错: {ex.Message}");
                await SendErrorToWebView($"处理消息时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理发送消息请求
        /// </summary>
        private async Task HandleSendMessage(dynamic data)
        {
            try
            {
                string userMessage = data?.message?.ToString();
                if (string.IsNullOrWhiteSpace(userMessage))
                    return;

                // 获取模式信息
                string mode = data?.mode?.ToString() ?? "chat";

                // 通知前端开始处理
                await SendToWebView("messageStart", new { message = userMessage, mode = mode });

                // 发送消息并获取流式回复
                // 在agent模式下启用函数调用
                bool enableFunctions = mode == "agent";

                string finalResponse = await _viewModel.SendMessageAsync(userMessage, (streamContent) =>
                {
                    // 发送流式更新到前端（必须在UI线程中执行）
                    System.Diagnostics.Debug.WriteLine($"发送流式内容到前端: '{streamContent}'");

                    // 使用Dispatcher确保在UI线程中执行
                    _webView.Dispatcher.BeginInvoke(new Action(async () =>
                    {
                        try
                        {
                            await SendToWebView("messageStream", new { content = streamContent });
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"发送流式内容失败: {ex.Message}");
                        }
                    }));
                }, mode, enableFunctions);

                System.Diagnostics.Debug.WriteLine($"AI回复完成，总长度: {finalResponse?.Length ?? 0}");

                // 通知前端消息处理完成，包含最终回复内容
                await SendToWebView("messageComplete", new { content = finalResponse ?? "" });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理发送消息失败: {ex.Message}");
                await SendErrorToWebView($"处理消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理清除对话历史请求
        /// </summary>
        private void HandleClearConversation()
        {
            _viewModel.ClearConversation();
            _ = SendToWebView("conversationCleared", null);
        }

        /// <summary>
        /// 处理获取对话历史请求
        /// </summary>
        private async Task HandleGetConversationHistory()
        {
            var history = _viewModel.GetConversationHistory();
            await SendToWebView("conversationHistory", new { history });
        }

        /// <summary>
        /// 发送消息到WebView
        /// </summary>
        /// <param name="type">消息类型</param>
        /// <param name="data">消息数据</param>
        public async Task SendToWebView(string type, object data)
        {
            try
            {
                var message = new
                {
                    type,
                    data,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                string json = JsonConvert.SerializeObject(message);
                System.Diagnostics.Debug.WriteLine($"发送到WebView: {type} - {json}");

                // 使用正确的WebView2 API方法
                _webView.CoreWebView2.PostWebMessageAsString(json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"发送消息到WebView时出错: {ex.Message}");
                Console.WriteLine($"发送消息到WebView时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送错误消息到WebView
        /// </summary>
        private async Task SendErrorToWebView(string errorMessage)
        {
            await SendToWebView("error", new { message = errorMessage });
        }
    }

    /// <summary>
    /// WebView消息模型
    /// </summary>
    public class WebMessage
    {
        public string Type { get; set; }
        public dynamic Data { get; set; }
        public string Timestamp { get; set; }
    }
}
