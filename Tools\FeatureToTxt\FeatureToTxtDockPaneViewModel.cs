using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using ArcGIS.Core.CIM;
using ArcGIS.Core.Data;
using ArcGIS.Core.Geometry;
using ArcGIS.Desktop.Core;
using ArcGIS.Desktop.Framework;
using ArcGIS.Desktop.Framework.Contracts;
using ArcGIS.Desktop.Framework.Threading.Tasks;
using ArcGIS.Desktop.Mapping;
using Microsoft.Win32;

namespace XIAOFUTools.Tools.FeatureToTxt
{
    /// <summary>
    /// 字段显示信息类
    /// </summary>
    public class FieldDisplayInfo
    {
        public string FieldName { get; set; }
        public string Alias { get; set; }
        public string FieldType { get; set; }

        public string DisplayText => string.IsNullOrEmpty(Alias) || Alias == FieldName
            ? $"{FieldName}（{FieldType}）"
            : $"{FieldName}（{Alias}）（{FieldType}）";

        public override string ToString() => DisplayText;
    }

    /// <summary>
    /// 输出字段项类
    /// </summary>
    public class OutputFieldItem : PropertyChangedBase
    {
        private string _fieldName;
        public string FieldName
        {
            get => _fieldName;
            set => SetProperty(ref _fieldName, value);
        }

        private string _description;
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        private bool _isEnabled = true;
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        private bool _canDelete = true;
        public bool CanDelete
        {
            get => _canDelete;
            set => SetProperty(ref _canDelete, value);
        }

        private bool _isAutoGenerated = false;
        public bool IsAutoGenerated
        {
            get => _isAutoGenerated;
            set => SetProperty(ref _isAutoGenerated, value);
        }

        public OutputFieldItem(string fieldName, string description, bool canDelete = true, bool isAutoGenerated = false)
        {
            FieldName = fieldName;
            Description = description;
            CanDelete = canDelete;
            IsAutoGenerated = isAutoGenerated;
        }
    }

    /// <summary>
    /// 要素类转TXT DockPane视图模型
    /// </summary>
    internal class FeatureToTxtDockPaneViewModel : PropertyChangedBase
    {
        #region 属性

        // 取消操作标志
        private bool _cancelRequested = false;
        public bool CancelRequested
        {
            get => _cancelRequested;
            set => SetProperty(ref _cancelRequested, value);
        }
        
        // 是否正在处理
        private bool _isProcessing = false;
        public bool IsProcessing
        {
            get => _isProcessing;
            set
            {
                SetProperty(ref _isProcessing, value);
                NotifyPropertyChanged(() => CanProcess);
            }
        }
        
        // 是否可以处理
        public bool CanProcess => !IsProcessing && HasSelectedLayer && !string.IsNullOrEmpty(OutputPath);

        // 面图层列表
        private ObservableCollection<FeatureLayer> _polygonLayers;
        public ObservableCollection<FeatureLayer> PolygonLayers
        {
            get => _polygonLayers;
            set => SetProperty(ref _polygonLayers, value);
        }

        // 选中的面图层
        private FeatureLayer _selectedPolygonLayer;
        public FeatureLayer SelectedPolygonLayer
        {
            get => _selectedPolygonLayer;
            set
            {
                SetProperty(ref _selectedPolygonLayer, value);
                NotifyPropertyChanged(() => HasSelectedLayer);
                NotifyPropertyChanged(() => CanProcess);
                LoadFieldNames();
            }
        }

        // 是否有选中图层
        public bool HasSelectedLayer => SelectedPolygonLayer != null;

        // 输出路径
        private string _outputPath = "";
        public string OutputPath
        {
            get => _outputPath;
            set
            {
                SetProperty(ref _outputPath, value);
                NotifyPropertyChanged(() => CanProcess);
            }
        }

        // 字段信息列表
        private ObservableCollection<FieldDisplayInfo> _fieldInfos;
        public ObservableCollection<FieldDisplayInfo> FieldInfos
        {
            get => _fieldInfos;
            set => SetProperty(ref _fieldInfos, value);
        }

        // 固定的字段顺序和对应的可能字段名
        private readonly Dictionary<string, List<string>> _fieldMappings = new Dictionary<string, List<string>>
        {
            { "地块面积", new List<string> { "地块面积", "面积", "AREA", "Shape_Area", "SHAPE_AREA", "MIANJI", "MJ" } },
            { "地块编号", new List<string> { "地块编号", "编号", "地块号", "BIANHAO", "BH", "ID", "OBJECTID", "FID" } },
            { "地块名称", new List<string> { "地块名称", "名称", "地名", "MINGCHENG", "MC", "NAME" } },
            { "图幅号", new List<string> { "图幅号", "图幅", "TUFUHAO", "TFH", "MAPSHEET" } },
            { "地块用途", new List<string> { "地块用途", "用途", "土地用途", "YONGTU", "YT", "LANDUSE" } },
            { "地类编码", new List<string> { "地类编码", "地类", "编码", "DILEI", "DL", "LANDCODE", "CODE" } }
        };

        // 输出字段配置集合
        private ObservableCollection<OutputFieldItem> _outputFields;
        public ObservableCollection<OutputFieldItem> OutputFields
        {
            get => _outputFields;
            set => SetProperty(ref _outputFields, value);
        }

        // 选项设置
        private bool _outputClosingPoint = true;
        public bool OutputClosingPoint
        {
            get => _outputClosingPoint;
            set => SetProperty(ref _outputClosingPoint, value);
        }

        private bool _innerRingStartFromOne = false;
        public bool InnerRingStartFromOne
        {
            get => _innerRingStartFromOne;
            set => SetProperty(ref _innerRingStartFromOne, value);
        }

        private bool _closingPointContinueNumbering = false;
        public bool ClosingPointContinueNumbering
        {
            get => _closingPointContinueNumbering;
            set => SetProperty(ref _closingPointContinueNumbering, value);
        }

        private bool _exportSeparately = false;
        public bool ExportSeparately
        {
            get => _exportSeparately;
            set => SetProperty(ref _exportSeparately, value);
        }

        private int _processedFeatureCount = 0;

        private bool _enableDetailedLogging = true;
        public bool EnableDetailedLogging
        {
            get => _enableDetailedLogging;
            set => SetProperty(ref _enableDetailedLogging, value);
        }

        private bool _swapXY = false;
        public bool SwapXY
        {
            get => _swapXY;
            set => SetProperty(ref _swapXY, value);
        }

        // 其他设置
        private string _prefix = "J";
        public string Prefix
        {
            get => _prefix;
            set => SetProperty(ref _prefix, value);
        }

        private int _decimalPlaces = 3;
        public int DecimalPlaces
        {
            get => _decimalPlaces;
            set => SetProperty(ref _decimalPlaces, value);
        }

        private ObservableCollection<string> _textFormats;
        public ObservableCollection<string> TextFormats
        {
            get => _textFormats;
            set => SetProperty(ref _textFormats, value);
        }

        private string _selectedTextFormat = "UTF-8";
        public string SelectedTextFormat
        {
            get => _selectedTextFormat;
            set => SetProperty(ref _selectedTextFormat, value);
        }

        // 进度值
        private int _progress = 0;
        public int Progress
        {
            get => _progress;
            set => SetProperty(ref _progress, value);
        }

        // 进度条是否不确定
        private bool _isProgressIndeterminate = false;
        public bool IsProgressIndeterminate
        {
            get => _isProgressIndeterminate;
            set => SetProperty(ref _isProgressIndeterminate, value);
        }

        // 状态消息
        private string _statusMessage = "请选择面图层。";
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        // 日志内容
        private string _logContent = "";
        public string LogContent
        {
            get => _logContent;
            set => SetProperty(ref _logContent, value);
        }

        #endregion

        #region 命令

        // 运行命令
        private ICommand _runCommand;
        public ICommand RunCommand
        {
            get
            {
                return _runCommand ?? (_runCommand = new RelayCommand(() => ExecuteAsyncSafe(), () => CanProcess));
            }
        }

        // 取消命令
        private ICommand _cancelCommand;
        public ICommand CancelCommand
        {
            get
            {
                return _cancelCommand ?? (_cancelCommand = new RelayCommand(() => Cancel(), () => IsProcessing));
            }
        }

        // 浏览输出路径命令
        private ICommand _browseOutputPathCommand;
        public ICommand BrowseOutputPathCommand
        {
            get
            {
                return _browseOutputPathCommand ?? (_browseOutputPathCommand = new RelayCommand(() => BrowseOutputPath()));
            }
        }



        // 刷新图层命令
        private ICommand _refreshLayersCommand;
        public ICommand RefreshLayersCommand
        {
            get
            {
                return _refreshLayersCommand ?? (_refreshLayersCommand = new RelayCommand(() => RefreshLayers()));
            }
        }

        // 删除输出字段命令
        private ICommand _removeOutputFieldCommand;
        public ICommand RemoveOutputFieldCommand
        {
            get
            {
                return _removeOutputFieldCommand ?? (_removeOutputFieldCommand = new RelayCommand<OutputFieldItem>((item) => RemoveOutputField(item)));
            }
        }

        // 添加输出字段命令
        private ICommand _addOutputFieldCommand;
        public ICommand AddOutputFieldCommand
        {
            get
            {
                return _addOutputFieldCommand ?? (_addOutputFieldCommand = new RelayCommand<string>((fieldName) => AddOutputField(fieldName)));
            }
        }

        // 动态获取图层字段列表
        private ObservableCollection<string> _availableFields;
        public ObservableCollection<string> AvailableFields
        {
            get => _availableFields;
            set => SetProperty(ref _availableFields, value);
        }

        // 显示帮助命令
        private ICommand _showHelpCommand;
        public ICommand ShowHelpCommand
        {
            get
            {
                return _showHelpCommand ?? (_showHelpCommand = new RelayCommand(() => ShowHelp()));
            }
        }

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public FeatureToTxtDockPaneViewModel()
        {
            // 注册编码提供程序以支持GB2312、GBK等编码
            try
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                LogInfo("编码提供程序注册成功");

                // 验证关键编码是否可用
                ValidateEncodingSupport();
            }
            catch (Exception ex)
            {
                LogError($"编码提供程序注册失败: {ex.Message}");
            }

            // 初始化属性
            PolygonLayers = new ObservableCollection<FeatureLayer>();
            FieldInfos = new ObservableCollection<FieldDisplayInfo>();
            TextFormats = new ObservableCollection<string> { "UTF-8", "UTF-8(无BOM)", "ANSI", "GBK", "Unicode" };
            OutputFields = new ObservableCollection<OutputFieldItem>();
            AvailableFields = new ObservableCollection<string>();

            // 设置默认输出路径为工程位置
            SetDefaultOutputPath();

            // 初始化默认输出字段
            InitializeDefaultOutputFields();

            StatusMessage = "正在初始化，请稍候...";
            LogContent = "";
            Progress = 0;
            IsProgressIndeterminate = false;

            LogInfo("ViewModel已初始化");
        }

        /// <summary>
        /// 设置默认输出路径为工程位置
        /// </summary>
        private void SetDefaultOutputPath()
        {
            try
            {
                // 尝试获取当前工程路径
                var project = Project.Current;
                if (project != null && !string.IsNullOrEmpty(project.Path))
                {
                    var projectDir = Path.GetDirectoryName(project.Path);
                    if (!string.IsNullOrEmpty(projectDir) && Directory.Exists(projectDir))
                    {
                        OutputPath = projectDir;
                        LogInfo($"设置默认输出路径为工程位置: {OutputPath}");
                        return;
                    }
                }

                // 如果无法获取工程路径，使用文档文件夹
                var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                if (!string.IsNullOrEmpty(documentsPath) && Directory.Exists(documentsPath))
                {
                    OutputPath = documentsPath;
                    LogInfo($"设置默认输出路径为文档文件夹: {OutputPath}");
                    return;
                }

                // 最后使用桌面
                var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                if (!string.IsNullOrEmpty(desktopPath) && Directory.Exists(desktopPath))
                {
                    OutputPath = desktopPath;
                    LogInfo($"设置默认输出路径为桌面: {OutputPath}");
                }
            }
            catch (Exception ex)
            {
                LogError($"设置默认输出路径时出错: {ex.Message}");
                // 如果出错，保持空路径
                OutputPath = "";
            }
        }

        /// <summary>
        /// 初始化默认输出字段
        /// </summary>
        private void InitializeDefaultOutputFields()
        {
            OutputFields.Add(new OutputFieldItem("点数", "自动根据下面点数生成", true, true));
            OutputFields.Add(new OutputFieldItem(",", "分隔符", true, true));
            OutputFields.Add(new OutputFieldItem("地块面积", "自动读取字段值", true, false));
            OutputFields.Add(new OutputFieldItem(",", "分隔符", true, true));
            OutputFields.Add(new OutputFieldItem("地块编号", "自动读取字段值", true, false));
            OutputFields.Add(new OutputFieldItem(",", "分隔符", true, true));
            OutputFields.Add(new OutputFieldItem("地块名称", "自动读取字段值", true, false));
            OutputFields.Add(new OutputFieldItem(",", "分隔符", true, true));
            OutputFields.Add(new OutputFieldItem("图形类型", "自动根据类型输出：点/线/面", true, true));
            OutputFields.Add(new OutputFieldItem(",", "分隔符", true, true));
            OutputFields.Add(new OutputFieldItem("图幅号", "自动读取字段值", true, false));
            OutputFields.Add(new OutputFieldItem(",", "分隔符", true, true));
            OutputFields.Add(new OutputFieldItem("地块用途", "自动读取字段值", true, false));
            OutputFields.Add(new OutputFieldItem(",", "分隔符", true, true));
            OutputFields.Add(new OutputFieldItem("地类编码", "自动读取字段值", true, false));
            OutputFields.Add(new OutputFieldItem(",", "分隔符", true, true));
            OutputFields.Add(new OutputFieldItem("@", "结束标记", true, true));
        }

        /// <summary>
        /// 刷新图层列表
        /// </summary>
        public void RefreshLayers()
        {
            StatusMessage = "正在刷新图层列表...";
            LogInfo("开始刷新图层列表");
            LogInfo($"当前PolygonLayers集合状态: {(PolygonLayers == null ? "null" : $"已初始化，包含{PolygonLayers.Count}个项目")}");

            // 添加测试数据以验证绑定
            if (PolygonLayers != null && PolygonLayers.Count == 0)
            {
                LogInfo("添加测试提示到图层列表");
                // 注意：这里不能添加null或非FeatureLayer对象，所以我们只记录日志
            }

            LoadPolygonLayers();
        }

        /// <summary>
        /// 删除输出字段
        /// </summary>
        private void RemoveOutputField(OutputFieldItem item)
        {
            if (item == null || !item.CanDelete) return;

            var index = OutputFields.IndexOf(item);
            OutputFields.Remove(item);

            // 如果删除的不是逗号，也删除前面的逗号
            if (item.FieldName != "," && index > 0 && index < OutputFields.Count + 1)
            {
                var prevItem = OutputFields.ElementAtOrDefault(index - 1);
                if (prevItem?.FieldName == ",")
                {
                    OutputFields.Remove(prevItem);
                }
            }

            StatusMessage = $"已删除字段 '{item.FieldName}'";
            LogInfo($"删除输出字段: {item.FieldName}");
        }

        /// <summary>
        /// 添加输出字段
        /// </summary>
        private void AddOutputField(string fieldName)
        {
            if (string.IsNullOrEmpty(fieldName)) return;

            // 检查是否已存在
            if (OutputFields.Any(f => f.FieldName == fieldName))
            {
                StatusMessage = $"字段 '{fieldName}' 已存在";
                return;
            }

            // 在@符号前插入新字段
            var atIndex = OutputFields.ToList().FindIndex(f => f.FieldName == "@");
            if (atIndex >= 0)
            {
                // 插入新字段
                var isAutoGenerated = fieldName == "点数" || fieldName == "图形类型";
                OutputFields.Insert(atIndex, new OutputFieldItem(fieldName, GetFieldDescription(fieldName), true, isAutoGenerated));
                // 插入逗号分隔符
                OutputFields.Insert(atIndex + 1, new OutputFieldItem(",", "分隔符", false, true));
            }

            StatusMessage = $"已添加字段 '{fieldName}'";
            LogInfo($"添加输出字段: {fieldName}");
        }

        /// <summary>
        /// 获取字段描述
        /// </summary>
        private string GetFieldDescription(string fieldName)
        {
            switch (fieldName)
            {
                case "点数": return "自动根据下面点数生成";
                case "图形类型": return "自动根据类型输出：点/线/面";
                case "@": return "结束标记";
                default: return "自动读取字段值";
            }
        }



        /// <summary>
        /// 加载面图层
        /// </summary>
        private void LoadPolygonLayers()
        {
            LogInfo("开始加载面图层...");
            StatusMessage = "正在加载图层...";

            Task.Run(async () =>
            {
                try
                {
                    var tempLayers = new List<FeatureLayer>();

                    // 等待一小段时间确保ArcGIS Pro完全初始化
                    await Task.Delay(500);

                    // 检查是否在ArcGIS Pro环境中
                    if (MapView.Active == null)
                    {
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            StatusMessage = "未检测到活动地图视图，请在ArcGIS Pro中打开地图";
                            LogWarning("MapView.Active为null，可能不在ArcGIS Pro环境中");
                        });
                        return;
                    }

                    LogInfo("检测到活动地图视图");

                    await QueuedTask.Run(() =>
                    {
                        try
                        {
                            var map = MapView.Active?.Map;
                            if (map == null)
                            {
                                LogWarning("当前地图为null");
                                return;
                            }

                            LogInfo($"当前地图名称: {map.Name}");

                            var allLayers = map.GetLayersAsFlattenedList().OfType<FeatureLayer>();
                            var layerList = allLayers.ToList();
                            var layerCount = layerList.Count;

                            LogInfo($"找到 {layerCount} 个要素图层");

                            if (layerCount == 0)
                            {
                                LogWarning("地图中没有要素图层");
                                return;
                            }

                            foreach (var layer in layerList)
                            {
                                try
                                {
                                    LogInfo($"检查图层: {layer.Name}, 类型: {layer.GetType().Name}");

                                    var featureClass = layer.GetFeatureClass();
                                    if (featureClass == null)
                                    {
                                        LogWarning($"图层 {layer.Name} 的要素类为null");
                                        continue;
                                    }

                                    var definition = featureClass.GetDefinition();
                                    if (definition == null)
                                    {
                                        LogWarning($"图层 {layer.Name} 的定义为null");
                                        continue;
                                    }

                                    var shapeType = definition.GetShapeType();
                                    LogInfo($"图层 {layer.Name} 的几何类型: {shapeType}");

                                    // 详细的图层诊断信息
                                    LogInfo($"图层 {layer.Name} 详细信息:");
                                    LogInfo($"  - 几何类型: {shapeType}");
                                    LogInfo($"  - 空间参考: {definition.GetSpatialReference()?.Name ?? "未知"}");
                                    LogInfo($"  - 字段数量: {definition.GetFields()?.Count ?? 0}");

                                    // 检查几何字段
                                    var geometryField = definition.GetFields()?.FirstOrDefault(f => f.FieldType == FieldType.Geometry);
                                    if (geometryField != null)
                                    {
                                        LogInfo($"  - 几何字段名: {geometryField.Name}");
                                        LogInfo($"  - 几何字段别名: {geometryField.AliasName}");
                                    }
                                    else
                                    {
                                        LogWarning($"  - 未找到几何字段！");
                                    }

                                    // 支持面类型
                                    if (shapeType == GeometryType.Polygon)
                                    {
                                        tempLayers.Add(layer);
                                        LogInfo($"✓ 找到面图层: {layer.Name} (类型: {shapeType})");

                                        // 检查要素数量和第一个要素的几何
                                        var featureCount = featureClass.GetCount();
                                        LogInfo($"图层 {layer.Name} 包含 {featureCount} 个要素");

                                        // 检查第一个要素的几何是否有效
                                        if (featureCount > 0)
                                        {
                                            try
                                            {
                                                // 使用非回收游标以避免GetShape()返回null
                                                using (var cursor = featureClass.Search(null, false))
                                                {
                                                    if (cursor.MoveNext())
                                                    {
                                                        var firstFeature = cursor.Current as Feature;
                                                        if (firstFeature != null)
                                                        {
                                                            var firstShape = firstFeature.GetShape();
                                                            LogInfo($"  - 第一个要素几何: {(firstShape == null ? "null" : firstShape.GeometryType.ToString())}");
                                                            LogInfo($"  - 第一个要素是否为空: {firstShape?.IsEmpty ?? true}");

                                                            if (firstShape != null && !firstShape.IsEmpty)
                                                            {
                                                                var envelope = firstShape.Extent;
                                                                LogInfo($"  - 第一个要素边界: X({envelope.XMin:F3}, {envelope.XMax:F3}), Y({envelope.YMin:F3}, {envelope.YMax:F3})");
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            catch (Exception firstFeatureEx)
                                            {
                                                LogWarning($"  - 检查第一个要素失败: {firstFeatureEx.Message}");
                                            }
                                        }
                                    }
                                    else
                                    {
                                        LogInfo($"跳过非面图层: {layer.Name} (类型: {shapeType})");

                                        // 对于非面图层，也提供一些信息
                                        if (shapeType == GeometryType.Polyline)
                                        {
                                            LogInfo($"  - 这是线图层，可能需要转换为面");
                                        }
                                        else if (shapeType == GeometryType.Point)
                                        {
                                            LogInfo($"  - 这是点图层，无法转换为坐标文件");
                                        }
                                    }
                                }
                                catch (Exception layerEx)
                                {
                                    LogWarning($"检查图层 {layer.Name} 时出错: {layerEx.Message}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            LogError($"QueuedTask执行出错: {ex.Message}");
                        }
                    });

                    // 在UI线程更新图层列表
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        try
                        {
                            LogInfo($"开始更新UI，找到 {tempLayers.Count} 个面图层");

                            PolygonLayers.Clear();

                            foreach (var layer in tempLayers)
                            {
                                PolygonLayers.Add(layer);
                                LogInfo($"添加图层到UI: {layer.Name}");
                            }

                            if (PolygonLayers.Count > 0)
                            {
                                SelectedPolygonLayer = PolygonLayers[0];
                                StatusMessage = $"成功加载 {PolygonLayers.Count} 个面图层";
                                LogInfo($"✓ UI更新完成，当前选中图层: {SelectedPolygonLayer.Name}");
                            }
                            else
                            {
                                StatusMessage = "未找到面图层，请确保地图中包含面要素图层";
                                LogWarning("未找到任何面图层");
                            }

                            // 强制刷新UI绑定
                            NotifyPropertyChanged(() => PolygonLayers);
                            NotifyPropertyChanged(() => SelectedPolygonLayer);
                            NotifyPropertyChanged(() => HasSelectedLayer);
                        }
                        catch (Exception uiEx)
                        {
                            LogError($"更新UI时出错: {uiEx.Message}");
                        }
                    });
                }
                catch (Exception ex)
                {
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        StatusMessage = $"加载图层出错: {ex.Message}";
                        LogError($"加载图层出错: {ex.Message}");
                        LogError($"异常详情: {ex}");
                    });
                }
            });
        }

        /// <summary>
        /// 加载字段信息并进行自动匹配
        /// </summary>
        private void LoadFieldNames()
        {
            if (SelectedPolygonLayer == null)
            {
                FieldInfos?.Clear();
                return;
            }

            Task.Run(async () =>
            {
                try
                {
                    var tempFieldInfos = new List<FieldDisplayInfo>();
                    var matchingResults = new List<string>();

                    await QueuedTask.Run(() =>
                    {
                        var featureClass = SelectedPolygonLayer.GetFeatureClass();
                        if (featureClass != null)
                        {
                            var definition = featureClass.GetDefinition();
                            var fields = definition.GetFields();
                            var fieldNames = fields.Select(f => f.Name).ToList();

                            // 收集所有字段信息
                            foreach (var field in fields)
                            {
                                if (field.FieldType == FieldType.Double ||
                                    field.FieldType == FieldType.Single ||
                                    field.FieldType == FieldType.Integer ||
                                    field.FieldType == FieldType.SmallInteger ||
                                    field.FieldType == FieldType.String)
                                {
                                    var fieldInfo = new FieldDisplayInfo
                                    {
                                        FieldName = field.Name,
                                        Alias = field.AliasName,
                                        FieldType = GetFieldTypeDisplayName(field.FieldType)
                                    };
                                    tempFieldInfos.Add(fieldInfo);
                                }
                            }

                            // 进行字段自动匹配
                            LogInfo("开始进行字段自动匹配...");
                            foreach (var mapping in _fieldMappings)
                            {
                                var matchedField = FindMatchingField(fieldNames, mapping.Value);
                                if (!string.IsNullOrEmpty(matchedField))
                                {
                                    matchingResults.Add($"✓ {mapping.Key}: {matchedField}");
                                    LogInfo($"字段匹配成功 - {mapping.Key}: {matchedField}");
                                }
                                else
                                {
                                    matchingResults.Add($"✗ {mapping.Key}: 未找到匹配字段");
                                    LogWarning($"字段匹配失败 - {mapping.Key}: 未找到匹配字段");
                                }
                            }
                        }
                    });

                    // 在UI线程更新字段列表和匹配结果
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        FieldInfos?.Clear();
                        AvailableFields?.Clear();

                        if (FieldInfos != null && AvailableFields != null)
                        {
                            // 添加实际字段
                            foreach (var fieldInfo in tempFieldInfos)
                            {
                                FieldInfos.Add(fieldInfo);
                                AvailableFields.Add(fieldInfo.FieldName);
                            }

                            // 添加分隔符
                            AvailableFields.Add("---实际字段---");

                            // 添加特殊字段
                            AvailableFields.Add("点数");
                            AvailableFields.Add("图形类型");
                            AvailableFields.Add(",");
                            AvailableFields.Add("@");
                        }

                        // 记录匹配结果
                        LogInfo($"字段匹配完成，共匹配 {matchingResults.Count(r => r.StartsWith("✓"))} 个字段");
                        LogInfo($"可用字段数量: {AvailableFields?.Count ?? 0}");
                    });
                }
                catch (Exception ex)
                {
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        StatusMessage = $"加载字段出错: {ex.Message}";
                        LogError($"加载字段出错: {ex.Message}");
                    });
                }
            });
        }

        /// <summary>
        /// 查找匹配的字段名
        /// </summary>
        private string FindMatchingField(List<string> availableFields, List<string> possibleNames)
        {
            foreach (var possibleName in possibleNames)
            {
                // 精确匹配
                var exactMatch = availableFields.FirstOrDefault(f =>
                    string.Equals(f, possibleName, StringComparison.OrdinalIgnoreCase));
                if (!string.IsNullOrEmpty(exactMatch))
                {
                    return exactMatch;
                }

                // 包含匹配
                var containsMatch = availableFields.FirstOrDefault(f =>
                    f.IndexOf(possibleName, StringComparison.OrdinalIgnoreCase) >= 0);
                if (!string.IsNullOrEmpty(containsMatch))
                {
                    return containsMatch;
                }
            }
            return null;
        }

        /// <summary>
        /// 获取字段类型的显示名称
        /// </summary>
        private string GetFieldTypeDisplayName(FieldType fieldType)
        {
            return fieldType switch
            {
                FieldType.Double => "双精度",
                FieldType.Single => "单精度",
                FieldType.Integer => "整型",
                FieldType.SmallInteger => "短整型",
                FieldType.String => "文本",
                _ => "未知类型"
            };
        }

        /// <summary>
        /// 浏览输出路径
        /// </summary>
        private void BrowseOutputPath()
        {
            var dialog = new XIAOFUTools.Common.WpfFolderBrowserDialog
            {
                Description = "选择输出文件夹",
                ShowNewFolderButton = true
            };

            if (dialog.ShowDialog())
            {
                OutputPath = dialog.SelectedPath;
            }
        }



        /// <summary>
        /// 取消操作
        /// </summary>
        private void Cancel()
        {
            CancelRequested = true;
            StatusMessage = "正在取消操作...";
        }

        /// <summary>
        /// 根据文本格式获取对应的编码
        /// </summary>
        private Encoding GetEncodingFromFormat(string format)
        {
            try
            {
                switch (format?.ToUpper())
                {
                    case "ANSI":
                        // 在Windows中文环境下，ANSI通常指GBK编码
                        return Encoding.GetEncoding("GBK");
                    case "GBK":
                        return Encoding.GetEncoding("GBK");
                    case "UNICODE":
                        return Encoding.Unicode;
                    case "UTF-8(无BOM)":
                        // 返回无BOM的UTF-8编码
                        return new UTF8Encoding(false);
                    case "UTF-8":
                    default:
                        // 默认UTF-8带BOM
                        return new UTF8Encoding(true);
                }
            }
            catch (Exception ex)
            {
                LogError($"获取编码失败，使用UTF-8: {ex.Message}");
                return new UTF8Encoding(true);
            }
        }

        /// <summary>
        /// 验证编码支持
        /// </summary>
        private void ValidateEncodingSupport()
        {
            var encodingsToTest = new[]
            {
                new { Name = "GBK", CodePage = "GBK" },
                new { Name = "GB2312", CodePage = "GB2312" },
                new { Name = "Big5", CodePage = "Big5" }
            };

            foreach (var encoding in encodingsToTest)
            {
                try
                {
                    var enc = Encoding.GetEncoding(encoding.CodePage);
                    LogInfo($"编码 {encoding.Name} 支持正常 (CodePage: {enc.CodePage})");
                }
                catch (Exception ex)
                {
                    LogError($"编码 {encoding.Name} 不支持: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 显示帮助信息
        /// </summary>
        private void ShowHelp()
        {
            var helpContent = @"要素类转TXT工具帮助

功能描述：
将面要素图层转换为TXT格式的坐标文件，支持合并导出和分块导出两种模式。

参数说明：
• 要素图层(面)：选择需要转换的面要素图层
• 输出文件夹：指定生成的TXT文件保存文件夹
• 输出字段配置：自定义输出字段的顺序和内容
• 选项设置：配置输出格式和坐标处理选项

文件命名规则：
• 合并导出（未勾选分块导出）：使用图层名称作为文件名
  例如：图层名为""地块数据"" → 生成""地块数据.txt""
• 分块导出（勾选分块导出）：图层名称 + 序号
  例如：图层名为""地块数据"" → 生成""地块数据_000001.txt""、""地块数据_000002.txt""等

导出模式：
• 合并导出：所有要素的坐标写入一个TXT文件
• 分块导出：每个要素生成一个单独的TXT文件

字段配置操作：
• 拖拽：可以拖拽字段块调整输出顺序
• 右键：右键点击可添加图层中的实际字段
• 删除：点击字段块右上角的×按钮删除字段
• 特殊字段：点数、图形类型、逗号、@等

选项设置：
• 是否输出闭合点：控制是否输出多边形的闭合点
• 内环1起编：内环坐标点从1开始编号
• 是否闭合点续编：闭合点是否继续编号
• 分块导出：控制是否每个要素单独导出为一个文件
• XY互换：交换X和Y坐标的输出顺序
• 前缀：坐标点编号的前缀文本
• 小数位数：坐标值的小数位数

操作步骤：
1. 选择要转换的面要素图层
2. 选择输出文件夹
3. 配置输出字段和选项
4. 选择导出模式（是否勾选分块导出）
5. 点击""生成TXT""开始转换

注意事项：
• 确保选择的图层包含面要素
• 输出文件夹必须存在且有写入权限
• 文件名会自动移除非法字符
• 大量要素转换可能需要较长时间
• 转换过程中可以点击""停止""按钮取消操作";

            ArcGIS.Desktop.Framework.Dialogs.MessageBox.Show(helpContent, "要素类转TXT工具帮助");
        }

        /// <summary>
        /// 安全启动异步执行，不阻塞UI线程
        /// </summary>
        private void ExecuteAsyncSafe()
        {
            // 立即启动后台任务，不等待完成，避免阻塞UI线程
            _ = Task.Run(async () =>
            {
                try
                {
                    await ExecuteAsync();
                }
                catch (Exception ex)
                {
                    // 确保异常在UI线程中处理
                    await System.Windows.Application.Current.Dispatcher.BeginInvoke(() =>
                    {
                        StatusMessage = $"执行失败: {ex.Message}";
                        IsProcessing = false;
                    });
                    LogError($"执行失败: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 执行转换（异步版本，避免UI阻塞）
        /// </summary>
        private async Task ExecuteAsync()
        {
            if (SelectedPolygonLayer == null)
            {
                StatusMessage = "请选择面图层。";
                return;
            }

            if (string.IsNullOrEmpty(OutputPath))
            {
                StatusMessage = "请选择输出文件夹。";
                return;
            }

            // 检查输出路径是否为有效文件夹
            if (!Directory.Exists(OutputPath))
            {
                StatusMessage = "输出路径不是有效的文件夹。";
                return;
            }

            // 在UI线程中更新状态
            await System.Windows.Application.Current.Dispatcher.BeginInvoke(() =>
            {
                IsProcessing = true;
                CancelRequested = false;
                Progress = 0;
                IsProgressIndeterminate = true;
                StatusMessage = "正在转换要素...";
                LogContent = "";
            });

            try
            {

                    LogInfo($"开始转换 - 图层: {SelectedPolygonLayer.Name}", true);
                    LogInfo($"输出文件夹: {OutputPath}", true);

                    await QueuedTask.Run(async () =>
                {
                    try
                    {
                        if (CancelRequested) return;

                        var featureClass = SelectedPolygonLayer.GetFeatureClass();
                        if (featureClass == null)
                        {
                            LogError("无法获取要素类");
                            return;
                        }

                        // 使用非回收游标（recycling=false）以避免GetShape()返回null
                        // 这是关键修复：recycling=true（默认）会导致feature.GetShape()返回null
                        var cursor = featureClass.Search(null, false);
                        int featureCount = 0;
                        int processedCount = 0;
                        _processedFeatureCount = 0;

                        // 首先计算总要素数
                        var totalFeatures = new List<Feature>();
                        while (cursor.MoveNext())
                        {
                            var feature = cursor.Current as Feature;
                            if (feature != null)
                            {
                                totalFeatures.Add(feature);
                            }
                        }
                        cursor.Dispose();

                        LogInfo($"找到 {totalFeatures.Count} 个要素，开始处理", true);

                        if (ExportSeparately)
                        {
                            // 分块导出：每个要素生成单独文件
                            LogInfo("分块导出模式：每个要素生成单独文件");

                            // 处理所有要素
                            for (int i = 0; i < totalFeatures.Count; i++)
                            {
                                if (CancelRequested) return;

                                var feature = totalFeatures[i];
                                featureCount = i + 1;

                                try
                                {
                                    // 极简日志输出
                                    if (featureCount == 1 || featureCount % 200 == 0)
                                    {
                                        LogInfo($"处理要素 {featureCount}");
                                    }

                                    // 在QueuedTask中获取几何对象并处理
                                    var shape = feature.GetShape();

                                    if (shape is Polygon geometry)
                                    {
                                        // 为每个要素生成单独的文件（已经在QueuedTask中）
                                        await ProcessSingleFeatureToFileInTask(feature, geometry, featureCount);
                                        processedCount++;
                                    }
                                    else
                                    {
                                        // 移除警告日志，避免大量输出
                                    }
                                }
                                catch (Exception shapeEx)
                                {
                                    LogError($"处理要素 {featureCount} 时出错: {shapeEx.Message}");
                                }

                                // 减少UI更新频率，提升性能
                                if (featureCount % 100 == 0 || featureCount == totalFeatures.Count)
                                {
                                    var progress = (int)((double)featureCount / totalFeatures.Count * 100);

                                    _ = System.Windows.Application.Current?.Dispatcher.BeginInvoke(() =>
                                    {
                                        Progress = progress;
                                        StatusMessage = $"已处理 {featureCount}/{totalFeatures.Count} 个要素";
                                    });

                                    await Task.Delay(1);
                                }
                            }
                        }
                        else
                        {
                            // 合并导出：所有要素生成一个文件
                            LogInfo("合并导出模式：所有要素生成一个文件");
                            await ProcessAllFeaturesToSingleFile(totalFeatures);
                            processedCount = totalFeatures.Count;
                        }

                        if (processedCount == 0)
                        {
                            LogWarning("没有成功处理任何要素");
                            return;
                        }

                        LogInfo($"转换完成，成功处理了 {processedCount} 个要素");
                    }
                    catch (Exception ex)
                    {
                        LogError($"转换过程中出错: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                await System.Windows.Application.Current.Dispatcher.BeginInvoke(() =>
                {
                    StatusMessage = $"转换失败: {ex.Message}";
                });
                LogError($"转换失败: {ex.Message}");
            }
            finally
            {
                // 确保UI状态更新在UI线程中进行
                await System.Windows.Application.Current.Dispatcher.BeginInvoke(() =>
                {
                    IsProcessing = false;
                    IsProgressIndeterminate = false;
                    Progress = 100;
                    if (!CancelRequested)
                    {
                        StatusMessage = "转换完成。";
                    }
                    else
                    {
                        StatusMessage = "操作已取消。";
                    }
                });
            }
        }

        /// <summary>
        /// 在QueuedTask中导出单个文件
        /// </summary>
        private async Task ExportSingleFileInTask(List<Feature> features)
        {
            var content = new StringBuilder();

            // 添加属性描述
            content.AppendLine("[属性描述]");
            content.AppendLine("坐标系=2000国家大地坐标系");
            content.AppendLine("几度分带=3");
            content.AppendLine("投影类型=高斯克吕格");
            content.AppendLine("计量单位=米");
            content.AppendLine("带号=37");
            content.AppendLine($"精度={Math.Pow(10, -DecimalPlaces).ToString($"F{DecimalPlaces}")}");
            content.AppendLine("转换参数=,,,,,,");
            content.AppendLine("[地块坐标]");

            // 处理所有要素
            for (int i = 0; i < features.Count; i++)
            {
                if (CancelRequested) return;

                var feature = features[i];
                ProcessFeatureSync(feature, content, i + 1);

                // 更新进度
                var progress = (int)((double)(i + 1) / features.Count * 100);
                System.Windows.Application.Current?.Dispatcher.BeginInvoke(() =>
                {
                    Progress = progress;
                    StatusMessage = $"正在处理第 {i + 1}/{features.Count} 个要素...";
                });
            }

            // 保存文件
            var contentString = content.ToString();
            await SaveToFile(contentString, OutputPath);
        }

        /// <summary>
        /// 在QueuedTask中导出分离文件
        /// </summary>
        private async Task ExportSeparateFilesInTask(List<Feature> features)
        {
            var directory = Path.GetDirectoryName(OutputPath);
            var fileNameWithoutExt = Path.GetFileNameWithoutExtension(OutputPath);
            var extension = Path.GetExtension(OutputPath);

            for (int i = 0; i < features.Count; i++)
            {
                if (CancelRequested) return;

                var feature = features[i];
                var content = new StringBuilder();

                // 添加属性描述
                content.AppendLine("[属性描述]");
                content.AppendLine("坐标系=2000国家大地坐标系");
                content.AppendLine("几度分带=3");
                content.AppendLine("投影类型=高斯克吕格");
                content.AppendLine("计量单位=米");
                content.AppendLine("带号=37");
                content.AppendLine($"精度={Math.Pow(10, -DecimalPlaces).ToString($"F{DecimalPlaces}")}");
                content.AppendLine("转换参数=,,,,,,");
                content.AppendLine("[地块坐标]");

                ProcessFeatureSync(feature, content, 1);

                // 生成文件名
                var fileName = $"{fileNameWithoutExt}_{i + 1:D3}{extension}";
                var filePath = Path.Combine(directory, fileName);

                // 保存文件
                var contentString = content.ToString();
                await SaveToFile(contentString, filePath);

                // 更新进度
                var progress = (int)((double)(i + 1) / features.Count * 100);
                System.Windows.Application.Current?.Dispatcher.BeginInvoke(() =>
                {
                    Progress = progress;
                    StatusMessage = $"正在处理第 {i + 1}/{features.Count} 个要素...";
                });
            }
        }

        /// <summary>
        /// 导出单个文件（异步版本，保留兼容性）
        /// </summary>
        private async Task ExportSingleFile(List<Feature> features)
        {
            var content = new StringBuilder();

            // 添加属性描述
            content.AppendLine("[属性描述]");
            content.AppendLine("坐标系=2000国家大地坐标系");
            content.AppendLine("几度分带=3");
            content.AppendLine("投影类型=高斯克吕格");
            content.AppendLine("计量单位=米");
            content.AppendLine("带号=37");
            content.AppendLine($"精度={Math.Pow(10, -DecimalPlaces).ToString($"F{DecimalPlaces}")}");
            content.AppendLine("转换参数=,,,,,,");
            content.AppendLine("[地块坐标]");

            // 在QueuedTask中处理所有要素
            await QueuedTask.Run(async () =>
            {
                for (int i = 0; i < features.Count; i++)
                {
                    if (CancelRequested) return;

                    var feature = features[i];
                    ProcessFeatureSync(feature, content, i + 1);

                    // 更新进度
                    var progress = (int)((double)(i + 1) / features.Count * 100);
                    System.Windows.Application.Current?.Dispatcher.BeginInvoke(() =>
                    {
                        Progress = progress;
                        StatusMessage = $"正在处理第 {i + 1}/{features.Count} 个要素...";
                    });
                }
            });

            // 保存文件
            await SaveToFile(content.ToString(), OutputPath);
        }

        /// <summary>
        /// 导出分离文件
        /// </summary>
        private async Task ExportSeparateFiles(List<Feature> features)
        {
            var directory = Path.GetDirectoryName(OutputPath);
            var fileNameWithoutExt = Path.GetFileNameWithoutExtension(OutputPath);
            var extension = Path.GetExtension(OutputPath);

            // 在QueuedTask中处理所有要素
            await QueuedTask.Run(async () =>
            {
                for (int i = 0; i < features.Count; i++)
                {
                    if (CancelRequested) return;

                    var feature = features[i];
                    var content = new StringBuilder();

                    // 添加属性描述
                    content.AppendLine("[属性描述]");
                    content.AppendLine("坐标系=2000国家大地坐标系");
                    content.AppendLine("几度分带=3");
                    content.AppendLine("投影类型=高斯克吕格");
                    content.AppendLine("计量单位=米");
                    content.AppendLine("带号=37");
                    content.AppendLine($"精度={Math.Pow(10, -DecimalPlaces).ToString($"F{DecimalPlaces}")}");
                    content.AppendLine("转换参数=,,,,,,");
                    content.AppendLine("[地块坐标]");

                    ProcessFeatureSync(feature, content, 1);

                    // 生成文件名
                    var fileName = $"{fileNameWithoutExt}_{i + 1:D3}{extension}";
                    var filePath = Path.Combine(directory, fileName);

                    await SaveToFile(content.ToString(), filePath);

                    // 更新进度
                    var progress = (int)((double)(i + 1) / features.Count * 100);
                    System.Windows.Application.Current?.Dispatcher.BeginInvoke(() =>
                    {
                        Progress = progress;
                        StatusMessage = $"正在处理第 {i + 1}/{features.Count} 个要素...";
                    });
                }
            });
        }

        /// <summary>
        /// 处理所有要素到单个文件
        /// </summary>
        private async Task ProcessAllFeaturesToSingleFile(List<Feature> features)
        {
            try
            {
                // 使用图层名称作为文件名
                var layerName = SelectedPolygonLayer?.Name ?? "未知图层";
                var safeFileName = GetSafeFileName(layerName);
                var filePath = Path.Combine(OutputPath, $"{safeFileName}.txt");

                LogInfo($"合并导出到文件: {filePath}");

                // 创建文件内容
                var content = new StringBuilder();

                // 添加属性描述
                content.AppendLine("[属性描述]");
                content.AppendLine("坐标系=2000国家大地坐标系");
                content.AppendLine("几度分带=3");
                content.AppendLine("投影类型=高斯克吕格");
                content.AppendLine("计量单位=米");
                content.AppendLine("带号=37");
                content.AppendLine($"精度={Math.Pow(10, -DecimalPlaces).ToString($"F{DecimalPlaces}")}");
                content.AppendLine("转换参数=,,,,,,");
                content.AppendLine("[地块坐标]");

                // 在QueuedTask中处理所有要素的几何操作
                await QueuedTask.Run(() =>
                {
                    // 处理所有要素
                    for (int i = 0; i < features.Count; i++)
                    {
                        if (CancelRequested) return;

                        var feature = features[i];
                        try
                        {
                            // 移除详细日志，提升性能
                            var shape = feature.GetShape();

                            // 无论几何是否为null，都调用ProcessFeatureSync进行详细诊断
                            ProcessFeatureSync(feature, content, i + 1);
                        }
                        catch (Exception ex)
                        {
                            LogError($"处理要素 {i + 1} 时出错: {ex.Message}");
                        }

                        // 优化进度更新频率，减少UI阻塞
                        if ((i + 1) % 10 == 0 || (i + 1) == features.Count)
                        {
                            var progress = (int)((double)(i + 1) / features.Count * 100);
                            System.Windows.Application.Current?.Dispatcher.BeginInvoke(() =>
                            {
                                Progress = progress;
                                StatusMessage = $"正在处理第 {i + 1}/{features.Count} 个要素...";
                            });
                        }
                    }
                });

                // 保存文件
                var contentString = content.ToString();
                LogInfo($"文件内容长度: {contentString.Length} 字符");
                LogInfo($"文件内容预览: {contentString.Substring(0, Math.Min(200, contentString.Length))}...");

                await SaveToFile(contentString, filePath);

                LogInfo($"合并导出完成: {safeFileName}.txt");
            }
            catch (Exception ex)
            {
                LogError($"合并导出时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理单个要素并生成文件（在QueuedTask中调用）
        /// </summary>
        private async Task ProcessSingleFeatureToFileInTask(Feature feature, Polygon geometry, int featureIndex)
        {
            try
            {
                // 获取要素名称用作文件名
                var featureName = GetFeatureName(feature, featureIndex);
                var safeFileName = GetSafeFileName(featureName);
                var filePath = Path.Combine(OutputPath, $"{safeFileName}.txt");

                // 移除文件生成日志，提升性能

                // 创建文件内容
                var content = new StringBuilder();

                // 添加属性描述
                content.AppendLine("[属性描述]");
                content.AppendLine("坐标系=2000国家大地坐标系");
                content.AppendLine("几度分带=3");
                content.AppendLine("投影类型=高斯克吕格");
                content.AppendLine("计量单位=米");
                content.AppendLine("带号=37");
                content.AppendLine($"精度={Math.Pow(10, -DecimalPlaces).ToString($"F{DecimalPlaces}")}");
                content.AppendLine("转换参数=,,,,,,");
                content.AppendLine("[地块坐标]");

                // 直接处理要素坐标（已经在QueuedTask中）
                ProcessFeatureSync(feature, content, featureIndex);

                // 保存文件 - 使用ConfigureAwait(false)避免死锁
                var contentString = content.ToString();
                await SaveToFile(contentString, filePath).ConfigureAwait(false);

                // 移除完成日志，提升性能
            }
            catch (Exception ex)
            {
                LogError($"为要素 {featureIndex} 生成文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理单个要素并生成文件（异步版本，保留兼容性）
        /// </summary>
        private async Task ProcessSingleFeatureToFile(Feature feature, Polygon geometry, int featureIndex)
        {
            try
            {
                // 获取要素名称用作文件名
                var featureName = GetFeatureName(feature, featureIndex);
                var safeFileName = GetSafeFileName(featureName);
                var filePath = Path.Combine(OutputPath, $"{safeFileName}.txt");

                LogInfo($"为要素 {featureIndex} 生成文件: {filePath}");

                // 创建文件内容
                var content = new StringBuilder();

                // 添加属性描述
                content.AppendLine("[属性描述]");
                content.AppendLine("坐标系=2000国家大地坐标系");
                content.AppendLine("几度分带=3");
                content.AppendLine("投影类型=高斯克吕格");
                content.AppendLine("计量单位=米");
                content.AppendLine("带号=37");
                content.AppendLine($"精度={Math.Pow(10, -DecimalPlaces).ToString($"F{DecimalPlaces}")}");
                content.AppendLine("转换参数=,,,,,,");
                content.AppendLine("[地块坐标]");

                // 在QueuedTask中处理要素坐标
                await QueuedTask.Run(() =>
                {
                    ProcessFeatureSync(feature, content, featureIndex);
                });

                // 保存文件
                var contentString = content.ToString();
                LogInfo($"要素 {featureIndex} 文件内容长度: {contentString.Length} 字符");

                await SaveToFile(contentString, filePath);

                LogInfo($"要素 {featureIndex} 文件生成完成: {safeFileName}.txt");
            }
            catch (Exception ex)
            {
                LogError($"为要素 {featureIndex} 生成文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取要素名称
        /// </summary>
        private string GetFeatureName(Feature feature, int featureIndex)
        {
            try
            {
                // 使用图层名称作为基础文件名
                var layerName = SelectedPolygonLayer?.Name ?? "未知图层";

                // 如果勾选了分块导出，在图层名称后加上序号
                if (ExportSeparately)
                {
                    return $"{layerName}_{featureIndex:D6}";
                }
                else
                {
                    return layerName;
                }
            }
            catch (Exception ex)
            {
                LogError($"获取要素 {featureIndex} 名称时出错: {ex.Message}");
                return $"要素_{featureIndex:D6}";
            }
        }

        /// <summary>
        /// 获取安全的文件名（移除非法字符）
        /// </summary>
        private string GetSafeFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
            {
                return "未命名";
            }

            // 替换文件名中的非法字符
            char[] invalidChars = Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            // 替换其他可能导致问题的字符
            fileName = fileName.Replace("\\", "_")
                              .Replace("/", "_")
                              .Replace(":", "_")
                              .Replace("*", "_")
                              .Replace("?", "_")
                              .Replace("\"", "_")
                              .Replace("<", "_")
                              .Replace(">", "_")
                              .Replace("|", "_");

            // 移除多余的下划线
            while (fileName.Contains("__"))
            {
                fileName = fileName.Replace("__", "_");
            }

            // 移除文件名开头和末尾的下划线
            fileName = fileName.Trim('_');

            // 如果文件名为空，使用默认值
            if (string.IsNullOrWhiteSpace(fileName))
            {
                fileName = "未命名";
            }

            // 限制文件名长度
            if (fileName.Length > 100)
            {
                fileName = fileName.Substring(0, 100);
            }

            return fileName;
        }

        /// <summary>
        /// 立即处理要素（在获取时直接处理）
        /// </summary>
        private void ProcessFeatureImmediately(Feature feature, Polygon geometry, StringBuilder content, int featureIndex)
        {
            try
            {
                LogInfo($"立即处理要素 {featureIndex}，几何类型: {geometry.GeometryType}，环数: {geometry.PartCount}");

                // 根据配置的输出字段获取值
                var fieldValues = new List<string>();

                foreach (var outputField in OutputFields.Where(f => f.IsEnabled))
                {
                    string value = "";

                    switch (outputField.FieldName)
                    {
                        case "点数":
                            value = ""; // 稍后计算
                            break;
                        case ",":
                            value = ",";
                            break;
                        case "地块面积":
                            value = GetFieldValueByMapping("地块面积", feature) ?? "0";
                            LogInfo($"地块面积: {value}");
                            break;
                        case "地块编号":
                            value = GetFieldValueByMapping("地块编号", feature) ?? featureIndex.ToString();
                            LogInfo($"地块编号: {value}");
                            break;
                        case "地块名称":
                            value = GetFieldValueByMapping("地块名称", feature) ?? "";
                            LogInfo($"地块名称: {value}");
                            break;
                        case "图形类型":
                            value = "面"; // 固定为面
                            break;
                        case "图幅号":
                            value = GetFieldValueByMapping("图幅号", feature) ?? "";
                            LogInfo($"图幅号: {value}");
                            break;
                        case "地块用途":
                            value = GetFieldValueByMapping("地块用途", feature) ?? "";
                            LogInfo($"地块用途: {value}");
                            break;
                        case "地类编码":
                            value = GetFieldValueByMapping("地类编码", feature) ?? "";
                            LogInfo($"地类编码: {value}");
                            break;
                        case "@":
                            value = "@";
                            break;
                        default:
                            value = GetFieldValueByMapping(outputField.FieldName, feature) ?? "";
                            break;
                    }

                    fieldValues.Add(value);
                }

                // 计算总点数和写入坐标
                WriteFeatureCoordinatesImmediately(geometry, content, fieldValues, featureIndex);

                LogInfo($"要素 {featureIndex} 处理完成");
            }
            catch (Exception ex)
            {
                LogError($"立即处理要素 {featureIndex} 时出错: {ex.Message}");
                LogError($"异常详情: {ex}");
            }
        }

        /// <summary>
        /// 处理单个要素（高性能版本，减少日志输出）
        /// </summary>
        private void ProcessFeatureSync(Feature feature, StringBuilder content, int featureIndex)
        {
            try
            {
                _processedFeatureCount++;
                bool isDetailedLogging = _processedFeatureCount <= 3; // 只对前3个要素输出详细日志

                // 极简日志输出，只在关键节点
                if (featureIndex == 1 || featureIndex % 500 == 0)
                {
                    LogInfo($"处理要素 {featureIndex}");
                }

                // 首先检查要素本身
                if (feature == null)
                {
                    LogError($"要素 {featureIndex} 为null");
                    return;
                }

                if (isDetailedLogging)
                {
                    LogInfo($"要素 {featureIndex} 基本信息:", true);
                    LogInfo($"  - 要素类: {feature.GetTable()?.GetName() ?? "未知"}", true);

                    // 尝试获取要素的字段信息
                    try
                    {
                        var fields = feature.GetFields();
                        LogInfo($"  - 字段数量: {fields?.Count ?? 0}", true);
                        if (fields != null && fields.Count > 0)
                        {
                            for (int i = 0; i < Math.Min(3, fields.Count); i++)
                            {
                                var field = fields[i];
                                LogInfo($"    字段 {i + 1}: {field.Name} ({field.FieldType})", true);
                            }
                        }
                    }
                    catch (Exception fieldEx)
                    {
                        LogWarning($"  - 无法获取字段信息: {fieldEx.Message}");
                    }
                }

                // 直接获取几何对象（已经在QueuedTask中）
                Geometry shape = null;
                try
                {
                    shape = feature.GetShape();
                    if (shape != null)
                    {
                        if (isDetailedLogging)
                        {
                            LogInfo($"要素 {featureIndex} 几何获取成功", true);
                        }
                    }
                    else
                    {
                        LogWarning($"要素 {featureIndex} 几何获取返回null，可能是几何无效");

                        // 尝试使用几何验证和修复
                        try
                        {
                            if (isDetailedLogging)
                            {
                                LogInfo($"尝试验证和修复要素 {featureIndex} 的几何...", true);
                            }

                            // 获取原始几何数据（可能包含无效几何）
                            var table = feature.GetTable();
                            var oid = feature.GetObjectID();
                            if (isDetailedLogging)
                            {
                                LogInfo($"要素OID: {oid}", true);
                            }

                            // 尝试使用不同的方法获取几何
                            var shapeFieldName = "Shape"; // 默认几何字段名
                            var definition = table.GetDefinition();
                            var geometryField = definition.GetFields().FirstOrDefault(f => f.FieldType == FieldType.Geometry);
                            if (geometryField != null)
                            {
                                shapeFieldName = geometryField.Name;
                                if (isDetailedLogging)
                                {
                                    LogInfo($"几何字段名: {shapeFieldName}", true);
                                }
                            }

                            // 尝试直接从字段获取几何
                            var rawGeometry = feature[shapeFieldName] as Geometry;
                            if (rawGeometry != null)
                            {
                                LogInfo($"从字段直接获取几何成功: {rawGeometry.GeometryType}");
                                LogInfo($"几何是否为空: {rawGeometry.IsEmpty}");
                                LogInfo($"几何是否有效: {!rawGeometry.IsEmpty}");

                                // 尝试验证几何
                                if (rawGeometry is Polygon rawPolygon)
                                {
                                    LogInfo($"原始多边形信息:");
                                    LogInfo($"  - 环数: {rawPolygon.PartCount}");
                                    LogInfo($"  - 是否为空: {rawPolygon.IsEmpty}");
                                    LogInfo($"  - 维度: {rawPolygon.Dimension}");

                                    // 检查是否有自相交或其他问题
                                    try
                                    {
                                        var envelope = rawPolygon.Extent;
                                        LogInfo($"  - 边界框: X({envelope.XMin:F3}, {envelope.XMax:F3}), Y({envelope.YMin:F3}, {envelope.YMax:F3})");

                                        // 检查面积
                                        var area = rawPolygon.Area;
                                        LogInfo($"  - 面积: {area:F6}");

                                        if (area > 0)
                                        {
                                            LogInfo($"  - 面积大于0，几何看起来有效");

                                            // 尝试使用这个几何
                                            shape = rawPolygon;
                                            LogInfo($"使用原始几何数据成功");
                                        }
                                        else
                                        {
                                            LogWarning($"  - 面积为0或负数，几何可能无效");
                                        }
                                    }
                                    catch (Exception geomEx)
                                    {
                                        LogError($"原始几何数据也有问题: {geomEx.Message}");
                                        LogError($"可能的问题：自相交、扣洞方向错误、或其他拓扑问题");
                                    }
                                }
                            }
                            else
                            {
                                LogError($"从字段也无法获取几何数据");

                                // 尝试更深入的诊断
                                try
                                {
                                    LogInfo($"深入诊断要素 {featureIndex} 的数据...");

                                    // 检查字段值
                                    var shapeFieldValue = feature[shapeFieldName];
                                    LogInfo($"Shape字段原始值类型: {shapeFieldValue?.GetType().Name ?? "null"}");
                                    LogInfo($"Shape字段原始值: {shapeFieldValue ?? "null"}");

                                    // 检查所有字段的值
                                    var fields = feature.GetFields();
                                    LogInfo($"要素所有字段值:");
                                    for (int i = 0; i < fields.Count; i++)
                                    {
                                        var field = fields[i];
                                        try
                                        {
                                            var value = feature[field.Name];
                                            LogInfo($"  {field.Name} ({field.FieldType}): {value ?? "null"}");
                                        }
                                        catch (Exception fieldEx)
                                        {
                                            LogWarning($"  {field.Name}: 无法获取值 - {fieldEx.Message}");
                                        }
                                    }

                                    // 检查要素是否有效
                                    LogInfo($"要素状态检查:");
                                    LogInfo($"  - OID: {feature.GetObjectID()}");
                                    LogInfo($"  - 表名: {feature.GetTable().GetName()}");

                                    // 尝试使用不同的方法获取几何
                                    LogInfo($"尝试其他方法获取几何...");

                                    // 方法1：尝试使用索引获取
                                    try
                                    {
                                        var shapeByIndex = feature[1]; // Shape通常是第二个字段
                                        LogInfo($"通过索引获取Shape: {shapeByIndex?.GetType().Name ?? "null"}");
                                    }
                                    catch (Exception indexEx)
                                    {
                                        LogWarning($"通过索引获取Shape失败: {indexEx.Message}");
                                    }

                                    // 方法2：检查是否是空几何
                                    LogInfo($"结论：该要素的几何数据在SHP文件中可能确实为空或损坏");
                                    LogInfo($"建议：");
                                    LogInfo($"  1. 在ArcGIS Pro中检查该要素是否可见");
                                    LogInfo($"  2. 运行'检查几何'工具验证SHP文件");
                                    LogInfo($"  3. 运行'修复几何'工具尝试修复");
                                    LogInfo($"  4. 如果无法修复，可能需要重新数字化该要素");
                                }
                                catch (Exception deepEx)
                                {
                                    LogError($"深入诊断失败: {deepEx.Message}");
                                }
                            }
                        }
                        catch (Exception repairEx)
                        {
                            LogError($"几何修复尝试失败: {repairEx.Message}");
                        }
                    }
                }
                catch (Exception shapeEx)
                {
                    LogError($"要素 {featureIndex} 获取几何对象失败: {shapeEx.Message}");
                    LogError($"异常详情: {shapeEx}");
                    LogError($"异常类型: {shapeEx.GetType().Name}");

                    // 检查是否是几何异常
                    if (shapeEx.Message.Contains("geometry") || shapeEx.Message.Contains("Geometry"))
                    {
                        LogError($"这可能是几何数据损坏或无效的问题");
                        LogError($"建议：1. 检查SHP文件是否有自相交、扣洞等复杂几何");
                        LogError($"建议：2. 在ArcGIS Pro中运行'修复几何'工具");
                        LogError($"建议：3. 检查要素是否有空几何或无效拓扑");
                    }

                    return;
                }

                LogInfo($"要素 {featureIndex} 几何类型: {shape?.GeometryType}，是否为空: {shape?.IsEmpty}");

                if (shape == null)
                {
                    LogError($"要素 {featureIndex} 的几何对象为null");

                    // 尝试诊断SHP文件问题
                    try
                    {
                        var table = feature.GetTable();
                        var definition = table?.GetDefinition();
                        var shapeField = definition?.GetFields()?.FirstOrDefault(f => f.FieldType == FieldType.Geometry);

                        LogInfo($"SHP文件诊断信息:");
                        LogInfo($"  - 表名: {table?.GetName() ?? "未知"}");
                        LogInfo($"  - 几何字段: {shapeField?.Name ?? "未找到"}");
                        LogInfo($"  - 几何字段类型: {shapeField?.FieldType}");

                        if (definition != null)
                        {
                            LogInfo($"  - 定义的几何类型: {definition.GetShapeType()}");
                            LogInfo($"  - 空间参考: {definition.GetSpatialReference()?.Name ?? "未知"}");
                        }
                    }
                    catch (Exception diagEx)
                    {
                        LogError($"SHP文件诊断失败: {diagEx.Message}");
                    }

                    return;
                }

                if (!(shape is Polygon geometry))
                {
                    LogWarning($"要素 {featureIndex} 不是面类型，实际类型: {shape.GeometryType}");

                    // 详细分析几何类型
                    LogInfo($"几何对象详细信息:");
                    LogInfo($"  - 类型: {shape.GeometryType}");
                    LogInfo($"  - 维度: {shape.Dimension}");
                    LogInfo($"  - 是否为空: {shape.IsEmpty}");
                    LogInfo($"  - 空间参考: {shape.SpatialReference?.Name ?? "未知"}");

                    try
                    {
                        var envelope = shape.Extent;
                        LogInfo($"  - 边界框: X({envelope.XMin:F3}, {envelope.XMax:F3}), Y({envelope.YMin:F3}, {envelope.YMax:F3})");
                    }
                    catch (Exception envEx)
                    {
                        LogWarning($"  - 无法获取边界框: {envEx.Message}");
                    }

                    // 尝试处理其他几何类型
                    if (shape is Polyline polyline)
                    {
                        LogInfo($"要素是线类型，线段数: {polyline.PartCount}");
                        LogInfo($"是否可以转换为面？线是否闭合？");

                        // 检查线是否闭合
                        try
                        {
                            for (int partIndex = 0; partIndex < polyline.PartCount; partIndex++)
                            {
                                var part = polyline.Parts[partIndex];
                                if (part.Count > 0)
                                {
                                    var firstSegment = part.First();
                                    var lastSegment = part.Last();
                                    var isClosed = firstSegment.StartPoint.IsEqual(lastSegment.EndPoint);
                                    LogInfo($"  线段 {partIndex + 1}: {part.Count} 个段，是否闭合: {isClosed}");
                                }
                            }
                        }
                        catch (Exception lineEx)
                        {
                            LogError($"分析线要素失败: {lineEx.Message}");
                        }
                    }
                    else if (shape is MapPoint point)
                    {
                        LogInfo($"要素是点类型: X={point.X:F6}, Y={point.Y:F6}");
                    }
                    else if (shape is Multipoint multipoint)
                    {
                        LogInfo($"要素是多点类型，点数: {multipoint.PointCount}");
                    }
                    else
                    {
                        LogWarning($"未知的几何类型: {shape.GetType().Name}");
                    }

                    return;
                }

                // 详细的几何信息
                LogInfo($"要素 {featureIndex} 几何详细信息:");
                LogInfo($"  - 类型: {geometry.GeometryType}");
                LogInfo($"  - 环数: {geometry.PartCount}");
                LogInfo($"  - 是否为空: {geometry.IsEmpty}");
                LogInfo($"  - 是否有Z值: {geometry.HasZ}");
                LogInfo($"  - 是否有M值: {geometry.HasM}");
                LogInfo($"  - 空间参考: {geometry.SpatialReference?.Name ?? "未知"}");

                // 尝试获取边界框信息
                try
                {
                    var envelope = geometry.Extent;
                    LogInfo($"  - 边界框: X({envelope.XMin:F3}, {envelope.XMax:F3}), Y({envelope.YMin:F3}, {envelope.YMax:F3})");
                }
                catch (Exception envEx)
                {
                    LogWarning($"  - 无法获取边界框: {envEx.Message}");
                }

                // 获取字段值 - 按固定顺序：点数,地块面积,地块编号,地块名称,图形类型,图幅号,地块用途,地类编码
                var pointCount = 0;
                var fieldValues = new List<string>();

                // 1. 点数 - 稍后计算
                fieldValues.Add(""); // 点数占位符

                // 2. 地块面积
                var areaValue = GetFieldValueByMapping("地块面积", feature) ?? "0";
                fieldValues.Add(areaValue);

                // 3. 地块编号
                var numberValue = GetFieldValueByMapping("地块编号", feature) ?? featureIndex.ToString();
                fieldValues.Add(numberValue);

                // 4. 地块名称
                var nameValue = GetFieldValueByMapping("地块名称", feature) ?? "";
                fieldValues.Add(nameValue);

                // 5. 图形类型 - 固定为"面"
                fieldValues.Add("面");

                // 6. 图幅号
                var mapSheetValue = GetFieldValueByMapping("图幅号", feature) ?? "";
                fieldValues.Add(mapSheetValue);

                // 7. 地块用途
                var landUseValue = GetFieldValueByMapping("地块用途", feature) ?? "";
                fieldValues.Add(landUseValue);

                // 8. 地类编码
                var landCodeValue = GetFieldValueByMapping("地类编码", feature) ?? "";
                fieldValues.Add(landCodeValue);

                // 移除字段值日志，提升性能

                // 计算总点数和写入坐标（高性能版本）
                WriteFeatureCoordinatesFast(geometry, content, fieldValues, featureIndex);

                // 移除处理完成日志，提升性能
            }
            catch (Exception ex)
            {
                LogError($"处理要素 {featureIndex} 时出错: {ex.Message}");
                LogError($"异常详情: {ex}");
            }
        }

        /// <summary>
        /// 处理单个要素（异步版本，保留兼容性）
        /// </summary>
        private async Task ProcessFeature(Feature feature, StringBuilder content, int featureIndex)
        {
            try
            {
                // 移除详细日志，提升性能
                var shape = feature.GetShape();

                if (!(shape is Polygon geometry))
                {
                    if (shape != null)
                    {
                        LogWarning($"要素 {featureIndex} 不是面类型，实际类型: {shape.GeometryType}");
                    }
                    else
                    {
                        LogWarning($"要素 {featureIndex} 的几何对象为null");
                    }
                    return;
                }

                // 获取字段值 - 按固定顺序：点数,地块面积,地块编号,地块名称,图形类型,图幅号,地块用途,地类编码
                var pointCount = 0;
                var fieldValues = new List<string>();

                LogInfo($"要素 {featureIndex} 几何信息: 类型={geometry.GeometryType}, 环数={geometry.PartCount}");

                // 1. 点数 - 稍后计算
                fieldValues.Add(""); // 点数占位符

                // 2. 地块面积
                var areaValue = GetFieldValueByMapping("地块面积", feature) ?? "0";
                fieldValues.Add(areaValue);
                LogInfo($"地块面积: {areaValue}");

                // 3. 地块编号
                var numberValue = GetFieldValueByMapping("地块编号", feature) ?? featureIndex.ToString();
                fieldValues.Add(numberValue);
                LogInfo($"地块编号: {numberValue}");

                // 4. 地块名称
                var nameValue = GetFieldValueByMapping("地块名称", feature) ?? "";
                fieldValues.Add(nameValue);
                LogInfo($"地块名称: {nameValue}");

                // 5. 图形类型 - 固定为"面"
                fieldValues.Add("面");

                // 6. 图幅号
                var mapSheetValue = GetFieldValueByMapping("图幅号", feature) ?? "";
                fieldValues.Add(mapSheetValue);
                LogInfo($"图幅号: {mapSheetValue}");

                // 7. 地块用途
                var landUseValue = GetFieldValueByMapping("地块用途", feature) ?? "";
                fieldValues.Add(landUseValue);
                LogInfo($"地块用途: {landUseValue}");

                // 8. 地类编码
                var landCodeValue = GetFieldValueByMapping("地类编码", feature) ?? "";
                fieldValues.Add(landCodeValue);
                LogInfo($"地类编码: {landCodeValue}");

                // 计算总点数
                // 移除详细几何日志

                for (int partIndex = 0; partIndex < geometry.PartCount; partIndex++)
                {
                    var part = geometry.Parts[partIndex];
                    var partPoints = new List<MapPoint>();

                    LogInfo($"处理第 {partIndex + 1} 个环，段数: {part.Count}");

                    // 获取环的所有点
                    foreach (var segment in part)
                    {
                        partPoints.Add(segment.StartPoint);
                        // 对于最后一个段，也添加终点
                        if (segment == part.Last())
                        {
                            partPoints.Add(segment.EndPoint);
                        }
                    }

                    LogInfo($"第 {partIndex + 1} 个环提取到 {partPoints.Count} 个点");
                    pointCount += partPoints.Count;

                    if (OutputClosingPoint && partPoints.Count > 0)
                    {
                        pointCount++; // 闭合点
                    }
                }

                LogInfo($"要素 {featureIndex} 总点数: {pointCount}");

                // 更新点数
                fieldValues[0] = pointCount.ToString();

                // 写入地块信息行
                var infoLine = string.Join(",", fieldValues) + ",@";
                content.AppendLine(infoLine);
                LogInfo($"要素 {featureIndex} 信息行: {infoLine}");

                // 写入坐标点
                WriteCoordinatePointsSync(content, geometry);

                LogInfo($"要素 {featureIndex} 处理完成");
            }
            catch (Exception ex)
            {
                LogError($"处理要素 {featureIndex} 时出错: {ex.Message}");
                LogError($"异常详情: {ex}");
            }
        }

        /// <summary>
        /// 立即写入要素坐标和信息行
        /// </summary>
        private void WriteFeatureCoordinatesImmediately(Polygon geometry, StringBuilder content, List<string> fieldValues, int featureIndex)
        {
            var pointCount = 0;

            LogInfo($"开始计算要素 {featureIndex} 坐标点数，几何类型: {geometry.GeometryType}，环数: {geometry.PartCount}");

            // 计算总点数
            for (int partIndex = 0; partIndex < geometry.PartCount; partIndex++)
            {
                var part = geometry.Parts[partIndex];
                var partPoints = new List<MapPoint>();

                LogInfo($"处理第 {partIndex + 1} 个环，段数: {part.Count}");

                // 获取环的所有点
                foreach (var segment in part)
                {
                    partPoints.Add(segment.StartPoint);
                    // 对于最后一个段，也添加终点
                    if (segment == part.Last())
                    {
                        partPoints.Add(segment.EndPoint);
                    }
                }

                LogInfo($"第 {partIndex + 1} 个环提取到 {partPoints.Count} 个点");
                pointCount += partPoints.Count;

                if (OutputClosingPoint && partPoints.Count > 0)
                {
                    pointCount++; // 闭合点
                }
            }

            LogInfo($"要素 {featureIndex} 总点数: {pointCount}");

            // 更新点数（如果点数字段存在且启用）
            var enabledFields = OutputFields.Where(f => f.IsEnabled).ToList();
            var pointCountIndex = enabledFields.FindIndex(f => f.FieldName == "点数");
            if (pointCountIndex >= 0)
            {
                fieldValues[pointCountIndex] = pointCount.ToString();
            }

            // 写入地块信息行
            var infoLine = string.Join("", fieldValues);
            content.AppendLine(infoLine);
            LogInfo($"要素 {featureIndex} 信息行: {infoLine}");

            // 写入坐标点
            WriteCoordinatePointsImmediately(content, geometry);
        }

        /// <summary>
        /// 写入要素坐标和信息行（高性能版本，支持多环和所有选项）
        /// </summary>
        private void WriteFeatureCoordinatesFast(Polygon geometry, StringBuilder content, List<string> fieldValues, int featureIndex)
        {
            try
            {
                // 使用高性能的坐标提取方法，但保持环信息
                var allPointsWithRings = new List<(MapPoint point, int ringNumber, int pointIndex)>();
                int totalPoints = 0;
                int globalPointIndex = 1; // 全局点编号

                // 按环处理，保持环号信息
                int ringNumber = 1;
                foreach (var part in geometry.Parts)
                {
                    var partPoints = new List<MapPoint>();

                    // 高性能获取环的所有点
                    foreach (var segment in part)
                    {
                        partPoints.Add(segment.StartPoint);
                    }

                    // 添加最后一个点（如果需要）
                    if (partPoints.Count > 0)
                    {
                        var lastSegment = part.LastOrDefault();
                        if (lastSegment != null)
                        {
                            partPoints.Add(lastSegment.EndPoint);
                        }
                    }

                    // 处理闭合点
                    bool hasClosingPoint = false;

                    // 添加调试信息
                    if (featureIndex <= 3)
                    {
                        LogInfo($"闭合点处理: OutputClosingPoint={OutputClosingPoint}, partPoints.Count={partPoints.Count}");
                        if (partPoints.Count > 1)
                        {
                            var firstPoint = partPoints[0];
                            var lastPoint = partPoints[partPoints.Count - 1];
                            LogInfo($"第一个点: X={firstPoint.X:F6}, Y={firstPoint.Y:F6}");
                            LogInfo($"最后一个点: X={lastPoint.X:F6}, Y={lastPoint.Y:F6}");
                            LogInfo($"点是否相等: {firstPoint.IsEqual(lastPoint)}");
                        }
                    }

                    if (partPoints.Count > 1)
                    {
                        var firstPoint = partPoints[0];
                        var lastPoint = partPoints[partPoints.Count - 1];
                        bool isGeometryClosed = firstPoint.IsEqual(lastPoint);

                        if (OutputClosingPoint)
                        {
                            // 用户要求输出闭合点
                            if (!isGeometryClosed)
                            {
                                // 几何不闭合，添加第一个点作为闭合点
                                partPoints.Add(firstPoint);
                                hasClosingPoint = true;
                                if (featureIndex <= 3)
                                {
                                    LogInfo($"几何不闭合，添加闭合点，hasClosingPoint=true");
                                }
                            }
                            else
                            {
                                // 几何已闭合，标记最后一个点为闭合点
                                hasClosingPoint = true;
                                if (featureIndex <= 3)
                                {
                                    LogInfo($"几何已闭合，标记最后一个点为闭合点，hasClosingPoint=true");
                                }
                            }
                        }
                        else
                        {
                            // 用户不要求输出闭合点
                            if (isGeometryClosed)
                            {
                                // 几何已闭合，移除最后一个闭合点
                                partPoints.RemoveAt(partPoints.Count - 1);
                                if (featureIndex <= 3)
                                {
                                    LogInfo($"用户不要求闭合点，移除几何自带的闭合点，hasClosingPoint=false");
                                }
                            }
                            else
                            {
                                if (featureIndex <= 3)
                                {
                                    LogInfo($"几何不闭合且用户不要求闭合点，hasClosingPoint=false");
                                }
                            }
                            hasClosingPoint = false;
                        }
                    }
                    else
                    {
                        if (featureIndex <= 3)
                        {
                            LogInfo($"点数不足，hasClosingPoint=false");
                        }
                    }

                    // 添加到总列表，带环号和点编号
                    int localPointIndex = InnerRingStartFromOne && ringNumber > 1 ? 1 : globalPointIndex;
                    int ringStartPointIndex = localPointIndex; // 记录当前环的起点编号

                    // 添加调试信息
                    if (featureIndex <= 3)
                    {
                        LogInfo($"环 {ringNumber}: localPointIndex={localPointIndex}, ringStartPointIndex={ringStartPointIndex}, hasClosingPoint={hasClosingPoint}, ClosingPointContinueNumbering={ClosingPointContinueNumbering}");
                        LogInfo($"环 {ringNumber}: partPoints.Count={partPoints.Count} (包含几何原始点数)");
                    }

                    for (int i = 0; i < partPoints.Count; i++)
                    {
                        var point = partPoints[i];
                        bool isClosingPoint = hasClosingPoint && i == partPoints.Count - 1;

                        // 处理闭合点编号逻辑
                        int pointIndex;
                        if (featureIndex <= 3)
                        {
                            LogInfo($"点 {i+1}: isClosingPoint={isClosingPoint}, ClosingPointContinueNumbering={ClosingPointContinueNumbering}");
                            if (isClosingPoint)
                            {
                                LogInfo($"这是闭合点！ringStartPointIndex={ringStartPointIndex}, localPointIndex={localPointIndex}");
                            }
                        }

                        if (isClosingPoint && !ClosingPointContinueNumbering)
                        {
                            // 闭合点不续编，使用当前环的起点编号
                            pointIndex = ringStartPointIndex;
                            if (featureIndex <= 3)
                            {
                                LogInfo($"闭合点不续编: 使用起点编号 {ringStartPointIndex}");
                            }
                        }
                        else
                        {
                            pointIndex = localPointIndex;
                            if (featureIndex <= 3)
                            {
                                LogInfo($"非闭合点或闭合点续编: 使用编号 {pointIndex}, localPointIndex将从{localPointIndex}变为{localPointIndex+1}");
                                if (isClosingPoint)
                                {
                                    LogInfo($"这是闭合点但要续编，所以使用 {pointIndex}");
                                }
                            }
                            localPointIndex++;
                        }

                        allPointsWithRings.Add((point, ringNumber, pointIndex));

                        // 只有非闭合点或闭合点续编时才增加全局编号
                        if (!isClosingPoint || ClosingPointContinueNumbering)
                        {
                            globalPointIndex++;
                        }
                    }

                    totalPoints += partPoints.Count;
                    ringNumber++;
                }

                // 更新点数
                fieldValues[0] = totalPoints.ToString();

                // 写入信息行
                var infoLine = string.Join(",", fieldValues) + ",@";
                content.AppendLine(infoLine);

                // 批量写入坐标点（高性能版本，带环号和正确的点编号）
                WriteCoordinatePointsWithRingsAndIndexFast(content, allPointsWithRings);
            }
            catch (Exception ex)
            {
                // 如果高性能方法失败，回退到原方法
                WriteFeatureCoordinates(geometry, content, fieldValues, featureIndex);
            }
        }

        /// <summary>
        /// 写入要素坐标和信息行（原版本，作为备用）
        /// </summary>
        private void WriteFeatureCoordinates(Polygon geometry, StringBuilder content, List<string> fieldValues, int featureIndex)
        {
            var pointCount = 0;

            // 移除详细几何日志

            // 计算总点数
            for (int partIndex = 0; partIndex < geometry.PartCount; partIndex++)
            {
                var part = geometry.Parts[partIndex];
                var partPoints = new List<MapPoint>();

                LogInfo($"处理第 {partIndex + 1} 个环，段数: {part.Count}");

                // 获取环的所有点
                foreach (var segment in part)
                {
                    partPoints.Add(segment.StartPoint);
                    // 对于最后一个段，也添加终点
                    if (segment == part.Last())
                    {
                        partPoints.Add(segment.EndPoint);
                    }
                }

                LogInfo($"第 {partIndex + 1} 个环提取到 {partPoints.Count} 个点");
                pointCount += partPoints.Count;

                if (OutputClosingPoint && partPoints.Count > 0)
                {
                    pointCount++; // 闭合点
                }
            }

            LogInfo($"要素 {featureIndex} 总点数: {pointCount}");

            // 更新点数
            fieldValues[0] = pointCount.ToString();

            // 写入地块信息行
            var infoLine = string.Join(",", fieldValues) + ",@";
            content.AppendLine(infoLine);
            LogInfo($"要素 {featureIndex} 信息行: {infoLine}");

            // 写入坐标点
            WriteCoordinatePointsSync(content, geometry);
        }

        /// <summary>
        /// 高性能批量写入坐标点（带环号和正确点编号支持）
        /// </summary>
        private void WriteCoordinatePointsWithRingsAndIndexFast(StringBuilder content, List<(MapPoint point, int ringNumber, int pointIndex)> pointsWithRings)
        {
            // 使用StringBuilder批量构建坐标字符串，避免频繁的字符串拼接
            var coordBuilder = new StringBuilder(pointsWithRings.Count * 70); // 预分配容量

            for (int i = 0; i < pointsWithRings.Count; i++)
            {
                var (point, ringNumber, pointIndex) = pointsWithRings[i];

                // 直接格式化坐标，避免多次ToString调用
                var x = Math.Round(point.X, DecimalPlaces);
                var y = Math.Round(point.Y, DecimalPlaces);

                // 根据SwapXY设置决定坐标顺序
                var coord1 = SwapXY ? x : y;
                var coord2 = SwapXY ? y : x;

                // 格式：{Prefix}{点编号},{环号},{坐标1},{坐标2}
                coordBuilder.AppendLine($"{Prefix}{pointIndex},{ringNumber},{coord1.ToString($"F{DecimalPlaces}")},{coord2.ToString($"F{DecimalPlaces}")}");
            }

            // 一次性添加所有坐标到主content
            content.Append(coordBuilder);
        }

        /// <summary>
        /// 高性能批量写入坐标点（带环号支持，原版本）
        /// </summary>
        private void WriteCoordinatePointsWithRingsFast(StringBuilder content, List<(MapPoint point, int ringNumber)> pointsWithRings)
        {
            // 使用StringBuilder批量构建坐标字符串，避免频繁的字符串拼接
            var coordBuilder = new StringBuilder(pointsWithRings.Count * 60); // 预分配容量

            for (int i = 0; i < pointsWithRings.Count; i++)
            {
                var (point, ringNumber) = pointsWithRings[i];

                // 直接格式化坐标，避免多次ToString调用
                var x = Math.Round(point.X, DecimalPlaces);
                var y = Math.Round(point.Y, DecimalPlaces);

                // 根据SwapXY设置决定坐标顺序
                var coord1 = SwapXY ? x : y;
                var coord2 = SwapXY ? y : x;

                // 格式：{Prefix}{点编号},{环号},{坐标1},{坐标2}
                coordBuilder.AppendLine($"{Prefix}{i + 1},{ringNumber},{coord1.ToString($"F{DecimalPlaces}")},{coord2.ToString($"F{DecimalPlaces}")}");
            }

            // 一次性添加所有坐标到主content
            content.Append(coordBuilder);
        }

        /// <summary>
        /// 高性能批量写入坐标点（原版本，作为备用）
        /// </summary>
        private void WriteCoordinatePointsFast(StringBuilder content, List<MapPoint> points)
        {
            // 使用StringBuilder批量构建坐标字符串，避免频繁的字符串拼接
            var coordBuilder = new StringBuilder(points.Count * 50); // 预分配容量

            for (int i = 0; i < points.Count; i++)
            {
                var point = points[i];

                // 直接格式化坐标，避免多次ToString调用
                var x = Math.Round(point.X, DecimalPlaces);
                var y = Math.Round(point.Y, DecimalPlaces);

                // 使用固定格式避免文化相关的格式化问题
                coordBuilder.AppendLine($"{i + 1},{x.ToString($"F{DecimalPlaces}")},{y.ToString($"F{DecimalPlaces}")}");
            }

            // 一次性添加所有坐标到主content
            content.Append(coordBuilder);
        }

        /// <summary>
        /// 立即写入坐标点
        /// </summary>
        private void WriteCoordinatePointsImmediately(StringBuilder content, Polygon geometry)
        {
            int pointIndex = 1;

            LogInfo($"开始写入坐标点，环数: {geometry.PartCount}");

            // 遍历所有环（外环和内环）
            for (int partIndex = 0; partIndex < geometry.PartCount; partIndex++)
            {
                var part = geometry.Parts[partIndex];
                var partPoints = new List<MapPoint>();

                LogInfo($"处理第 {partIndex + 1} 个环");

                // 获取环的所有点
                foreach (var segment in part)
                {
                    partPoints.Add(segment.StartPoint);
                    // 对于最后一个段，也添加终点
                    if (segment == part.Last())
                    {
                        partPoints.Add(segment.EndPoint);
                    }
                }

                LogInfo($"第 {partIndex + 1} 个环提取到 {partPoints.Count} 个点");

                if (partPoints.Count > 0)
                {
                    // 确定环号和起始索引
                    int ringNumber = partIndex + 1;
                    int startIndex = (partIndex > 0 && InnerRingStartFromOne) ? 1 : pointIndex;

                    pointIndex = WriteRingPoints(content, partPoints, ringNumber, startIndex);
                    LogInfo($"第 {partIndex + 1} 个环坐标写入完成，下一个起始索引: {pointIndex}");
                }
                else
                {
                    LogWarning($"第 {partIndex + 1} 个环没有提取到坐标点");
                }
            }

            LogInfo("所有坐标点写入完成");
        }

        /// <summary>
        /// 写入坐标点（同步版本）
        /// </summary>
        private void WriteCoordinatePointsSync(StringBuilder content, Polygon geometry)
        {
            LogInfo($"开始写入坐标点，环数: {geometry.PartCount}");

            // 检查是否有多个环（包含内环）
            if (geometry.PartCount > 1)
            {
                LogInfo($"检测到多环多边形（包含内环），环数: {geometry.PartCount}");
                LogInfo("使用Parts遍历方法以正确处理所有环（外环和内环）");
                WriteCoordinatePointsByParts(content, geometry);
            }
            else
            {
                LogInfo("单环多边形，使用Points属性方法");
                try
                {
                    // 对于单环多边形，可以使用Points属性
                    var points = geometry.Points;
                    LogInfo($"使用Points属性提取到 {points.Count} 个坐标点");

                    if (points.Count > 0)
                    {
                        // 记录前几个坐标点的详细信息
                        for (int i = 0; i < Math.Min(3, points.Count); i++)
                        {
                            var point = points[i];
                            LogInfo($"MapPoint {i + 1}: X={point.X:F6}, Y={point.Y:F6}");
                        }

                        // 直接使用Points集合
                        WriteRingPoints(content, points, 1, 1);
                        LogInfo($"坐标写入完成，共写入 {points.Count} 个点");
                    }
                    else
                    {
                        // 如果Points为空，尝试通过Parts遍历
                        LogWarning("Points集合为空，尝试通过Parts遍历");
                        WriteCoordinatePointsByParts(content, geometry);
                    }
                }
                catch (Exception ex)
                {
                    LogError($"使用Points属性提取坐标失败: {ex.Message}");
                    LogInfo("尝试通过Parts遍历提取坐标");
                    WriteCoordinatePointsByParts(content, geometry);
                }
            }

            LogInfo("所有坐标点写入完成");
        }

        /// <summary>
        /// 通过Parts遍历获取坐标点（备用方法）
        /// </summary>
        private void WriteCoordinatePointsByParts(StringBuilder content, Polygon geometry)
        {
            int pointIndex = 1;

            LogInfo($"通过Parts遍历写入坐标点，环数: {geometry.PartCount}");

            // 遍历所有环（外环和内环）
            for (int partIndex = 0; partIndex < geometry.PartCount; partIndex++)
            {
                var part = geometry.Parts[partIndex];
                var partPoints = new List<MapPoint>();

                LogInfo($"处理第 {partIndex + 1} 个环，段数: {part.Count}");

                // 获取环的所有点
                foreach (var segment in part)
                {
                    partPoints.Add(segment.StartPoint);
                    // 对于最后一个段，也添加终点
                    if (segment == part.Last())
                    {
                        partPoints.Add(segment.EndPoint);
                    }
                }

                LogInfo($"第 {partIndex + 1} 个环提取到 {partPoints.Count} 个点");

                if (partPoints.Count > 0)
                {
                    // 确定环号和起始索引
                    int ringNumber = partIndex + 1;
                    int startIndex = (partIndex > 0 && InnerRingStartFromOne) ? 1 : pointIndex;

                    pointIndex = WriteRingPoints(content, partPoints, ringNumber, startIndex);
                    LogInfo($"第 {partIndex + 1} 个环坐标写入完成，下一个起始索引: {pointIndex}");
                }
                else
                {
                    LogWarning($"第 {partIndex + 1} 个环没有提取到坐标点");
                }
            }
        }

        /// <summary>
        /// 写入坐标点（异步版本，保留兼容性）
        /// </summary>
        private async Task WriteCoordinatePoints(StringBuilder content, Polygon geometry)
        {
            int pointIndex = 1;

            LogInfo($"开始写入坐标点，环数: {geometry.PartCount}");

            // 遍历所有环（外环和内环）
            for (int partIndex = 0; partIndex < geometry.PartCount; partIndex++)
            {
                var part = geometry.Parts[partIndex];
                var partPoints = new List<MapPoint>();

                LogInfo($"处理第 {partIndex + 1} 个环");

                // 获取环的所有点
                foreach (var segment in part)
                {
                    partPoints.Add(segment.StartPoint);
                    // 对于最后一个段，也添加终点
                    if (segment == part.Last())
                    {
                        partPoints.Add(segment.EndPoint);
                    }
                }

                LogInfo($"第 {partIndex + 1} 个环提取到 {partPoints.Count} 个点");

                if (partPoints.Count > 0)
                {
                    // 确定环号和起始索引
                    int ringNumber = partIndex + 1;
                    int startIndex = (partIndex > 0 && InnerRingStartFromOne) ? 1 : pointIndex;

                    pointIndex = WriteRingPoints(content, partPoints, ringNumber, startIndex);
                    LogInfo($"第 {partIndex + 1} 个环坐标写入完成，下一个起始索引: {pointIndex}");
                }
                else
                {
                    LogWarning($"第 {partIndex + 1} 个环没有提取到坐标点");
                }
            }

            LogInfo("所有坐标点写入完成");
        }

        /// <summary>
        /// 写入环的坐标点
        /// </summary>
        private int WriteRingPoints(StringBuilder content, IEnumerable<MapPoint> points, int ringNumber, int startIndex)
        {
            var pointList = points.ToList();
            int currentIndex = startIndex;

            // 只在第一个环或少量要素时输出详细日志
            bool isDetailedLogging = ringNumber == 1 && _processedFeatureCount < 5;

            if (isDetailedLogging)
            {
                LogInfo($"写入环 {ringNumber} 的坐标点，共 {pointList.Count} 个点，起始索引: {startIndex}", true);
            }

            if (pointList.Count == 0)
            {
                LogWarning($"环 {ringNumber} 没有坐标点可写入");
                return currentIndex;
            }

            // 只在详细日志模式下记录坐标信息
            if (isDetailedLogging)
            {
                for (int i = 0; i < Math.Min(2, pointList.Count); i++)
                {
                    var point = pointList[i];
                    LogInfo($"点 {i + 1} 原始坐标: X={point.X:F6}, Y={point.Y:F6}", true);
                }
            }

            for (int i = 0; i < pointList.Count; i++)
            {
                var point = pointList[i];

                // 检查坐标是否有效
                if (double.IsNaN(point.X) || double.IsNaN(point.Y))
                {
                    LogWarning($"点 {i + 1} 包含无效坐标: X={point.X}, Y={point.Y}");
                    continue;
                }

                var x = SwapXY ? point.Y : point.X;
                var y = SwapXY ? point.X : point.Y;

                var xStr = x.ToString($"F{DecimalPlaces}");
                var yStr = y.ToString($"F{DecimalPlaces}");

                var coordinateLine = $"{Prefix}{currentIndex},{ringNumber},{yStr},{xStr}";
                content.AppendLine(coordinateLine);

                // 只在详细日志模式下记录前2个点
                if (isDetailedLogging && i < 2)
                {
                    LogInfo($"写入坐标点 {currentIndex}: {coordinateLine}", true);
                }

                currentIndex++;
            }

            // 闭合点
            if (OutputClosingPoint && pointList.Count > 0)
            {
                var firstPoint = pointList[0];
                var x = SwapXY ? firstPoint.Y : firstPoint.X;
                var y = SwapXY ? firstPoint.X : firstPoint.Y;

                var xStr = x.ToString($"F{DecimalPlaces}");
                var yStr = y.ToString($"F{DecimalPlaces}");

                string closingLine;
                if (ClosingPointContinueNumbering)
                {
                    closingLine = $"{Prefix}{currentIndex},{ringNumber},{yStr},{xStr}";
                    content.AppendLine(closingLine);
                    currentIndex++;
                }
                else
                {
                    closingLine = $"{Prefix}{startIndex},{ringNumber},{yStr},{xStr}";
                    content.AppendLine(closingLine);
                }

                LogInfo($"写入闭合点: {closingLine}");
            }

            LogInfo($"环 {ringNumber} 坐标点写入完成，共写入 {pointList.Count} 个点，内容长度增加了约 {pointList.Count * 30} 字符");
            return currentIndex;
        }

        /// <summary>
        /// 根据字段映射获取字段值
        /// </summary>
        private string GetFieldValueByMapping(string mappingKey, Feature feature)
        {
            try
            {
                if (!_fieldMappings.ContainsKey(mappingKey))
                {
                    LogWarning($"字段映射中不存在键: {mappingKey}");
                    return "";
                }

                var possibleFieldNames = _fieldMappings[mappingKey];

                // 直接尝试从要素中获取字段值，而不是通过FeatureClass
                foreach (var fieldName in possibleFieldNames)
                {
                    try
                    {
                        var value = feature[fieldName];
                        if (value != null)
                        {
                            LogInfo($"字段匹配成功: {mappingKey} -> {fieldName} = {value}");
                            return value.ToString();
                        }
                    }
                    catch
                    {
                        // 字段不存在，继续尝试下一个
                        continue;
                    }
                }

                LogInfo($"字段匹配失败: {mappingKey} - 未找到匹配的字段");
                return "";
            }
            catch (Exception ex)
            {
                LogError($"获取字段值时出错 ({mappingKey}): {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取字段值
        /// </summary>
        private string GetFieldValue(Feature feature, string fieldName)
        {
            if (string.IsNullOrEmpty(fieldName)) return "";

            try
            {
                var value = feature[fieldName];
                return value?.ToString() ?? "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// 保存到文件
        /// </summary>
        private async Task SaveToFile(string content, string filePath)
        {
            try
            {
                LogInfo($"准备保存文件: {filePath}");
                LogInfo($"文件内容长度: {content.Length} 字符");
                LogInfo($"文件内容预览: {content.Substring(0, Math.Min(200, content.Length))}...");

                Encoding encoding = GetEncodingFromFormat(SelectedTextFormat);
                LogInfo($"使用文本格式: {SelectedTextFormat}, 编码: {encoding.EncodingName}");
                LogInfo($"编码详细信息: CodePage={encoding.CodePage}, BodyName={encoding.BodyName}");

                // 验证内容是否可以用选定编码正确编码
                try
                {
                    var testBytes = encoding.GetBytes(content);
                    var testString = encoding.GetString(testBytes);
                    if (testString != content)
                    {
                        LogError($"警告: 内容在 {SelectedTextFormat} 编码下可能存在字符丢失");
                    }
                    else
                    {
                        LogInfo($"编码验证通过: 内容可以正确编码为 {SelectedTextFormat}");
                    }
                }
                catch (Exception encEx)
                {
                    LogError($"编码验证失败: {encEx.Message}");
                }

                await File.WriteAllTextAsync(filePath, content, encoding);

                // 验证文件编码
                var savedBytes = await File.ReadAllBytesAsync(filePath);
                LogInfo($"文件保存后前10字节: {string.Join(" ", savedBytes.Take(10).Select(b => b.ToString("X2")))}");

                // 检测BOM和编码验证
                if (savedBytes.Length >= 3 && savedBytes[0] == 0xEF && savedBytes[1] == 0xBB && savedBytes[2] == 0xBF)
                {
                    LogInfo("检测到UTF-8 BOM - 文件编码正确");
                }
                else if (savedBytes.Length >= 2 && savedBytes[0] == 0xFF && savedBytes[1] == 0xFE)
                {
                    LogInfo("检测到UTF-16LE BOM - 文件编码正确");
                }
                else if (savedBytes.Length >= 2 && savedBytes[0] == 0xFE && savedBytes[1] == 0xFF)
                {
                    LogInfo("检测到UTF-16BE BOM - 文件编码正确");
                }
                else
                {
                    if (SelectedTextFormat?.ToUpper() == "UTF-8(无BOM)")
                    {
                        LogInfo("UTF-8无BOM编码 - 文件编码正确");
                    }
                    else if (SelectedTextFormat?.ToUpper() == "ANSI" || SelectedTextFormat?.ToUpper() == "GBK")
                    {
                        LogInfo($"{SelectedTextFormat}编码 - 文件编码正确");
                    }
                    else
                    {
                        LogInfo("未检测到BOM，请确认编码格式是否正确");
                    }
                }

                // 验证文件是否成功保存
                if (File.Exists(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    LogInfo($"文件保存成功: {filePath}, 大小: {fileInfo.Length} 字节");
                }
                else
                {
                    LogError($"文件保存失败: 文件不存在 {filePath}");
                }
            }
            catch (Exception ex)
            {
                LogError($"保存文件失败: {ex.Message}");
                LogError($"异常详情: {ex}");
            }
        }

        /// <summary>
        /// 记录信息日志（优化版本，减少UI更新频率）
        /// </summary>
        private void LogInfo(string message, bool forceUpdate = false)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var logMessage = $"[{timestamp}] {message}";

            // 对于大量数据处理，减少UI更新频率
            if (forceUpdate || _logUpdateCounter % 10 == 0)
            {
                if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == true)
                {
                    LogContent += logMessage + Environment.NewLine;
                }
                else
                {
                    System.Windows.Application.Current?.Dispatcher.BeginInvoke(() =>
                    {
                        LogContent += logMessage + Environment.NewLine;
                    });
                }
            }

            _logUpdateCounter++;
        }

        private int _logUpdateCounter = 0;

        /// <summary>
        /// 记录警告日志
        /// </summary>
        private void LogWarning(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var logMessage = $"[{timestamp}] 警告: {message}";

            if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == true)
            {
                LogContent += logMessage + Environment.NewLine;
            }
            else
            {
                System.Windows.Application.Current?.Dispatcher.BeginInvoke(() =>
                {
                    LogContent += logMessage + Environment.NewLine;
                });
            }
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        private void LogError(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var logMessage = $"[{timestamp}] 错误: {message}";

            if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == true)
            {
                LogContent += logMessage + Environment.NewLine;
            }
            else
            {
                System.Windows.Application.Current?.Dispatcher.BeginInvoke(() =>
                {
                    LogContent += logMessage + Environment.NewLine;
                });
            }
        }
    }

    /// <summary>
    /// RelayCommand实现
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public void Execute(object parameter)
        {
            _execute();
        }
    }

    /// <summary>
    /// 泛型RelayCommand实现
    /// </summary>
    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T> _execute;
        private readonly Func<T, bool> _canExecute;

        public RelayCommand(Action<T> execute, Func<T, bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object parameter)
        {
            return _canExecute?.Invoke((T)parameter) ?? true;
        }

        public void Execute(object parameter)
        {
            _execute((T)parameter);
        }
    }
}
