using System.Linq;
using System.Threading.Tasks;
using XIAOFUTools.Tools.AIAssistant.Models;
using XIAOFUTools.Tools.AIAssistant.Tools.Base;
using Newtonsoft.Json;

namespace XIAOFUTools.Tools.AIAssistant.Tools
{
    /// <summary>
    /// 地图信息函数
    /// 获取当前地图的详细信息，包括范围、坐标系、图层等
    /// </summary>
    public class MapInfoFunction : BaseOpenAIFunction
    {
        public override string Name => "get_map_info";

        public override string Description => "获取当前ArcGIS Pro地图的详细信息，包括地图名称、空间参考系、地图范围、图层列表、选中要素等信息。用于了解当前GIS工作环境。";

        protected override FunctionParameters GetParametersDefinition()
        {
            var parameters = new FunctionParameters();
            
            parameters.AddProperty("include_layers", 
                ParameterProperty.CreateBoolean("是否包含详细的图层信息", true));
            
            parameters.AddProperty("include_extent", 
                ParameterProperty.CreateBoolean("是否包含地图范围信息", true));
            
            parameters.AddProperty("include_selection", 
                ParameterProperty.CreateBoolean("是否包含选中要素信息", true));

            return parameters;
        }

        protected override async Task<FunctionResult> ExecuteInternalAsync(FunctionCall functionCall, GISContext context)
        {
            Log($"开始获取地图信息，参数: {functionCall.Arguments}");

            if (context == null)
            {
                return FunctionResult.CreateFailure(
                    functionCall.Id,
                    functionCall.Name,
                    "无法获取ArcGIS Pro上下文信息，请确保ArcGIS Pro正在运行且有活动地图"
                );
            }

            // 解析参数
            var parameters = ParseArguments<MapInfoParameters>(functionCall);

            // 构建地图信息
            var mapInfo = new
            {
                map_name = context.MapName ?? "无活动地图",
                spatial_reference = context.SpatialReference ?? "未知坐标系",
                scale = context.Scale,
                extent = parameters.IncludeExtent ? new
                {
                    x_min = context.CurrentExtent?.XMin,
                    y_min = context.CurrentExtent?.YMin,
                    x_max = context.CurrentExtent?.XMax,
                    y_max = context.CurrentExtent?.YMax
                } : null,
                layer_count = context.ActiveLayers?.Count ?? 0,
                layers = parameters.IncludeLayers ? context.ActiveLayers?.Select(l => new
                {
                    name = l.Name,
                    type = l.LayerType,
                    visible = l.IsVisible,
                    editable = l.IsEditable,
                    feature_count = l.FeatureCount,
                    data_source = l.DataSource
                }).ToArray() : null,
                selected_features = parameters.IncludeSelection ? new
                {
                    count = context.SelectedFeatureCount,
                    layer = context.SelectedLayerName
                } : null,
                project_info = new
                {
                    project_path = context.ProjectPath,
                    has_unsaved_edits = context.HasUnsavedEdits
                }
            };

            var resultJson = JsonConvert.SerializeObject(mapInfo, Formatting.Indented);
            var message = $"成功获取地图信息: {context.MapName}，包含 {context.ActiveLayers?.Count ?? 0} 个图层";

            return FunctionResult.CreateSuccessWithData(
                functionCall.Id,
                functionCall.Name,
                mapInfo,
                message
            );
        }

        /// <summary>
        /// 地图信息参数模型
        /// </summary>
        private class MapInfoParameters
        {
            [JsonProperty("include_layers")]
            public bool IncludeLayers { get; set; } = true;

            [JsonProperty("include_extent")]
            public bool IncludeExtent { get; set; } = true;

            [JsonProperty("include_selection")]
            public bool IncludeSelection { get; set; } = true;
        }
    }
}
