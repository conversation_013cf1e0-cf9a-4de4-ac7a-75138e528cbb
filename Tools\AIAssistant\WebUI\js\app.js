// AI助手主应用逻辑
class AIAssistantApp {
    constructor() {
        console.log('Initializing AIAssistantApp constructor...');

        // 获取DOM元素
        this.messagesArea = document.getElementById('messages-area');
        this.messageInput = document.getElementById('messageInput');
        this.sendBtn = document.getElementById('sendButton');
        this.stopBtn = document.getElementById('stopButton');

        // 详细的元素检查
        console.log('Element references:');
        console.log('- messagesArea:', this.messagesArea);
        console.log('- messageInput:', this.messageInput);
        console.log('- sendBtn:', this.sendBtn);
        console.log('- stopBtn:', this.stopBtn);

        // 检查关键元素是否存在
        if (!this.messagesArea) {
            console.error('CRITICAL: messages-area element not found');
            console.log('Available elements with id:', Array.from(document.querySelectorAll('[id]')).map(el => el.id));
            return;
        }

        if (!this.messageInput) {
            console.error('CRITICAL: messageInput element not found');
        }

        if (!this.sendBtn) {
            console.error('CRITICAL: sendButton element not found');
        }

        // 新界面元素
        this.historyToggle = document.getElementById('historyToggle');
        this.menuButton = document.getElementById('menuButton');
        this.dropdownMenu = document.getElementById('dropdownMenu');
        this.historyBar = document.getElementById('historyBar');
        this.attachBtn = document.getElementById('attachBtn');
        this.modeSwitch = document.getElementById('modeSwitch');

        // 详细的元素检查
        console.log('🔍 UI Elements check:');
        console.log('- historyToggle:', this.historyToggle);
        console.log('- menuButton:', this.menuButton);
        console.log('- dropdownMenu:', this.dropdownMenu);
        console.log('- historyBar:', this.historyBar);
        console.log('- attachBtn:', this.attachBtn);
        console.log('- modeSwitch:', this.modeSwitch);
        this.chatLabel = document.getElementById('chatLabel');
        this.agentLabel = document.getElementById('agentLabel');
        this.toolIndicator = document.getElementById('toolIndicator');
        this.statusIndicator = document.getElementById('statusIndicator');
        this.statusText = document.getElementById('statusText');

        // 状态变量
        this.isProcessing = false;
        this.currentStreamMessage = null;
        this.currentMode = 'chat'; // 'chat' 或 'agent'
        this.conversationHistory = [];
        this.toolBlockManager = null; // 将在DOM加载后初始化

        this.initializeEventListeners();
        this.initializeWebViewCommunication();
        this.setupAutoResize();
        
        // 等待工具块管理器初始化
        this.waitForToolBlockManager();
    }

    initializeEventListeners() {
        // 发送按钮点击事件
        if (this.sendBtn) {
            this.sendBtn.addEventListener('click', () => this.sendMessage());
        }
        if (this.stopBtn) {
            this.stopBtn.addEventListener('click', () => this.stopGeneration());
        }

        // 历史记录切换事件已通过 HTML 中的 onclick 处理，避免重复绑定
        // if (this.historyToggle) {
        //     this.historyToggle.addEventListener('click', () => this.toggleHistory());
        // }

        // 菜单按钮事件已通过 HTML 中的 onclick 处理，避免重复绑定
        // if (this.menuButton) {
        //     this.menuButton.addEventListener('click', (e) => {
        //         e.stopPropagation();
        //         this.toggleMenu();
        //     });
        // }

        // 附件按钮
        if (this.attachBtn) {
            this.attachBtn.addEventListener('click', () => this.handleAttachment());
        }

        // 模式切换
        if (this.modeSwitch) {
            this.modeSwitch.addEventListener('change', () => this.switchMode());
        }

        // 输入框事件
        if (this.messageInput) {
            this.messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            this.messageInput.addEventListener('input', () => {
                this.updateSendButtonState();
            });
        }

        // 点击空白区域关闭菜单
        document.addEventListener('click', (e) => {
            if (this.dropdownMenu && this.menuButton &&
                !this.dropdownMenu.contains(e.target) && !this.menuButton.contains(e.target)) {
                this.closeMenu();
            }
            if (this.historyBar && this.historyToggle &&
                !this.historyBar.contains(e.target) && !this.historyToggle.contains(e.target)) {
                this.closeHistory();
            }
        });

        // 初始化发送按钮状态
        this.updateSendButtonState();
    }

    initializeWebViewCommunication() {
        console.log('Initializing WebView communication...');

        // 检查WebView2是否可用
        if (typeof window.chrome === 'undefined' || typeof window.chrome.webview === 'undefined') {
            console.error('WebView2 not available - running in browser mode');
            // 在浏览器中模拟消息处理用于测试
            this.simulateBrowserMode();
            return;
        }

        // 监听来自C#的消息
        window.chrome.webview.addEventListener('message', (event) => {
            console.log('Received raw message from C#:', event.data);

            try {
                // 尝试解析JSON消息
                let messageData;
                if (typeof event.data === 'string') {
                    messageData = JSON.parse(event.data);
                } else {
                    messageData = event.data;
                }

                console.log('Parsed message:', messageData);
                this.handleCSharpMessage(messageData);
            } catch (error) {
                console.error('Failed to parse message from C#:', error, event.data);
            }
        });

        console.log('WebView communication initialized');
    }

    // 浏览器模式模拟（仅用于测试，不影响实际API调用）
    simulateBrowserMode() {
        console.log('Running in browser simulation mode - 仅用于测试前端逻辑');
        console.log('实际使用时将通过WebView2与C#后端通信');

        // 在浏览器模式下，sendToCS方法不会被实际调用
        // 因为没有WebView2环境，这里只是为了避免JavaScript错误
        this.sendToCS = (type, data) => {
            console.log('Browser mode - sendToCS called but not executed:', type, data);
            console.log('请在ArcGIS Pro环境中测试真实的API流式输出');
        };
    }

    handleCSharpMessage(data) {
        console.log('收到C#消息:', data);
        
        switch (data.type) {
            case 'messageStart':
                this.handleMessageStart(data.data);
                break;
            case 'messageStream':
                this.handleMessageStream(data.data);
                break;
            case 'messageComplete':
                this.handleMessageComplete(data.data);
                break;
            case 'conversationCleared':
                this.handleConversationCleared();
                break;
            case 'conversationHistory':
                this.handleConversationHistory(data.data);
                break;
            case 'error':
                this.handleError(data.data);
                break;
        }
    }

    sendMessage() {
        console.log('sendMessage called');

        if (!this.messageInput) {
            console.error('Cannot send message: messageInput is null');
            return;
        }

        const message = this.messageInput.value.trim();
        console.log('Message to send:', message);

        if (!message || this.isProcessing) {
            console.log('Message empty or already processing, aborting');
            return;
        }

        // 显示用户消息
        console.log('Adding user message...');
        this.addUserMessage(message);

        // 添加到对话历史
        this.conversationHistory.push({
            role: 'user',
            content: message,
            timestamp: new Date().toISOString()
        });

        // 清空输入框
        this.messageInput.value = '';
        this.updateSendButtonState();

        // 发送消息到C#，包含模式信息
        console.log('Sending message to C#...');
        this.sendToCS('sendMessage', {
            message,
            mode: this.currentMode,
            conversationHistory: this.conversationHistory
        });
    }

    clearConversation() {
        if (confirm('确定要清除当前对话记录吗？')) {
            this.sendToCS('clearConversation', {});
        }
    }

    newConversation() {
        if (this.conversationHistory.length > 0) {
            if (confirm('确定要开始新的对话吗？当前对话将被保存到历史记录。')) {
                this.saveCurrentConversation();
                this.clearCurrentConversation();
            }
        }
    }

    // 模式切换
    switchMode() {
        const isAgent = this.modeSwitch.checked;
        this.currentMode = isAgent ? 'agent' : 'chat';

        // 更新标签状态
        this.chatLabel.classList.toggle('active', !isAgent);
        this.agentLabel.classList.toggle('active', isAgent);

        // 更新输入框提示
        if (this.currentMode === 'chat') {
            this.messageInput.placeholder = '请输入您的GIS问题...';
        } else {
            this.messageInput.placeholder = '请描述您需要完成的GIS任务...';
        }

        console.log(`切换到${this.currentMode}模式`);
    }

    // 设置自动调整大小
    setupAutoResize() {
        const textarea = this.messageInput;

        const autoResize = () => {
            textarea.style.height = 'auto';
            const newHeight = Math.min(textarea.scrollHeight, 120);
            textarea.style.height = newHeight + 'px';
        };

        textarea.addEventListener('input', autoResize);
        textarea.addEventListener('paste', () => {
            setTimeout(autoResize, 0);
        });

        // 初始调整
        autoResize();
    }

    // 历史记录控制
    toggleHistory() {
        if (!this.historyBar) {
            console.error('History bar element not found');
            return;
        }

        console.log('🔄 Toggling history bar...');
        console.log('Before toggle - has show class:', this.historyBar.classList.contains('show'));

        this.historyBar.classList.toggle('show');

        console.log('After toggle - has show class:', this.historyBar.classList.contains('show'));
        console.log('History bar display style:', window.getComputedStyle(this.historyBar).display);
        console.log('History bar z-index:', window.getComputedStyle(this.historyBar).zIndex);

        if (this.historyBar.classList.contains('show')) {
            console.log('📋 Loading conversation history...');
            this.loadConversationHistory();
        }
    }

    closeHistory() {
        if (this.historyBar) {
            this.historyBar.classList.remove('show');
        }
    }

    // 菜单控制
    toggleMenu() {
        if (!this.dropdownMenu) {
            console.error('Dropdown menu element not found');
            return;
        }

        console.log('🔄 Toggling dropdown menu...');
        console.log('Before toggle - has show class:', this.dropdownMenu.classList.contains('show'));

        this.dropdownMenu.classList.toggle('show');

        console.log('After toggle - has show class:', this.dropdownMenu.classList.contains('show'));
        console.log('Menu display style:', window.getComputedStyle(this.dropdownMenu).display);
        console.log('Menu z-index:', window.getComputedStyle(this.dropdownMenu).zIndex);
    }

    closeMenu() {
        if (this.dropdownMenu) {
            this.dropdownMenu.classList.remove('show');
        }
    }

    // 停止生成
    stopGeneration() {
        this.isProcessing = false;
        this.showSendButton();
        this.hideToolIndicator();
        this.updateStatus('已停止', '🟡');
        // 发送停止信号到后端
        this.sendToCS('stopGeneration', {});
    }

    // 显示/隐藏按钮
    showSendButton() {
        this.sendBtn.style.display = 'flex';
        this.stopBtn.style.display = 'none';
    }

    showStopButton() {
        this.sendBtn.style.display = 'none';
        this.stopBtn.style.display = 'flex';
    }

    // 工具指示器
    showToolIndicator(text = '正在调用工具...') {
        document.getElementById('toolIndicatorText').textContent = text;
        this.toolIndicator.classList.add('show');
    }

    hideToolIndicator() {
        this.toolIndicator.classList.remove('show');
    }

    // 等待工具块管理器初始化
    waitForToolBlockManager() {
        const checkManager = () => {
            if (window.toolBlockManager) {
                this.toolBlockManager = window.toolBlockManager;
                console.log('Tool block manager connected to main app');
            } else {
                setTimeout(checkManager, 100);
            }
        };
        checkManager();
    }

    // 创建工具调用块
    createToolCall(toolName, params = {}) {
        if (!this.toolBlockManager) {
            console.warn('Tool block manager not initialized');
            return null;
        }
        
        return this.toolBlockManager.createToolBlock(toolName, params);
    }

    // 开始工具执行
    startToolCall(toolId) {
        if (!this.toolBlockManager) return;
        this.toolBlockManager.startToolExecution(toolId);
    }

    // 完成工具执行
    completeToolCall(toolId, result, success = true) {
        if (!this.toolBlockManager) return;
        this.toolBlockManager.completeToolExecution(toolId, result, success);
    }

    // 状态更新
    updateStatus(text, indicator = '🟢') {
        this.statusText.textContent = text;
        this.statusIndicator.textContent = indicator;
    }

    // 附件处理
    handleAttachment() {
        // 创建文件输入元素
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.txt,.pdf,.doc,.docx,.shp,.gdb';
        fileInput.multiple = true;

        fileInput.onchange = (e) => {
            const files = Array.from(e.target.files);
            this.processAttachments(files);
        };

        fileInput.click();
    }

    processAttachments(files) {
        console.log('处理附件:', files);
        // TODO: 实现附件处理逻辑
        alert(`选择了 ${files.length} 个文件，附件功能开发中...`);
    }

    sendToCS(type, data) {
        const message = {
            type,
            data,
            timestamp: new Date().toISOString()
        };

        console.log('Sending to C#:', message);

        if (typeof window.chrome !== 'undefined' && window.chrome.webview) {
            window.chrome.webview.postMessage(JSON.stringify(message));
        } else {
            console.warn('WebView2 not available, message not sent');
        }
    }

    handleMessageStart(data) {
        console.log('Message start:', data);
        this.isProcessing = true;
        this.showStopButton();
        this.updateStatus('正在思考...', '🟡');

        // 创建AI消息容器
        this.currentStreamMessage = this.createAssistantMessage('');

        // 确保流式消息容器创建成功
        if (!this.currentStreamMessage) {
            console.error('Failed to create stream message container');
            this.handleError({ message: '无法创建消息容器' });
            return;
        }

        console.log('Stream message container created successfully');
    }

    handleMessageStream(data) {
        console.log('Handling message stream chunk:', data.content);

        if (!this.currentStreamMessage) {
            console.error('No current stream message container');
            return;
        }

        if (!data.content) {
            console.warn('No content in stream data');
            return;
        }

        // 直接使用我们的流式内容追加方法
        this.appendStreamContent(this.currentStreamMessage, data.content);

        console.log('Stream content appended, current length:',
            (this.currentStreamMessage.getAttribute('data-raw-content') || '').length);
    }

    // 直接的流式内容追加方法
    appendStreamContent(targetElement, newContent) {
        if (!targetElement || !newContent) {
            console.warn('appendStreamContent: invalid parameters', targetElement, newContent);
            return;
        }

        // 获取当前内容
        let currentContent = targetElement.getAttribute('data-raw-content') || '';

        // 追加新内容
        currentContent += newContent;

        // 保存原始内容
        targetElement.setAttribute('data-raw-content', currentContent);

        console.log('Appending content:', newContent, 'Total length:', currentContent.length);

        // 渲染内容 - 先尝试简单文本，避免Markdown渲染延迟
        try {
            if (typeof MarkdownRenderer !== 'undefined') {
                targetElement.innerHTML = MarkdownRenderer.render(currentContent);
            } else {
                // 简单的文本渲染，保持换行
                targetElement.innerHTML = this.escapeHtml(currentContent).replace(/\n/g, '<br>');
            }
        } catch (error) {
            console.error('Error rendering content:', error);
            // 降级到纯文本
            targetElement.textContent = currentContent;
        }

        // 滚动到底部
        this.scrollToBottom();
    }

    handleMessageComplete(data) {
        console.log('Message complete', data);
        this.isProcessing = false;
        this.showSendButton();
        this.updateStatus('在线', '🟢');
        this.hideToolIndicator();

        // 如果没有流式消息但有最终内容，创建消息显示
        if (data && data.content && !this.currentStreamMessage) {
            console.log('Creating message from final content:', data.content);
            this.currentStreamMessage = this.createAssistantMessage('');
            this.appendStreamContent(this.currentStreamMessage, data.content);
        }

        // 如果有当前流消息，添加到历史记录
        if (this.currentStreamMessage) {
            const content = this.currentStreamMessage.getAttribute('data-raw-content') || '';
            if (content) {
                this.conversationHistory.push({
                    role: 'assistant',
                    content: content,
                    timestamp: new Date().toISOString()
                });
                console.log('Added to conversation history, total messages:', this.conversationHistory.length);
            }
        } else if (data && data.content) {
            // 如果没有流消息但有最终内容，直接添加到历史记录
            this.conversationHistory.push({
                role: 'assistant',
                content: data.content,
                timestamp: new Date().toISOString()
            });
            console.log('Added final content to conversation history:', data.content);
        }

        this.currentStreamMessage = null;

        // 滚动到底部
        this.scrollToBottom();
    }

    handleConversationCleared() {
        // 清除所有消息，保留欢迎消息
        const welcomeMessage = document.getElementById('welcome-message');
        this.messagesArea.innerHTML = '';
        if (welcomeMessage) {
            this.messagesArea.appendChild(welcomeMessage.cloneNode(true));
        }
    }

    handleConversationHistory(data) {
        // 处理对话历史（如果需要）
        console.log('对话历史:', data.history);
    }

    handleError(data) {
        console.error('Handling error:', data);

        this.isProcessing = false;
        this.showSendButton();
        this.updateStatus('错误', '🔴');
        this.hideToolIndicator();

        // 显示错误消息
        this.addAssistantMessage(`❌ ${data.message}`);
    }

    addUserMessage(content) {
        if (!this.messagesArea) {
            console.error('messagesArea is null, cannot add user message');
            return;
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = 'message user-message';
        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="message-text">${this.escapeHtml(content)}</div>
            </div>
        `;

        this.messagesArea.appendChild(messageDiv);
        this.scrollToBottom();
    }

    addAssistantMessage(content) {
        const messageDiv = this.createAssistantMessage(content);
        return messageDiv;
    }

    addAssistantMessage(content) {
        const messageDiv = this.createAssistantMessage(content);
        return messageDiv;
    }

    createAssistantMessage(content) {
        if (!this.messagesArea) {
            console.error('messagesArea is null, cannot create assistant message');
            return null;
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant-message';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        const textDiv = document.createElement('div');
        textDiv.className = 'message-text';

        if (content) {
            if (typeof MarkdownRenderer !== 'undefined') {
                textDiv.innerHTML = MarkdownRenderer.render(content);
            } else {
                textDiv.innerHTML = this.escapeHtml(content).replace(/\n/g, '<br>');
            }
        }

        contentDiv.appendChild(textDiv);
        messageDiv.appendChild(contentDiv);
        this.messagesArea.appendChild(messageDiv);

        this.scrollToBottom();
        return textDiv;
    }



    updateSendButtonState() {
        if (!this.messageInput || !this.sendBtn) {
            return;
        }
        const hasText = this.messageInput.value.trim().length > 0;
        this.sendBtn.disabled = !hasText || this.isProcessing;
    }

    autoResizeTextarea() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    scrollToBottom() {
        if (!this.messagesArea) {
            return;
        }
        setTimeout(() => {
            this.messagesArea.scrollTop = this.messagesArea.scrollHeight;
        }, 10);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 历史记录管理
    saveCurrentConversation() {
        if (this.conversationHistory.length === 0) return;

        const conversation = {
            id: Date.now().toString(),
            title: this.generateConversationTitle(),
            messages: [...this.conversationHistory],
            timestamp: new Date().toISOString(),
            mode: this.currentMode
        };

        const savedConversations = this.getSavedConversations();
        savedConversations.unshift(conversation);

        // 限制保存的对话数量
        if (savedConversations.length > 50) {
            savedConversations.splice(50);
        }

        localStorage.setItem('ai_conversations', JSON.stringify(savedConversations));
    }

    getSavedConversations() {
        try {
            return JSON.parse(localStorage.getItem('ai_conversations') || '[]');
        } catch {
            return [];
        }
    }

    generateConversationTitle() {
        const firstUserMessage = this.conversationHistory.find(msg => msg.role === 'user');
        if (firstUserMessage) {
            return firstUserMessage.content.substring(0, 30) + (firstUserMessage.content.length > 30 ? '...' : '');
        }
        return '新对话';
    }

    loadConversationHistory() {
        const historyList = document.getElementById('history-list');

        // 检查元素是否存在
        if (!historyList) {
            console.error('History list element not found');
            return;
        }

        const conversations = this.getSavedConversations();

        if (conversations.length === 0) {
            historyList.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">暂无历史记录</div>';
            return;
        }

        historyList.innerHTML = conversations.map(conv => `
            <div class="history-item" data-id="${conv.id}">
                <div class="history-item-title">${conv.title}</div>
                <div class="history-item-time">${new Date(conv.timestamp).toLocaleString()}</div>
            </div>
        `).join('');

        // 添加点击事件
        historyList.querySelectorAll('.history-item').forEach(item => {
            item.addEventListener('click', () => {
                const convId = item.dataset.id;
                this.loadConversation(convId);
            });
        });
    }

    loadConversation(conversationId) {
        const conversations = this.getSavedConversations();
        const conversation = conversations.find(conv => conv.id === conversationId);

        if (conversation) {
            this.clearCurrentConversation();
            this.conversationHistory = [...conversation.messages];
            this.currentMode = conversation.mode || 'chat';
            this.switchMode(this.currentMode);

            // 重新显示消息
            this.displayConversationMessages(conversation.messages);
            this.closeHistorySidebar();
        }
    }

    displayConversationMessages(messages) {
        // 清除当前消息（保留欢迎消息）
        const welcomeMessage = document.getElementById('welcome-message');
        this.messagesArea.innerHTML = '';
        if (welcomeMessage) {
            this.messagesArea.appendChild(welcomeMessage.cloneNode(true));
        }

        // 显示历史消息
        messages.forEach(msg => {
            if (msg.role === 'user') {
                this.addUserMessage(msg.content);
            } else if (msg.role === 'assistant') {
                this.addAssistantMessage(msg.content);
            }
        });
    }

    clearCurrentConversation() {
        this.conversationHistory = [];
        const welcomeMessage = document.getElementById('welcome-message');
        this.messagesArea.innerHTML = '';
        if (welcomeMessage) {
            this.messagesArea.appendChild(welcomeMessage.cloneNode(true));
        }
    }

    // 设置管理
    initializeSettings() {
        const temperatureSlider = document.getElementById('temperature-slider');
        const temperatureValue = document.getElementById('temperature-value');

        if (temperatureSlider && temperatureValue) {
            temperatureSlider.addEventListener('input', (e) => {
                temperatureValue.textContent = e.target.value;
            });
        }
    }
}

// 测试流式输出的全局函数
function testStreamOutput() {
    if (window.aiApp) {
        console.log('Testing stream output...');

        // 模拟流式输出测试
        window.aiApp.handleMessageStart({});

        const testMessage = "这是一个流式输出测试消息，用来验证每个字符是否能够实时显示。";
        const chunks = testMessage.split('');

        chunks.forEach((chunk, index) => {
            setTimeout(() => {
                console.log(`Test chunk ${index + 1}/${chunks.length}: "${chunk}"`);
                window.aiApp.handleMessageStream({ content: chunk });
            }, index * 100); // 100ms间隔
        });

        setTimeout(() => {
            console.log('Test stream complete');
            window.aiApp.handleMessageComplete(null);
        }, chunks.length * 100 + 500);
    }
}

// 全局函数，供HTML onclick使用
function handleHistoryClick() {
    console.log('🔍 History button clicked!');
    if (window.aiApp) {
        console.log('✅ AI App found, calling toggleHistory');
        window.aiApp.toggleHistory();
    } else {
        console.error('❌ AI App not found');
    }
}

function handleMenuClick() {
    console.log('📋 Menu button clicked!');
    if (window.aiApp) {
        console.log('✅ AI App found, calling toggleMenu');
        window.aiApp.toggleMenu();
    } else {
        console.error('❌ AI App not found');
    }
}

function handleNewChatClick() {
    console.log('🆕 New chat button clicked!');
    if (window.aiApp) {
        console.log('✅ AI App found, starting new conversation');
        window.aiApp.closeMenu();
        window.aiApp.newConversation();
    } else {
        console.error('❌ AI App not found');
    }
}

function handleClearChatClick() {
    console.log('🗑️ Clear chat button clicked!');
    if (window.aiApp) {
        console.log('✅ AI App found, clearing conversation');
        window.aiApp.closeMenu();
        window.aiApp.clearConversation();
    } else {
        console.error('❌ AI App not found');
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing AI Assistant App...');

    // 检查关键元素是否存在
    const messagesArea = document.getElementById('messages-area');
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');

    console.log('Elements check:');
    console.log('messages-area:', messagesArea);
    console.log('messageInput:', messageInput);
    console.log('sendButton:', sendButton);

    if (!messagesArea) {
        console.error('Critical: messages-area element not found in DOM');
        return;
    }

    window.aiApp = new AIAssistantApp();
    console.log('AI Assistant App initialized successfully');
});

// 调试函数 - 手动测试UI元素
function debugUI() {
    console.log('🔧 Debug UI Elements:');

    const historyBar = document.getElementById('historyBar');
    const dropdownMenu = document.getElementById('dropdownMenu');

    console.log('historyBar element:', historyBar);
    console.log('dropdownMenu element:', dropdownMenu);

    if (historyBar) {
        console.log('historyBar classes:', historyBar.className);
        console.log('historyBar computed display:', window.getComputedStyle(historyBar).display);
        console.log('historyBar computed z-index:', window.getComputedStyle(historyBar).zIndex);

        // 强制显示历史记录面板
        historyBar.classList.add('show');
        historyBar.style.backgroundColor = 'yellow';
        historyBar.style.border = '3px solid red';
        console.log('✅ 强制显示历史记录面板（黄色背景，红色边框）');
    }

    if (dropdownMenu) {
        console.log('dropdownMenu classes:', dropdownMenu.className);
        console.log('dropdownMenu computed display:', window.getComputedStyle(dropdownMenu).display);
        console.log('dropdownMenu computed z-index:', window.getComputedStyle(dropdownMenu).zIndex);

        // 强制显示下拉菜单
        dropdownMenu.classList.add('show');
        dropdownMenu.style.backgroundColor = 'lightblue';
        dropdownMenu.style.border = '3px solid blue';
        console.log('✅ 强制显示下拉菜单（蓝色背景，蓝色边框）');
    }
}

// 工具块演示函数
function demoToolBlocks() {
    if (window.aiApp && window.aiApp.toolBlockManager) {
        window.aiApp.demoToolBlocks();
    } else {
        console.warn('AI App or Tool Block Manager not ready');
    }
}

// 挂载到全局
window.debugUI = debugUI;
window.demoToolBlocks = demoToolBlocks;
