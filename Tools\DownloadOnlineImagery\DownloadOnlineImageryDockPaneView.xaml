<UserControl x:Class="XIAOFUTools.Tools.DownloadOnlineImagery.DownloadOnlineImageryDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:extensions="clr-namespace:ArcGIS.Desktop.Extensions;assembly=ArcGIS.Desktop.Extensions"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.DownloadOnlineImagery"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="400"
             Loaded="UserControl_Loaded">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Margin="8">
        <Grid.RowDefinitions>
            <!-- 要素图层选择 -->
            <RowDefinition Height="Auto"/>
            <!-- 输出文件夹 -->
            <RowDefinition Height="Auto"/>
            <!-- 下载级别和合并选项 -->
            <RowDefinition Height="Auto"/>
            <!-- 日志输出 -->
            <RowDefinition Height="*"/>
            <!-- 进度条 -->
            <RowDefinition Height="Auto"/>
            <!-- 底部按钮区域 (帮助、停止、开始下载) -->
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 要素图层选择 -->
        <Grid Grid.Row="0" Margin="0,0,0,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="70"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="要素图层:" VerticalAlignment="Center"/>
            <ComboBox Grid.Column="1"
                      ItemsSource="{Binding FeatureLayers}"
                      SelectedItem="{Binding SelectedFeatureLayer}"
                      DisplayMemberPath="Name"
                      Style="{StaticResource ComboBoxStyle}" Height="20"
                      ToolTip="选择用于确定下载范围的要素图层"/>
            <Button Grid.Column="2" Width="22" Height="20" Margin="5,0,0,0"
                    Style="{StaticResource DefaultButtonStyle}"
                    Command="{Binding RefreshLayersCommand}"
                    ToolTip="刷新图层列表"
                    VerticalAlignment="Center">
                <TextBlock Text="⟲" FontSize="12" FontWeight="Bold"
                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Button>
        </Grid>

        <!-- 输出文件夹 -->
        <Grid Grid.Row="1" Margin="0,0,0,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="70"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="45"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="输出文件夹:" VerticalAlignment="Center"/>
            <TextBox Grid.Column="1" Text="{Binding OutputFolder}"
                     Style="{StaticResource TextBoxStyle}" Height="20" Margin="0,0,3,0"
                     ToolTip="选择影像输出文件夹"/>
            <Button Grid.Column="2" Content="浏览" Height="20" Margin="0" Width="45"
                    Command="{Binding BrowseOutputFolderCommand}"
                    Style="{StaticResource DefaultButtonStyle}"
                    ToolTip="浏览选择输出文件夹"/>
        </Grid>

        <!-- 下载级别和合并选项 -->
        <Grid Grid.Row="2" Margin="0,0,0,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="70"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="下载级别:" VerticalAlignment="Center"/>
            <ComboBox Grid.Column="1"
                      ItemsSource="{Binding DownloadLevels}"
                      SelectedItem="{Binding SelectedDownloadLevel}"
                      Style="{StaticResource ComboBoxStyle}" Height="20"
                      ToolTip="选择影像下载级别（9-21级）"/>
            <CheckBox Grid.Column="2" Content="合并影像" IsChecked="{Binding MergeImages}"
                      Style="{StaticResource CheckBoxStyle}" Margin="10,0,0,0"
                      ToolTip="是否将下载的影像合并为单个文件"/>
        </Grid>

        <!-- 日志输出 -->
        <Grid Grid.Row="3" Margin="0,0,0,5">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Text="执行日志:" Margin="0,0,0,3"/>
            <Border Grid.Row="1" BorderBrush="#CDCDCD" BorderThickness="1">
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                    <TextBox Text="{Binding LogMessages}"
                             IsReadOnly="True"
                             TextWrapping="Wrap"
                             Background="White"
                             BorderThickness="0"
                             FontFamily="Consolas"
                             FontSize="11"
                             Padding="5"/>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- 进度条 -->
        <Grid Grid.Row="4" Margin="0,3,0,3">
            <ProgressBar Style="{StaticResource ProgressBarStyle}"
                        Height="8"
                        Value="{Binding Progress}"
                        Minimum="0" Maximum="100"
                        IsIndeterminate="{Binding IsProgressIndeterminate}"/>
        </Grid>

        <!-- 底部按钮区域 -->
        <Grid Grid.Row="5" Margin="0,0,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 帮助按钮 -->
            <Button Grid.Column="0" Content="?"
                    Style="{StaticResource HelpButtonStyle}"
                    Command="{Binding ShowHelpCommand}"
                    ToolTip="显示帮助信息"/>

            <!-- 空白区域 -->
            <Grid Grid.Column="1"/>

            <!-- 停止按钮 -->
            <Button Grid.Column="2" Content="停止" Command="{Binding StopDownloadCommand}"
                    Style="{StaticResource CancelButtonStyle}"
                    IsEnabled="{Binding IsProcessing}"
                    Background="#FFE74C3C" Foreground="White" Margin="0,0,4,0"/>

            <!-- 开始下载按钮 -->
            <Button Grid.Column="3" Content="开始下载" Command="{Binding StartDownloadCommand}"
                    Style="{StaticResource ExecuteButtonStyle}"
                    IsEnabled="{Binding CanProcess}" Width="80"/>
        </Grid>
    </Grid>
</UserControl>
