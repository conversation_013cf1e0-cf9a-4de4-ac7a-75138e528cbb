// Markdown渲染器
class MarkdownRenderer {
    static initialize() {
        // 配置marked选项
        if (typeof marked !== 'undefined') {
            marked.setOptions({
                highlight: function(code, lang) {
                    if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                        try {
                            return hljs.highlight(code, { language: lang }).value;
                        } catch (err) {
                            console.warn('代码高亮失败:', err);
                        }
                    }
                    return code;
                },
                breaks: true,
                gfm: true
            });
        }
    }

    static render(content) {
        if (typeof marked === 'undefined') {
            // 如果marked未加载，使用简单的文本处理
            return this.simpleTextProcess(content);
        }

        try {
            // 使用marked渲染Markdown
            let html = marked.parse(content);
            
            // 后处理：添加代码复制按钮
            html = this.addCodeCopyButtons(html);
            
            return html;
        } catch (error) {
            console.error('Markdown渲染失败:', error);
            return this.simpleTextProcess(content);
        }
    }

    static simpleTextProcess(content) {
        // 简单的文本处理，用于fallback
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>');
    }

    static addCodeCopyButtons(html) {
        // 为代码块添加复制按钮
        return html.replace(
            /<pre><code([^>]*)>([\s\S]*?)<\/code><\/pre>/g,
            (match, attrs, code) => {
                const copyId = 'copy-' + Math.random().toString(36).substr(2, 9);
                return `
                    <div class="code-block-container">
                        <div class="code-block-header">
                            <button class="copy-code-btn" onclick="MarkdownRenderer.copyCode('${copyId}')" title="复制代码">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                    <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1"></path>
                                </svg>
                            </button>
                        </div>
                        <pre><code${attrs} id="${copyId}">${code}</code></pre>
                    </div>
                `;
            }
        );
    }

    static copyCode(elementId) {
        const codeElement = document.getElementById(elementId);
        if (codeElement) {
            const text = codeElement.textContent;
            
            // 尝试使用现代API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    this.showCopyFeedback(elementId);
                }).catch(err => {
                    console.error('复制失败:', err);
                    this.fallbackCopyText(text);
                });
            } else {
                this.fallbackCopyText(text);
            }
        }
    }

    static fallbackCopyText(text) {
        // 降级复制方法
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            console.log('代码已复制到剪贴板');
        } catch (err) {
            console.error('复制失败:', err);
        }
        
        document.body.removeChild(textArea);
    }

    static showCopyFeedback(elementId) {
        const button = document.querySelector(`button[onclick="MarkdownRenderer.copyCode('${elementId}')"]`);
        if (button) {
            const originalTitle = button.title;
            button.title = '已复制!';
            button.style.color = '#28a745';
            
            setTimeout(() => {
                button.title = originalTitle;
                button.style.color = '';
            }, 2000);
        }
    }
}

// 初始化Markdown渲染器
document.addEventListener('DOMContentLoaded', () => {
    MarkdownRenderer.initialize();
});

// 添加代码块样式
const style = document.createElement('style');
style.textContent = `
    .code-block-container {
        position: relative;
        margin: 8px 0;
    }
    
    .code-block-header {
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 1;
    }
    
    .copy-code-btn {
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 4px 6px;
        cursor: pointer;
        font-size: 12px;
        color: #666;
        transition: all 0.2s;
    }
    
    .copy-code-btn:hover {
        background: rgba(255, 255, 255, 0.95);
        color: #333;
    }
    
    .code-block-container pre {
        margin: 0;
        padding-top: 32px;
    }
`;
document.head.appendChild(style);
