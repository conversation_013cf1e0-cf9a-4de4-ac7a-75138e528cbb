<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 统一蓝色渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6c5ce7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a29bfe;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="32" height="32" rx="8" fill="url(#bgGradient)"/>

  <!-- 区域多边形 -->
  <path d="M4 8 L27 6 L29 16 L23 25 L8 27 L3 18 Z" fill="#ffffff" opacity="0.9"/>

  <!-- 图表区域 -->
  <rect x="8" y="10" width="16" height="12" rx="2" fill="#ffffff" opacity="0.9"/>

  <!-- 柱状图 -->
  <rect x="10" y="16" width="3" height="4" rx="1" fill="#fdcb6e"/>
  <rect x="14" y="14" width="3" height="6" rx="1" fill="#e17055"/>
  <rect x="18" y="17" width="3" height="3" rx="1" fill="#74b9ff"/>

  <!-- 眼睛图标 -->
  <ellipse cx="16" cy="4" rx="4" ry="2" fill="#ffffff" opacity="0.9"/>
  <circle cx="16" cy="4" r="1.5" fill="#6c5ce7"/>
  <circle cx="16" cy="4" r="0.5" fill="#ffffff"/>

  <!-- 面积数值 -->
  <text x="16" y="26" fill="#ffffff" font-family="SF Pro, Arial, sans-serif" font-size="6" font-weight="bold" text-anchor="middle">1234m²</text>
</svg>