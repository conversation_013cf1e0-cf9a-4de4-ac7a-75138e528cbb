# XIAOFU工具箱 - DockPane通用风格规范

## 概述

本文档定义了XIAOFU工具箱中所有DockPane停靠窗格的统一设计规范和实现标准，确保所有工具具有一致的用户体验和专业的界面风格。

## DockPane架构规范

### 1. 文件结构

每个DockPane工具应包含以下文件：
- **XxxxButton.cs** - 工具按钮类，继承自Button
- **XxxxDockPane.cs** - DockPane主类，继承自DockPane
- **XxxxDockPaneView.xaml** - DockPane界面布局
- **XxxxDockPaneView.xaml.cs** - DockPane界面代码后台
- **XxxxViewModel.cs** - 视图模型，包含业务逻辑

### 2. 命名规范

- DockPane类：`{工具名}DockPane`
- DockPane视图：`{工具名}DockPaneView`
- DockPane ID：`XIAOFUTools_{工具名}DockPane`
- 按钮类：`{工具名}Button`
- 视图模型：`{工具名}ViewModel`

## DockPane实现规范

### 1. DockPane主类实现

```csharp
internal class XxxxDockPane : DockPane
{
    private const string _dockPaneID = "XIAOFUTools_XxxxDockPane";

    protected XxxxDockPane() { }

    /// <summary>
    /// 创建停靠窗格内容
    /// </summary>
    protected override System.Windows.Controls.Control OnCreateContent()
    {
        return new XxxxDockPaneView();
    }

    /// <summary>
    /// 显示停靠窗格
    /// </summary>
    internal static void Show()
    {
        DockPane pane = FrameworkApplication.DockPaneManager.Find(_dockPaneID);
        pane?.Activate();
    }
}
```

### 2. 按钮类实现

```csharp
internal class XxxxButton : Button
{
    protected override void OnClick()
    {
        // 打开停靠窗格
        XxxxDockPane.Show();
    }
}
```

### 3. Config.daml配置

```xml
<dockPanes>
  <dockPane id="XIAOFUTools_XxxxDockPane"
            caption="工具标题"
            className="XIAOFUTools.Tools.Xxx.XxxxDockPane"
            dock="group"
            dockWith="esri_core_projectDockPane">
  </dockPane>
</dockPanes>
```

## 界面设计规范

### 1. 布局结构

```xml
<UserControl Loaded="UserControl_Loaded">
    <Border Background="{StaticResource BackgroundBrush}" 
            BorderBrush="#CDCDCD" BorderThickness="1" CornerRadius="4">
        <Grid Margin="12">
            <Grid.RowDefinitions>
                <!-- 参数输入区域 -->
                <RowDefinition Height="Auto"/>
                <!-- 进度条 -->
                <RowDefinition Height="Auto"/>
                <!-- 日志区域（动态高度） -->
                <RowDefinition Height="*"/>
                <!-- 按钮区域 -->
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
        </Grid>
    </Border>
</UserControl>
```

### 2. 按钮区域设计

```xml
<!-- 按钮区域 -->
<Border BorderBrush="{StaticResource DividerBrush}" 
        BorderThickness="0,1,0,0" 
        Margin="0,5,0,0" 
        Padding="0,10,0,0">
    <Grid>
        <!-- 帮助按钮（左侧） -->
        <Button Content="?" Width="22" Height="22"
                Style="{StaticResource HelpButtonStyle}"
                Command="{Binding ShowHelpCommand}"
                ToolTip="查看工具使用说明"
                HorizontalAlignment="Left"/>
        
        <!-- 功能按钮（右侧） -->
        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
            <!-- 只在处理时显示停止按钮 -->
            <Button Content="停止" Width="80" Command="{Binding CancelCommand}" 
                    Style="{StaticResource CancelButtonStyle}"
                    Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}"
                    IsEnabled="{Binding IsProcessing}" Margin="0,0,10,0"/>
            <Button Content="运行" Width="80" Command="{Binding RunCommand}"
                    Style="{StaticResource ExecuteButtonStyle}"
                    IsEnabled="{Binding CanProcess}"/>
        </StackPanel>
    </Grid>
</Border>
```

### 3. 日志区域设计

```xml
<!-- 日志窗口（动态高度） -->
<Border BorderBrush="#CDCDCD" BorderThickness="1" Margin="0,0,0,10">
    <TextBox Style="{StaticResource LogTextBoxStyle}"
             Text="{Binding LogContent, Mode=OneWay}"
             BorderThickness="0"
             VerticalAlignment="Stretch"
             HorizontalAlignment="Stretch"/>
</Border>
```

## ViewModel规范

### 1. 基础属性

所有DockPane的ViewModel应包含以下基础属性：

```csharp
// 处理状态
public bool IsProcessing { get; set; }
public bool CanProcess => !IsProcessing;
public bool CancelRequested { get; set; }

// 界面状态
public string StatusMessage { get; set; }
public string LogContent { get; set; }
public int Progress { get; set; }
public bool IsProgressIndeterminate { get; set; }
```

### 2. 基础命令

```csharp
// 停止命令（只在处理时可用）
public ICommand CancelCommand { get; }

// 运行命令
public ICommand RunCommand { get; }

// 帮助命令
public ICommand ShowHelpCommand { get; }
```

### 3. 初始化方法

```csharp
public void RefreshLayers()
{
    LoadFeatureLayers();
}
```

## 用户体验规范

### 1. 按钮行为

- **无关闭按钮**：用户通过DockPane右上角X关闭，不提供额外关闭按钮
- **动态停止按钮**：只在处理过程中显示"停止"按钮
- **帮助按钮**：左下角提供"?"帮助按钮，显示工具使用说明

### 2. 界面响应

- **非阻塞设计**：DockPane不阻塞地图操作
- **动态高度**：日志区域根据内容动态调整高度
- **实时反馈**：处理过程中提供进度条和状态信息

### 3. 数据加载

- **延迟加载**：在UserControl_Loaded事件中刷新图层数据
- **自动刷新**：每次显示DockPane时自动刷新图层列表

## 样式规范

### 1. 使用统一样式

- 所有控件使用ControlStyles.xaml中定义的样式
- 保持与其他工具的视觉一致性
- 使用标准的颜色和字体规范

### 2. 布局规范

- 外边距：12px
- 控件间距：10px
- **无需设置背景色**：DockPane自动继承ArcGIS Pro主题样式
- **无需Border容器**：直接使用Grid作为根容器即可
- 内部边框颜色：#CDCDCD（仅用于日志窗口等内部组件）

## 最佳实践

### 1. 性能优化

- 使用QueuedTask.Run执行耗时操作
- 在UI线程更新界面元素
- 及时释放资源

### 2. 错误处理

- 提供详细的错误信息
- 在日志中记录操作过程
- 优雅处理异常情况

### 3. 用户友好

- 提供清晰的状态反馈
- 支持操作取消
- 提供详细的帮助信息

## 高级功能实现

### 1. 异步操作模式

```csharp
private async Task ProcessDataAsync()
{
    try
    {
        IsProcessing = true;
        StatusMessage = "正在处理数据...";
        
        await QueuedTask.Run(() =>
        {
            // GIS操作必须在QueuedTask中执行
            var map = MapView.Active.Map;
            // 处理逻辑...
        });
        
        StatusMessage = "处理完成";
    }
    catch (Exception ex)
    {
        LogError($"处理失败: {ex.Message}");
    }
    finally
    {
        IsProcessing = false;
    }
}
```

### 2. 进度报告机制

```csharp
private async Task ProcessWithProgress(List<Layer> layers)
{
    var totalCount = layers.Count;
    for (int i = 0; i < totalCount; i++)
    {
        if (CancelRequested) break;
        
        // 更新进度
        Progress = (int)((double)(i + 1) / totalCount * 100);
        StatusMessage = $"正在处理第 {i + 1}/{totalCount} 个图层";
        
        // 处理单个图层
        await ProcessSingleLayer(layers[i]);
    }
}
```

### 3. 取消操作支持

```csharp
private CancellationTokenSource _cancellationTokenSource;

public ICommand CancelCommand => new RelayCommand(() =>
{
    CancelRequested = true;
    _cancellationTokenSource?.Cancel();
    StatusMessage = "操作已取消";
});
```

## 数据绑定最佳实践

### 1. 图层数据绑定

```csharp
public ObservableCollection<Layer> FeatureLayers { get; set; }
    = new ObservableCollection<Layer>();

private void LoadFeatureLayers()
{
    FeatureLayers.Clear();
    
    var map = MapView.Active?.Map;
    if (map == null) return;
    
    var layers = map.GetLayersAsFlattenedList()
                   .OfType<FeatureLayer>()
                   .Where(layer => layer.GetFeatureClass() != null);
    
    foreach (var layer in layers)
    {
        FeatureLayers.Add(layer);
    }
}
```

### 2. 字段数据绑定

```csharp
public ObservableCollection<string> AvailableFields { get; set; }
    = new ObservableCollection<string>();

private void LoadFields(FeatureLayer layer)
{
    AvailableFields.Clear();
    
    if (layer?.GetFeatureClass() == null) return;
    
    var fieldDefinitions = layer.GetFeatureClass().GetDefinition().GetFields();
    foreach (var field in fieldDefinitions)
    {
        if (field.FieldType == FieldType.String ||
            field.FieldType == FieldType.Integer ||
            field.FieldType == FieldType.Double)
        {
            AvailableFields.Add(field.Name);
        }
    }
}
```

## 错误处理和日志记录

### 1. 结构化日志记录

```csharp
public void LogInfo(string message)
{
    var timestamp = DateTime.Now.ToString("HH:mm:ss");
    LogContent += $"[{timestamp}] {message}\n";
    ScrollToBottom();
}

public void LogError(string message)
{
    var timestamp = DateTime.Now.ToString("HH:mm:ss");
    LogContent += $"[{timestamp}] 错误: {message}\n";
    ScrollToBottom();
}

public void LogWarning(string message)
{
    var timestamp = DateTime.Now.ToString("HH:mm:ss");
    LogContent += $"[{timestamp}] 警告: {message}\n";
    ScrollToBottom();
}
```

### 2. 异常处理策略

```csharp
try
{
    await QueuedTask.Run(() =>
    {
        // GIS操作
    });
}
catch (InvalidOperationException ex)
{
    LogError($"操作无效: {ex.Message}");
    MessageBox.Show("请检查输入参数是否正确", "操作错误");
}
catch (UnauthorizedAccessException ex)
{
    LogError($"访问被拒绝: {ex.Message}");
    MessageBox.Show("请检查文件权限", "权限错误");
}
catch (Exception ex)
{
    LogError($"未知错误: {ex.Message}");
    MessageBox.Show($"操作失败: {ex.Message}", "错误");
}
```

## 性能优化指南

### 1. 内存管理

```csharp
// 正确的资源释放
using (var geodatabase = new Geodatabase(connectionPath))
using (var featureClass = geodatabase.OpenDataset<FeatureClass>("LayerName"))
{
    // 使用资源
} // 自动释放
```

### 2. 批量操作优化

```csharp
// 使用编辑操作批量处理
var editOperation = new EditOperation()
{
    Name = "批量更新要素"
};

foreach (var feature in features)
{
    editOperation.Modify(feature, attributes);
}

var result = await editOperation.ExecuteAsync();
if (!result)
{
    LogError($"批量操作失败: {editOperation.ErrorMessage}");
}
```

### 3. UI响应性优化

```csharp
// 使用Dispatcher确保UI更新在主线程
Application.Current.Dispatcher.Invoke(() =>
{
    StatusMessage = "更新状态";
    Progress = newProgress;
});
```

## 测试和调试

### 1. 单元测试示例

```csharp
[TestMethod]
public void TestLayerLoading()
{
    var viewModel = new TestDockPaneViewModel();
    viewModel.RefreshLayers();
    
    Assert.IsTrue(viewModel.FeatureLayers.Count >= 0);
}
```

### 2. 调试技巧

```csharp
// 使用条件编译进行调试输出
#if DEBUG
System.Diagnostics.Debug.WriteLine($"处理图层: {layer.Name}");
#endif

// 使用日志记录关键信息
LogInfo($"开始处理 {layers.Count} 个图层");
```

## 国际化支持

### 1. 文本资源化

```csharp
// 使用资源文件存储文本
public string ProcessingMessage => Properties.Resources.ProcessingMessage;
public string ErrorMessage => Properties.Resources.ErrorMessage;
```

### 2. 日期时间格式化

```csharp
// 使用当前文化信息格式化
var timestamp = DateTime.Now.ToString("G", CultureInfo.CurrentCulture);
```

## 示例参考

### 完整实现示例
以下工具已按此规范完整实现，可作为开发参考：
1. **BatchLayerClip** - 按字段批量裁剪要素图层（复杂参数处理）
2. **BoundaryPointGenerator** - 生成四至坐标点（几何计算）
3. **BatchAddData** - 批量添加数据（文件系统操作）
4. **SpecialCoordinateTransform** - 特殊坐标转换（数据转换）
5. **AIAssistant** - AI助手（Web集成）

### 开发检查清单
在完成DockPane开发后，请确保：
- [ ] 实现了标准的DockPane架构
- [ ] 使用了统一的样式和布局
- [ ] 添加了适当的错误处理
- [ ] 实现了异步操作模式
- [ ] 支持操作取消功能
- [ ] 提供了详细的日志记录
- [ ] 通过了性能测试
- [ ] 验证了内存使用情况
- [ ] 测试了异常场景处理
- [ ] 确保了线程安全性

## 代码质量保证

### 1. 代码审查要点
- 是否正确使用了QueuedTask
- 是否有内存泄漏风险
- 异常处理是否完善
- UI更新是否在主线程

### 2. 性能测试
- 大数据量处理测试
- 内存使用监控
- UI响应性测试
- 并发操作测试

在开发新的DockPane工具时，应严格遵循本规范，确保整个插件的一致性和专业性。
