using System;
using Newtonsoft.Json;

namespace XIAOFUTools.Tools.AIAssistant.Models
{
    /// <summary>
    /// OpenAI函数调用模型
    /// 表示AI发起的函数调用请求
    /// </summary>
    public class FunctionCall
    {
        /// <summary>
        /// 函数名称
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// 函数参数（JSON字符串）
        /// </summary>
        [JsonProperty("arguments")]
        public string Arguments { get; set; }

        /// <summary>
        /// 调用ID（用于跟踪）
        /// </summary>
        [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
        public string Id { get; set; }

        public FunctionCall()
        {
            Id = Guid.NewGuid().ToString();
        }

        /// <summary>
        /// 创建函数调用
        /// </summary>
        /// <param name="name">函数名称</param>
        /// <param name="arguments">函数参数</param>
        /// <param name="id">调用ID</param>
        /// <returns>函数调用</returns>
        public static FunctionCall Create(string name, string arguments, string id = null)
        {
            return new FunctionCall
            {
                Name = name,
                Arguments = arguments,
                Id = id ?? Guid.NewGuid().ToString()
            };
        }

        /// <summary>
        /// 解析参数为指定类型
        /// </summary>
        /// <typeparam name="T">参数类型</typeparam>
        /// <returns>解析后的参数对象</returns>
        public T ParseArguments<T>() where T : class
        {
            if (string.IsNullOrWhiteSpace(Arguments))
                return null;

            try
            {
                return JsonConvert.DeserializeObject<T>(Arguments);
            }
            catch (JsonException ex)
            {
                throw new ArgumentException($"无法解析函数参数: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取参数的字符串表示
        /// </summary>
        /// <returns>格式化的参数字符串</returns>
        public override string ToString()
        {
            return $"FunctionCall: {Name}({Arguments})";
        }
    }

    /// <summary>
    /// 工具调用模型（OpenAI新格式）
    /// </summary>
    public class ToolCall
    {
        /// <summary>
        /// 调用ID
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        /// 调用类型（通常为"function"）
        /// </summary>
        [JsonProperty("type")]
        public string Type { get; set; } = "function";

        /// <summary>
        /// 函数调用详情
        /// </summary>
        [JsonProperty("function")]
        public FunctionCall Function { get; set; }

        public ToolCall()
        {
            Id = Guid.NewGuid().ToString();
        }

        /// <summary>
        /// 创建工具调用
        /// </summary>
        /// <param name="functionCall">函数调用</param>
        /// <param name="id">调用ID</param>
        /// <returns>工具调用</returns>
        public static ToolCall Create(FunctionCall functionCall, string id = null)
        {
            return new ToolCall
            {
                Id = id ?? Guid.NewGuid().ToString(),
                Function = functionCall
            };
        }
    }
}
