# 当前右键菜单配置

## 📋 已实现的右键菜单功能

### 1. 地图选择上下文菜单 ✅
**菜单ID**: `esri_mapping_selection2DContextMenu`
**触发条件**: 在选中的要素上右键点击
**添加的功能**:
- 📐 **查看面积** - 在"复制"按钮上方，带分隔线
- 🎨 **添加预设图层** - 紧跟在查看面积下方

```xml
<updateMenu refID="esri_mapping_selection2DContextMenu">
  <insertButton refID="XIAOFUTools_ViewAreaButton" insert="before" placeWith="esri_core_editCopyButton" separator="true"/>
  <insertGallery refID="XIAOFUTools_PresetLayersGallery" insert="before" placeWith="esri_core_editCopyButton" separator="false"/>
</updateMenu>
```

**菜单效果**:
```
┌─────────────────────────────┐
│ 📐 查看面积                 │ ← 新增
│ 🎨 添加预设图层             │ ← 新增
├─────────────────────────────┤
│ 📋 复制                     │
│ ✂️ 剪切                     │
│ 📝 编辑要素                 │
│ ⚙️ 属性                     │
└─────────────────────────────┘
```

### 2. 地图框右键菜单 ✅
**菜单ID**: `esri_mapping_mapContextMenu`
**触发条件**: 在地图列表中的地图上右键点击
**添加的功能**:
- 🎨 **添加预设图层** - 在"添加数据"按钮上方，带分隔线
- 📁 **批量添加数据** - 紧跟在预设图层下方

```xml
<updateMenu refID="esri_mapping_mapContextMenu">
  <insertGallery refID="XIAOFUTools_PresetLayersGallery" insert="before" placeWith="esri_mapping_addDataButton" separator="true"/>
  <insertButton refID="XIAOFUTools_BatchAddDataButton" insert="before" placeWith="esri_mapping_addDataButton" separator="false"/>
</updateMenu>
```

**菜单效果**:
```
┌─────────────────────────────┐
│ 🎨 添加预设图层             │ ← 新增
│ 📁 批量添加数据             │ ← 新增
├─────────────────────────────┤
│ ➕ 添加数据                 │
│ 📋 粘贴图层                 │
│ 🗑️ 移除                    │
│ ⚙️ 属性                     │
└─────────────────────────────┘
```

### 3. 地图视图右键菜单 ✅
**菜单ID**: `esri_mapping_popupToolContextMenu`
**触发条件**: 在地图视图空白区域右键点击
**添加的功能**:
- 🎨 **添加预设图层** - 在"粘贴"按钮上方，带分隔线
- 📁 **批量添加数据** - 紧跟在预设图层下方

```xml
<updateMenu refID="esri_mapping_popupToolContextMenu">
  <insertGallery refID="XIAOFUTools_PresetLayersGallery" insert="before" placeWith="esri_core_editPasteButton" separator="true"/>
  <insertButton refID="XIAOFUTools_BatchAddDataButton" insert="before" placeWith="esri_core_editPasteButton" separator="false"/>
</updateMenu>
```

**菜单效果**:
```
┌─────────────────────────────┐
│ 🎨 添加预设图层             │ ← 新增
│ 📁 批量添加数据             │ ← 新增
├─────────────────────────────┤
│ 📋 粘贴                     │
│ 🔍 缩放至全图               │
│ 📏 测量                     │
│ ⚙️ 地图属性                 │
└─────────────────────────────┘
```

### 4. 图层右键菜单 ✅
**菜单ID**: `esri_mapping_layerContextMenu`
**触发条件**: 在图层列表中的图层上右键点击
**添加的功能**:
- 📐 **计算面积** - 在"打开属性表"按钮上方，带分隔线

```xml
<updateMenu refID="esri_mapping_layerContextMenu">
  <insertButton refID="XIAOFUTools_AreaCalculatorButton" insert="before" placeWith="esri_editing_table_openTablePaneButton" separator="true"/>
</updateMenu>
```

**菜单效果**:
```
┌─────────────────────────────┐
│ 📐 计算面积                 │ ← 新增
├─────────────────────────────┤
│ 📊 打开属性表               │
│ 🎨 符号系统                 │
│ 🏷️ 标注                     │
│ ⚙️ 属性                     │
└─────────────────────────────┘
```

### 5. 未注册图层右键菜单 ✅
**菜单ID**: `esri_mapping_unregisteredLayerContextMenu`
**触发条件**: 在SHP等未注册图层上右键点击
**添加的功能**:
- 📐 **计算面积** - 在"打开属性表"按钮上方，带分隔线

```xml
<updateMenu refID="esri_mapping_unregisteredLayerContextMenu">
  <insertButton refID="XIAOFUTools_AreaCalculatorButton" insert="before" placeWith="esri_editing_table_openTablePaneButton" separator="true"/>
</updateMenu>
```

**菜单效果**:
```
┌─────────────────────────────┐
│ 📐 计算面积                 │ ← 新增
├─────────────────────────────┤
│ 📊 打开属性表               │
│ 🎨 符号系统                 │
│ 🏷️ 标注                     │
│ ⚙️ 属性                     │
└─────────────────────────────┘
```

## 🎯 使用场景

### 查看面积工具
1. **选择要素** - 在地图中选择面要素或线要素
2. **右键点击** - 在选中的要素上右键
3. **点击查看面积** - 快速打开面积计算工具
4. **查看结果** - 立即显示面积和长度信息

### 计算面积工具
**方式1 - 图层右键**:
1. **右键图层** - 在内容窗格的图层名称上右键
2. **点击计算面积** - 快速打开计算面积工具
3. **配置参数** - 选择目标字段、单位和计算方式
4. **执行计算** - 批量计算图层中所有要素的面积

**方式2 - SHP图层右键**:
1. **右键SHP图层** - 在内容窗格的Shapefile图层上右键
2. **点击计算面积** - 快速打开计算面积工具
3. **配置参数** - 选择目标字段、单位和计算方式
4. **执行计算** - 批量计算图层中所有要素的面积

### 添加预设图层工具
**方式1 - 地图框右键**:
1. **右键地图** - 在内容窗格的地图名称上右键
2. **点击添加预设图层** - 打开预设图层图库
3. **选择图层** - 从预设图层库中选择需要的图层
4. **快速添加** - 一键添加预设图层到地图

**方式2 - 地图视图右键**:
1. **右键地图视图** - 在地图视图的空白区域右键
2. **点击添加预设图层** - 打开预设图层图库
3. **选择图层** - 从预设图层库中选择需要的图层
4. **快速添加** - 一键添加预设图层到地图

### 批量添加数据工具
**方式1 - 地图框右键**:
1. **右键地图** - 在内容窗格的地图名称上右键
2. **点击批量添加数据** - 快速打开批量添加工具
3. **选择文件夹** - 扫描并添加GIS数据文件
4. **批量导入** - 一次性添加多个数据文件

**方式2 - 地图视图右键**:
1. **右键地图视图** - 在地图视图的空白区域右键
2. **点击批量添加数据** - 快速打开批量添加工具
3. **选择文件夹** - 扫描并添加GIS数据文件
4. **批量导入** - 一次性添加多个数据文件

## 📋 预留的菜单扩展

以下菜单已预留，可以添加更多功能：

| 菜单类型 | 菜单ID | 状态 |
|---------|--------|------|
| 图层右键菜单 | `esri_mapping_layerContextMenu` | ✅ 已使用 |
| 未注册图层菜单 | `esri_mapping_unregisteredLayerContextMenu` | ✅ 已使用 |
| 图层组菜单 | `esri_mapping_groupLayerContextMenu` | 🔲 预留 |
| 独立表菜单 | `esri_mapping_standaloneTableContextMenu` | 🔲 预留 |
| 栅格图层菜单 | `esri_mapping_rasterLayerContextMenu` | 🔲 预留 |
| 要素数据菜单 | `esri_editing_data` | 🔲 预留 |
| 独立表数据菜单 | `esri_editing_standalonetable_data_menu` | 🔲 预留 |

## 🔧 添加新菜单项的模板

### 基本模板
```xml
<updateMenu refID="菜单ID">
  <insertButton refID="你的按钮ID" insert="before" placeWith="参考按钮ID" separator="true"/>
</updateMenu>
```

### 常用参考按钮
- `esri_core_editCopyButton` - 复制按钮
- `esri_mapping_addDataButton` - 添加数据按钮
- `esri_editing_table_openTablePaneButton` - 打开属性表按钮
- `esri_mapping_selectedLayerSymbologyButton` - 符号系统按钮

## 📝 注意事项

1. **按钮定义**: 必须先在`<controls>`节点中定义按钮
2. **重启测试**: 修改后需要重启ArcGIS Pro进行测试
3. **位置选择**: 选择合适的参考按钮确保菜单项显示在正确位置
4. **分隔线**: 使用`separator="true"`与其他菜单项区分

---

*最后更新: 2024年*  
*当前版本: v1.0*
